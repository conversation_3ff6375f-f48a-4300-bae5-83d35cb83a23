// truck-hydraulic-system-fields.js
// This file defines the Truck Hydraulic System Configurator component attributes as JS objects for dynamic form rendering
export const componentAttributes = [
    { Name: "Application", DataType: "String", Caption: "Application", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Application", DisplayType: "TypeableDropDown" },
    { Name: "Year", DataType: "String", Caption: "Year", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Years", DisplayType: "TypeableDropDown" },
    { Name: "Vehicle_Type", DataType: "String", Caption: "Vehicle Type", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Vehicle_Type", DisplayType: "TypeableDropDown" },
    { Name: "Make", DataType: "String", Caption: "Make", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Make", DisplayType: "TypeableDropDown" },
    { Name: "Engine", DataType: "String", Caption: "Engine", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Engine", DisplayType: "TypeableDropDown" },
    { Name: "Pump", DataType: "String", Caption: "Pump", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Pump", DisplayType: "TypeableDropDown" },
    { Name: "Kit", DataType: "String", Caption: "Kit", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Kit", DisplayType: "TypeableDropDown" },
    { Name: "Clutch", DataType: "String", Caption: "Clutch", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Clutch", DisplayType: "TypeableDropDown" },
    { Name: "Adapter", DataType: "String", Caption: "Adapter", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Adapter", DisplayType: "TypeableDropDown" },
    { Name: "Product", DataType: "String", Caption: "Product", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Product", DisplayType: "TypeableDropDown" },
    { Name: "Cab", DataType: "String", Caption: "Cab", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Cab", DisplayType: "RadioButtonVertical" },
    { Name: "Chassis", DataType: "String", Caption: "Chassis", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Chassis", DisplayType: "RadioButtonVertical" },
    { Name: "Reservoir", DataType: "Boolean", Caption: "Reservoir", IsLocked: false, IsVisible: true, IsRequired: true, DisplayType: "RadioButtonVertical" },
    { Name: "Hoses660", DataType: "Boolean", Caption: "Hoses660", IsLocked: false, IsVisible: true, IsRequired: true, DisplayType: "RadioButtonVertical" },
    { Name: "Valve", DataType: "String", Caption: "Valve", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Valve", DisplayType: "TypeableDropDown" },
    { Name: "ACC_Hose", DataType: "Boolean", Caption: "ACC Hose", IsLocked: false, IsVisible: true, IsRequired: true, DisplayType: "RadioButtonVertical" },
    { Name: "Aux_Hoses", DataType: "String", Caption: "Aux Hoses", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Aux_Hoses", DisplayType: "TypeableDropDown" },
    { Name: "Control", DataType: "String", Caption: "Control", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Control", DisplayType: "TypeableDropDown" },
    { Name: "Harness", DataType: "String", Caption: "Harness", IsLocked: false, IsVisible: true, IsRequired: true, DisplayType: "TextBox" },
    { Name: "AGKitLessMiniPack", DataType: "Boolean", Caption: "AGKitLessMiniPack", IsLocked: false, IsVisible: true, IsRequired: true, DisplayType: "CheckBox" },
    { Name: "ValveYN", DataType: "Boolean", Caption: "ValveYN", IsLocked: false, IsVisible: true, IsRequired: true, DisplayType: "CheckBox" },
    { Name: "Macfit", DataType: "Boolean", Caption: "Macfit", IsLocked: false, IsVisible: true, IsRequired: true, DisplayType: "RadioButtonHorizontal" }
];
