<Matrix Name="NoEngine" Description="Check to see if 1 1/2 ton trucks exists." DataType="Boolean">
  <Dimension Name="Make =" DataType="String" Description="" AccessMethod="=" ValueExpression="=Make" />
  <Dimension Name="Year &lt;" DataType="String" Description="" AccessMethod="&gt;" ValueExpression="=Year" />
  <Dimension Name="Vehicle_Type =" DataType="String" Description="" AccessMethod="=" ValueExpression="=Vehicle_Type" />
  <DefaultValue></DefaultValue>
  <Value>
    <Lookup>CT</Lookup>
    <Lookup>1997</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>CT</Lookup>
    <Lookup>2010</Lookup>
    <Lookup>OT</Lookup>
    <Value>False</Value>
  </Value>
  <Value>
    <Lookup>CT</Lookup>
    <Lookup>3000</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>DG</Lookup>
    <Lookup>2007</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>DG</Lookup>
    <Lookup>2018</Lookup>
    <Lookup>OT</Lookup>
    <Value>False</Value>
  </Value>
  <Value>
    <Lookup>DG</Lookup>
    <Lookup>3000</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>FD</Lookup>
    <Lookup>1983</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>FD</Lookup>
    <Lookup>1988</Lookup>
    <Lookup>VN</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>FD</Lookup>
    <Lookup>1992</Lookup>
    <Lookup>OT</Lookup>
    <Value>False</Value>
  </Value>
  <Value>
    <Lookup>FD</Lookup>
    <Lookup>1995</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>FD</Lookup>
    <Lookup>1996</Lookup>
    <Lookup>OT</Lookup>
    <Value>False</Value>
  </Value>
  <Value>
    <Lookup>FD</Lookup>
    <Lookup>2015</Lookup>
    <Lookup>OT</Lookup>
    <Value>False</Value>
  </Value>
  <Value>
    <Lookup>FD</Lookup>
    <Lookup>2018</Lookup>
    <Lookup>VN</Lookup>
    <Value>False</Value>
  </Value>
  <Value>
    <Lookup>FD</Lookup>
    <Lookup>3000</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>FD</Lookup>
    <Lookup>3000</Lookup>
    <Lookup>VN</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>FL</Lookup>
    <Lookup>1999</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>FL</Lookup>
    <Lookup>2000</Lookup>
    <Lookup>OT</Lookup>
    <Value>False</Value>
  </Value>
  <Value>
    <Lookup>FL</Lookup>
    <Lookup>2002</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>FL</Lookup>
    <Lookup>2008</Lookup>
    <Lookup>OT</Lookup>
    <Value>False</Value>
  </Value>
  <Value>
    <Lookup>FL</Lookup>
    <Lookup>3000</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>GM</Lookup>
    <Lookup>1986</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>GM</Lookup>
    <Lookup>1987</Lookup>
    <Lookup>OT</Lookup>
    <Value>False</Value>
  </Value>
  <Value>
    <Lookup>GM</Lookup>
    <Lookup>1987</Lookup>
    <Lookup>VN</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>GM</Lookup>
    <Lookup>2003</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>GM</Lookup>
    <Lookup>2015</Lookup>
    <Lookup>OT</Lookup>
    <Value>False</Value>
  </Value>
  <Value>
    <Lookup>GM</Lookup>
    <Lookup>2015</Lookup>
    <Lookup>PU</Lookup>
    <Value>False</Value>
  </Value>
  <Value>
    <Lookup>GM</Lookup>
    <Lookup>2015</Lookup>
    <Lookup>VN</Lookup>
    <Value>False</Value>
  </Value>
  <Value>
    <Lookup>GM</Lookup>
    <Lookup>3000</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>GM</Lookup>
    <Lookup>3000</Lookup>
    <Lookup>PU</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>GM</Lookup>
    <Lookup>3000</Lookup>
    <Lookup>VN</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>HN</Lookup>
    <Lookup>2008</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>HN</Lookup>
    <Lookup>2011</Lookup>
    <Lookup>OT</Lookup>
    <Value>False</Value>
  </Value>
  <Value>
    <Lookup>HN</Lookup>
    <Lookup>3000</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>IN</Lookup>
    <Lookup>1996</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>IN</Lookup>
    <Lookup>2015</Lookup>
    <Lookup>OT</Lookup>
    <Value>False</Value>
  </Value>
  <Value>
    <Lookup>IN</Lookup>
    <Lookup>3000</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>IZ</Lookup>
    <Lookup>1997</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>IZ</Lookup>
    <Lookup>2013</Lookup>
    <Lookup>OT</Lookup>
    <Value>False</Value>
  </Value>
  <Value>
    <Lookup>IZ</Lookup>
    <Lookup>3000</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>KW</Lookup>
    <Lookup>2009</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>KW</Lookup>
    <Lookup>2015</Lookup>
    <Lookup>OT</Lookup>
    <Value>False</Value>
  </Value>
  <Value>
    <Lookup>KW</Lookup>
    <Lookup>3000</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>MT</Lookup>
    <Lookup>2000</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>MT</Lookup>
    <Lookup>2008</Lookup>
    <Lookup>OT</Lookup>
    <Value>False</Value>
  </Value>
  <Value>
    <Lookup>MT</Lookup>
    <Lookup>3000</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>NS</Lookup>
    <Lookup>2008</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>NS</Lookup>
    <Lookup>2010</Lookup>
    <Lookup>OT</Lookup>
    <Value>False</Value>
  </Value>
  <Value>
    <Lookup>NS</Lookup>
    <Lookup>3000</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>SP</Lookup>
    <Lookup>2006</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>SP</Lookup>
    <Lookup>2016</Lookup>
    <Lookup>OT</Lookup>
    <Value>False</Value>
  </Value>
  <Value>
    <Lookup>SP</Lookup>
    <Lookup>3000</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>ST</Lookup>
    <Lookup>2002</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>ST</Lookup>
    <Lookup>2011</Lookup>
    <Lookup>OT</Lookup>
    <Value>False</Value>
  </Value>
  <Value>
    <Lookup>ST</Lookup>
    <Lookup>3000</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>FL</Lookup>
    <Lookup>2011</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>FL</Lookup>
    <Lookup>2013</Lookup>
    <Lookup>OT</Lookup>
    <Value>False</Value>
  </Value>
  <Value>
    <Lookup>FS</Lookup>
    <Lookup>3000</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>PB</Lookup>
    <Lookup>2013</Lookup>
    <Lookup>OT</Lookup>
    <Value>True</Value>
  </Value>
  <Value>
    <Lookup>GM</Lookup>
    <Lookup>2019</Lookup>
    <Lookup>OT</Lookup>
    <Value>TRUE</Value>
  </Value>
  <Value>
    <Lookup>GM</Lookup>
    <Lookup>2020</Lookup>
    <Lookup>OT</Lookup>
    <Value>TRUE</Value>
  </Value>
  <Tags />
</Matrix>