%% Butler Manufacturing Configurator (SYS<PERSON>Butler) - Data Relationships
erDiagram
    %% Core Truck Information
    TRUCK {
        string truck_id_PK
        string make
        string year
        string axle_type
        string cab_axle_config
        boolean is_ford
        boolean know_bed_preference
    }
    %% Butler Configuration
    BUTLER_CONFIGURATION {
        string butler_config_id_PK
        string truck_id_FK
        string customer_code
        decimal total_price
        datetime created_date
        string status
        boolean wireless_control
        boolean power_unit_required
    }
    %% Bed and Bed Features
    BED {
        string bed_id_PK
        string bed_model
        string bed_type
        string bed_feature
        string description
        decimal base_price
    }
    BED_MOUNT {
        string bed_mount_id_PK
        string bed_model_FK
        string make
        string year
        string cab_axle
        string axle_type
        string mount_part_number
    }
    %% Toolbox Components
    TOOLBOX {
        string toolbox_id_PK
        string toolbox_part_number
        string toolbox_type
        string size_description
        decimal base_price
    }
    TOOLBOX_COMPATIBILITY {
        string compatibility_id_PK
        string bed_id_FK
        string toolbox_id_FK
        string position_type
        string left_part_number
        string right_part_number
    }
    %% Hydraulic Components
    HYDRAULIC_POWER_UNIT {
        string hyd_unit_id_PK
        string unit_part_number
        string unit_type
        string description
        decimal base_price
    }
    %% Accessories and Features
    HEADACHE_RACK {
        string headache_id_PK
        string rack_part_number
        string rack_type
        decimal base_price
    }
    WORK_LIGHTS {
        string lights_id_PK
        string lights_part_number
        string light_type
        decimal base_price
    }
    RECEIVER_HITCH {
        string hitch_id_PK
        string hitch_part_number
        string hitch_type
        decimal base_price
    }
    FIFTH_WHEEL_BALL {
        string ball_id_PK
        string ball_part_number
        string ball_type
        decimal base_price
    }
    %% Wire Harness and Adapters
    WIRE_HARNESS {
        string harness_id_PK
        string harness_part_number
        string make_compatibility
        string year_compatibility
        decimal base_price
    }
    BUTLER_ADAPTER {
        string adapter_id_PK
        string adapter_part_number
        string adapter_description
        string make_compatibility
        string year_compatibility
        string cab_axle_compatibility
        string bed_type_compatibility
        decimal base_price
    }
    %% Mount Kits
    MOUNT_KIT {
        string mount_kit_id_PK
        string kit_part_number
        string make
        string year
        string axle_type
        string cab_axle
        string bed_type
        decimal base_price
    }
    %% Configuration Line Items (BOM)
    BUTLER_CONFIG_LINE_ITEM {
        string line_item_id_PK
        string butler_config_id_FK
        string component_type
        string part_number
        integer quantity
        decimal unit_price
        decimal line_total
    }
    %% Compatibility Matrices
    TRUCK_OPTIONS_MATRIX {
        string truck_opt_id_PK
        string make
        string year
        string axle
        string cab_axle
        string bed_type
        string compatible_options
    }
    BUTLER_MOUNT_KIT_MATRIX {
        string butler_mk_id_PK
        string make
        string year
        string axle
        string cab_axle
        string bed_type
        string mount_kit_part_number
    }
    BUTLER_ADAPTER_HARNESS_MATRIX {
        string adapter_harness_id_PK
        string make
        string year
        string axle
        string cab_axle
        string bed_type
        string adapter_harness_parts
    }
    DEWEZE_BED_MOUNT_MATRIX {
        string deweze_mount_id_PK
        string cab_axle
        string axle
        string bed_model
        string make
        string year
        string mount_part_number
    }
    BUTLER_SKIRTED_MATRIX {
        string skirted_id_PK
        string bed_id_FK
        string lh37_part
        string lh40_part
        string lh42_part
        string lh55_part
        string lh56_part
        string lh57_part
        string lh60_part
    }
    %% Relationships
    TRUCK ||--o{ BUTLER_CONFIGURATION : "has"
    BUTLER_CONFIGURATION ||--o{ BUTLER_CONFIG_LINE_ITEM : "contains"
    %% Bed relationships
    BED ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"
    BED ||--o{ BED_MOUNT : "requires"
    BED ||--o{ TOOLBOX_COMPATIBILITY : "supports"
    BED ||--o{ BUTLER_SKIRTED_MATRIX : "has_skirted_options"
    %% Component relationships to configuration
    TOOLBOX ||--o{ TOOLBOX_COMPATIBILITY : "available_for"
    TOOLBOX ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"
    HYDRAULIC_POWER_UNIT ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"
    HEADACHE_RACK ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"
    WORK_LIGHTS ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"
    RECEIVER_HITCH ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"
    FIFTH_WHEEL_BALL ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"
    WIRE_HARNESS ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"
    BUTLER_ADAPTER ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"
    MOUNT_KIT ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"
    %% Matrix relationships for compatibility
    TRUCK ||--o{ TRUCK_OPTIONS_MATRIX : "determines_options"
    TRUCK ||--o{ BUTLER_MOUNT_KIT_MATRIX : "determines_mount_kit"
    TRUCK ||--o{ BUTLER_ADAPTER_HARNESS_MATRIX : "determines_adapter"
    TRUCK ||--o{ DEWEZE_BED_MOUNT_MATRIX : "determines_bed_mount"
    %% Many-to-many relationships
    BUTLER_CONFIGURATION }o--o{ TOOLBOX : "left_hand_toolbox"
    BUTLER_CONFIGURATION }o--o{ TOOLBOX : "right_hand_toolbox"
    BUTLER_CONFIGURATION }o--o{ TOOLBOX : "across_bed_toolbox"