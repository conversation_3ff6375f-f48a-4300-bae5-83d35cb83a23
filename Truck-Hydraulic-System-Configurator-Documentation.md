# Truck Hydraulic System Configurator Documentation

## Overview

This is a product configuration system for truck hydraulic systems (PTO/pump kits) with the identifier `*700CPK`. The system guides users through selecting compatible components based on vehicle specifications and application requirements.

## System Architecture

### Database Configuration

- **Production Database**: `HARPER_APP` (for SalesPortal, SYTELINE_PROD)
- **Development Database**: `PILOTDEV_APP` (for PilotDev)
- **Default Database**: `PILOT_APP` (for other applications)

### Pricing System

- Supports customer-specific pricing codes
- Markup/discount calculations based on FirstPrice variable
- List price vs. discounted price display

## Screen Flow and User Input Fields

### 1. Application Selection Screen

**Field**: `Application`

- **Type**: TypeableDropDown
- **Required**: Yes
- **Options**: Referenced from "Application" option list
- **Caption**: "Truck & System Specifications"

### 2. AG Kit Configuration (Conditional)

**Condition**: Only shown if Application = "AG" **Field**: `AGKitLessMiniPack`

- **Type**: CheckBox
- **Caption**: "Less AG Mini Pack?"
BDC tables poipulated / finish models, but BDC tables populated first - so thay can get to the documentation team and test to make sure it is 100%

### 3. Vehicle Make Selection

**Field**: `Make`

- **Type**: TypeableDropDown
- **Required**: Yes
- **Caption**: "Select Make of Vehicle"
- **Filter Logic**: Option list group filtered by Application type

### 4. Mack Special Condition (Conditional)

**Condition**: Only shown if Make = "MC" (Mack) **Field**: `Macfit`

- **Type**: RadioButtonHorizontal
- **Caption**: "Click info link select yes if kti will fit:"
- **Info Link**: "/bulletin/IB7042201Mack.pdf"
- **Critical**: If false, shows "Will Not Fit" error message

### 5. Vehicle Year Selection

**Field**: `Year`

- **Type**: TypeableDropDown
- **Required**: Yes
- **Caption**: "Enter 4 Digit Truck Year"
- **Filter**: Options filtered by selected Make

### 6. Vehicle Type Selection

**Field**: `Vehicle_Type`

- **Type**: TypeableDropDown
- **Required**: Yes
- **Caption**: "Select Vehicle Type"
- **Filter**: Uses VT_Filter matrix based on Make and Year

### 7. Engine Selection

**Field**: `Engine`

- **Type**: TypeableDropDown
- **Required**: Yes
- **Caption**: "Select Engine"
- **Filter**: Uses MultipleEngineGroups matrix
- **Error Handling**: Shows "No Engine Kit Available" if no options

### 8. Pump Selection

**Field**: `Pump`

- **Type**: TypeableDropDown (using Pump_1T1 option list)
- **Required**: Yes
- **Caption**: "Select Pump Size"
- **Filter**: Uses PumpGroupMatrix based on Engine, Application, Vehicle_Type, Year

### 9. Kit Selection

**Field**: `Kit`

- **Type**: TypeableDropDown
- **Required**: Yes
- **Caption**: "Select Your Kit"
- **Filter**: Uses Kit_Group matrix
- **Error Handling**: Shows "No kit exists. Please contact Harper." if no options

### 10. Clutch Selection

**Field**: `Clutch`

- **Type**: TypeableDropDown
- **Required**: Yes
- **Caption**: "Select Your Clutch"
- **Filter**: Uses Clutch_Group matrix with HD_Clutch exceptions

### 11. Adapter Selection

**Field**: `Adapter`

- **Type**: TypeableDropDown
- **Required**: Yes
- **Caption**: "Select Adapter"
- **Filter**: Uses MultipleAdapterGroups matrix

### 12. AG-Specific Fields (Conditional)

**Condition**: Only shown if Application = "AG"

These fields are handled by an included "AG Products" ruleset:

- Product selection
- Valve configuration (ValveYN checkbox for 660 products)
- Control selection
- Hose configurations
- Auxiliary hose options
- Reservoir options

## Key Matrix Lookups Required

### 1. VT_Filter Matrix

- **Inputs**: Make, Year (as number)
- **Purpose**: Determines available vehicle types
- **Special Logic**: Ford/GM/Sprinter with non-AG applications default to "3" if "ZZ"

### 2. MultipleEngineGroups Matrix

- **Inputs**: Make, Vehicle_Type, Year
- **Purpose**: Determines available engines
- **Output**: Comma-separated engine group codes

### 3. PumpGroupMatrix

- **Inputs**: Engine, Application Type, Vehicle_Type, Year
- **Purpose**: Determines available pump sizes
- **Output**: Comma-separated pump group codes

### 4. Kit_Group Matrix

- **Inputs**: Engine, Year, Pump, Vehicle_Type, Application
- **Purpose**: Determines available kit options
- **Output**: Dash-separated kit group codes

### 5. Clutch_Group Matrix

- **Inputs**: Based on current selections
- **Purpose**: Determines available clutch options
- **Exception Handling**: HD_Clutch matrix can override

### 6. MultipleAdapterGroups Matrix

- **Inputs**: Kit substring, Pump, AG flag
- **Purpose**: Determines available adapters
- **Output**: Comma-separated adapter group codes

## Pricing Matrices

### 1. Pricing_Kit Matrix

- **Input**: Kit selection
- **Output**: Kit base price

### 2. Pricing_Pump Matrix

- **Input**: Pump part number
- **Output**: Pump base price

### 3. Pricing_Clutch Matrix

- **Input**: Clutch selection
- **Output**: Clutch base price

### 4. Pricing_Adapter Matrix

- **Input**: Adapter selection
- **Output**: Adapter base price

## Manufacturing Integration

### BOM Generation

The system automatically generates manufacturing components with:

- **Kit**: Primary kit part number
- **Pump**: Pump_PartNumber from lookup
- **Clutch**: Selected clutch
- **Adapter**: Selected adapter

### AG-Specific BOM Items (Conditional)

- **Valve Hoses**: Different part numbers for 660 vs other products
- **Harness**: Harness_PartNumber
- **Valve**: Valve_PartNumber (if not 660 product or if ValveYN=true for 660)
- **Control**: Control_PartNumber (if selected)
- **Auxiliary Hoses**: Aux_PartNumber (if ACC_Hose=true)
- **Suction/Pressure Hoses**: For 800-900 series products
- **Reservoir**: Res_PartNumber (for 660 products if Reservoir=true)

### Operation Setup

- **Operation 10**: Shipping operation
- **Work Center**: 600
- **Labor Hours**: 0.4 hours

## Special Business Rules

### 1. Kit-Specific Warnings

- **Kit 700455**: "Additional modifications needed" warning
- **Kit 700614**: Ford PPOB alternator warning about 110V outlets

### 2. Application-Specific Logic

- **AG Applications**: Additional product selection screens
- **Commercial/Other/Work Applications**: Different make filtering
- **660 Products**: Special valve and reservoir handling

### 3. Pricing Logic

- **Markup Mode**: Applies percentage increase to base prices
- **Discount Mode**: Applies percentage discount to base prices
- **Customer Code Integration**: Links to customer-specific pricing

## Required Option Lists

1. **Application**: Available applications (AG, COM, OTHER, WRK, etc.)
2. **Make**: Vehicle manufacturers (filtered by application)
3. **Years**: Available years (filtered by make)
4. **Vehicle_Type**: Vehicle types (filtered by VT_Filter results)
5. **Engine**: Engine options (filtered by engine groups)
6. **Pump_1T1**: Pump sizes (filtered by pump groups)
7. **Kit**: Kit options (filtered by kit groups)
8. **Clutch**: Clutch options (filtered by clutch groups)
9. **Adapter**: Adapter options (filtered by adapter groups)

## AG-Specific Option Lists

10. **Product**: AG product types
11. **Valve**: Valve options
12. **Control**: Control options
13. **Aux_Hoses**: Auxiliary hose options
14. **Cab**: Cab configurations
15. **Chassis**: Chassis types

## Integration Requirements

### User Management

- Customer code retrieval (`usr.GetCustCode` or `usr.GetCustCodeDev`)
- First price/discount retrieval (`usr.GetFirstPrice`)

### External Systems

- SyteLine ERP integration for manufacturing
- Sales portal integration
- Development environment support
