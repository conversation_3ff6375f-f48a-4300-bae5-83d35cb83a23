# Harper Configurator ERD Documentation Index

## Overview

This document serves as an index and guide to the Entity Relationship Diagrams (ERDs) for the Harper Configurator project. The project contains two specialized configurators, each with its own data model and business logic.

## Available ERD Documents

### 1. [Truck Hydraulic System Configurator (SYS.700CPK)](./ERD-Truck-Hydraulic-System-Configurator.md)

**Purpose**: General-purpose truck hydraulic system configuration for PTO/pump kits

**Key Features**:
- Vehicle specification-driven configuration
- Hydraulic component selection (kits, pumps, clutches, adapters)
- Agricultural (AG) application support with specialized components
- Complex compatibility matrix filtering
- SyteLine ERP integration for manufacturing

**Core Entities**:
- VEHICLE, CONFIGURATION, CONFIG_LINE_ITEM
- KIT, PUMP, CLUTCH, ADAPTER
- AG_PRODUCT, VALVE, CONTROL, HOSE, HARNESS, RESERVOIR
- Multiple compatibility matrix tables

**Business Applications**:
- Agriculture equipment hydraulics
- Commercial vehicle hydraulics
- Work truck hydraulic systems
- Custom hydraulic configurations

### 2. [Butler Manufacturing Configurator (SYS<PERSON>Butler)](./ERD-Butler-Manufacturing-Configurator.md)

**Purpose**: Specialized configurator for Butler truck beds and accessories

**Key Features**:
- Truck bed-centric configuration
- Multiple toolbox positioning (left-hand, right-hand, across-bed)
- Hydraulic power unit integration
- Extensive accessory options
- Ford-specific handling and features

**Core Entities**:
- TRUCK, BUTLER_CONFIGURATION, BUTLER_CONFIG_LINE_ITEM
- BED, BED_MOUNT, TOOLBOX, TOOLBOX_COMPATIBILITY
- HYDRAULIC_POWER_UNIT, HEADACHE_RACK, WORK_LIGHTS
- WIRE_HARNESS, BUTLER_ADAPTER, MOUNT_KIT
- Multiple compatibility matrix tables

**Business Applications**:
- Truck bed manufacturing
- Commercial vehicle outfitting
- Fleet vehicle customization
- Specialty truck configurations

## Diagram Comparison

| Aspect | SYS.700CPK | SYS.Butler |
|--------|------------|------------|
| **Primary Focus** | Hydraulic systems | Truck beds & accessories |
| **Core Entity** | VEHICLE | TRUCK |
| **Configuration Entity** | CONFIGURATION | BUTLER_CONFIGURATION |
| **BOM Entity** | CONFIG_LINE_ITEM | BUTLER_CONFIG_LINE_ITEM |
| **Special Applications** | Agricultural (AG) products | Ford-specific features |
| **Component Complexity** | High (many hydraulic components) | Medium (bed-focused components) |
| **Matrix Tables** | 5 main matrices | 5 main matrices |
| **Pricing Integration** | Component-specific pricing matrices | Integrated component pricing |

## Common Design Patterns

### 1. Configuration Management Pattern
Both configurators follow a similar pattern:
- Core vehicle/truck entity
- Configuration header entity
- Line item detail entity for BOM generation

### 2. Compatibility Matrix Pattern
Both systems use matrix tables for filtering compatible options:
- Input dimensions based on vehicle specifications
- Output values determine available components
- Complex multi-dimensional filtering logic

### 3. Component Hierarchy Pattern
Both systems organize components into logical groups:
- Primary components (required)
- Secondary components (optional)
- Accessory components (add-ons)

### 4. Pricing Integration Pattern
Both systems integrate pricing at multiple levels:
- Base component pricing
- Customer-specific pricing codes
- Final configuration pricing

## Key Relationships Across Both Systems

### Primary Relationships
1. **Vehicle/Truck → Configuration** (1:Many)
2. **Configuration → Line Items** (1:Many)
3. **Components → Line Items** (1:Many)

### Matrix Filtering Relationships
- Vehicle specifications drive matrix lookups
- Matrix results filter available components
- Cascading compatibility checks

### Manufacturing Integration
- BOM generation for production
- Part number management
- ERP system integration

## Implementation Considerations

### Database Design
- **Normalization**: Both systems use normalized designs with appropriate denormalization for performance
- **Indexing**: Heavy use of composite indexes on matrix tables for fast filtering
- **Constraints**: Foreign key constraints ensure data integrity across complex relationships

### Performance Optimization
- **Matrix Caching**: Frequently accessed compatibility results should be cached
- **Lazy Loading**: Component details loaded only when needed
- **Batch Processing**: BOM generation optimized for bulk operations

### Scalability Considerations
- **Horizontal Scaling**: Matrix tables can be partitioned by vehicle make/year
- **Vertical Scaling**: Component tables benefit from SSD storage for fast lookups
- **Caching Strategy**: Redis or similar for matrix result caching

## Business Rules Summary

### SYS.700CPK Business Rules
- Mack vehicles require special compatibility checks
- AG applications trigger additional component workflows
- 660 series products have special valve/reservoir handling
- Ford/GM/Sprinter vehicles have default fallback logic

### SYS.Butler Business Rules
- Ford vehicles require special mounting tab handling
- Toolboxes can be positioned in multiple locations simultaneously
- Skirted beds have length-specific part numbers
- Wireless control is optional for hydraulic operations

## Integration Points

### Manufacturing Systems
- **SyteLine ERP**: Primary manufacturing integration
- **BOM Generation**: Automated bill of materials creation
- **Work Orders**: Manufacturing work order generation
- **Inventory**: Real-time inventory checking

### Sales Systems
- **Customer Pricing**: Customer-specific pricing integration
- **Quote Generation**: Automated quote creation
- **Order Processing**: Sales order integration

### External Systems
- **Parts Catalogs**: Integration with parts management systems
- **Shipping**: Integration with logistics systems
- **Warranty**: Integration with warranty tracking systems

## Development Guidelines

### Adding New Components
1. Create component entity with proper attributes
2. Add pricing matrix if needed
3. Update compatibility matrices
4. Add to BOM generation logic
5. Update UI option lists

### Modifying Compatibility Rules
1. Update relevant matrix tables
2. Test cascading effects on dependent components
3. Validate BOM generation
4. Update documentation

### Performance Monitoring
- Monitor matrix query performance
- Track BOM generation times
- Monitor configuration save/load times
- Track user workflow completion rates

## Future Enhancements

### Potential Improvements
- **Real-time Inventory**: Live inventory checking during configuration
- **3D Visualization**: Visual representation of configured products
- **Mobile Support**: Mobile-optimized configuration interfaces
- **AI Recommendations**: Machine learning-based component recommendations

### Scalability Enhancements
- **Microservices**: Break into smaller, focused services
- **Event Sourcing**: Track all configuration changes
- **CQRS**: Separate read/write models for better performance
- **GraphQL**: More flexible API for frontend applications

## Conclusion

These ERD diagrams provide a comprehensive foundation for understanding the data relationships in both Harper configurators. The designs support complex business rules, manufacturing integration, and scalable performance while maintaining data integrity and flexibility for future enhancements.

For detailed entity descriptions, relationships, and business rules, refer to the individual ERD documents linked above.
