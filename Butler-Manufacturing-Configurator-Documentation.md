# Butler Manufacturing Configurator Documentation

## Overview

The Butler Manufacturing Configurator is a web-based tool for configuring truck bed systems. It uses a matrix-driven approach to determine compatible mount kits and adapter harnesses based on truck specifications.

## System Architecture

The configurator follows the same architectural pattern as the Truck Hydraulic System Configurator:

1. **Field Definitions** - Define form fields and their behavior
2. **Matrix Lookups** - Determine compatibility and component selection
3. **Option Lists** - Provide component catalogs with descriptions
4. **Dynamic Form Rendering** - Progressive field revelation based on selections

## Data Flow

```mermaid
graph TD
    A[User Selects Make] --> B[Load Years Options]
    B --> C[User Selects Year]
    C --> D[Load Axle Options]
    D --> E[User Selects Axle]
    E --> F[Load Cab/Axle Options]
    F --> G[User Selects Cab/Axle]
    G --> H[Load Bed Type Options]
    H --> I[User Selects Bed Type]
    I --> J[Calculate Mount Kit]
    I --> K[Calculate Adapter]
    
    J --> L[Matrix: ButlerMKOpt]
    K --> M[Matrix: ButlerAdapterHarns]
    
    L --> N[OptionList: ButlerMountKits]
    M --> O[OptionList: ButlerAdapter]
    
    N --> P[Display Mount Kit with Description]
    O --> Q[Display Adapter with Description]
    
    style J fill:#fff3e0
    style K fill:#fff3e0
    style L fill:#e8f5e8
    style M fill:#e8f5e8
```

## File Structure

### Core Files
- `sys-butler.html` - Main configurator page
- `sys-butler.js` - Form rendering and state management
- `butler-manufacturing-fields.js` - Field definitions and business logic
- `sys-butler.css` - Styling
- `matrix-loader.js` - Matrix evaluation engine (shared)

### Data Files

#### Matrices
- `Matrix_ButlerMKOpt.xml` - Mount kit selection matrix
- `Matrix_ButlerAdapterHarns.xml` - Adapter harness selection matrix
- `Matrix_ButlerSkirted.xml` - Skirted bed configurations

#### Option Lists
- `OptionList_Make.xml` - Vehicle manufacturers
- `OptionList_Years.xml` - Vehicle years
- `OptionList_Axle.xml` - Axle configurations
- `OptionList_Cab_Axle.xml` - Cab and chassis/bed length options
- `OptionList_Bed_Type.xml` - Bed type options
- `OptionList_ButlerMountKits.xml` - Mount kit catalog with descriptions
- `OptionList_ButlerAdapter.xml` - Adapter catalog with descriptions

## Field Definitions

### Core Truck Information Fields

#### 1. Make
- **Type**: Dynamic select
- **Data Source**: `OptionList_Make.xml`
- **Required**: Yes
- **Dependencies**: None

#### 2. Years
- **Type**: Dynamic select  
- **Data Source**: `OptionList_Years.xml`
- **Required**: Yes
- **Dependencies**: Make

#### 3. Axle
- **Type**: Dynamic select
- **Data Source**: `OptionList_Axle.xml`
- **Required**: Yes
- **Dependencies**: Make, Years
- **Options**: Single Rear Wheel (S), Dual Rear Wheel (D)

#### 4. CabAxle
- **Type**: Dynamic select
- **Data Source**: `OptionList_Cab_Axle.xml`
- **Required**: Yes
- **Dependencies**: Make, Years, Axle
- **Common Options**: 
  - SB40 (Short Bed 40")
  - LB56 (Long Bed 56")
  - CC84 (Cab & Chassis 84")

#### 5. BedType
- **Type**: Dynamic select
- **Data Source**: `OptionList_Bed_Type.xml`
- **Required**: Yes
- **Dependencies**: Make, Years, Axle, CabAxle
- **Options**: Regular/Flat (R), Aluminum (A), Steel (S)

### Calculated Display Fields

#### 6. MountKit
- **Type**: Display (calculated)
- **Matrix**: `ButlerMKOpt`
- **Matrix Inputs**: Make, Years, Axle, CabAxle, BedType
- **Description Lookup**: `OptionList_ButlerMountKits.xml`
- **Output**: Mount kit part number with description

#### 7. ButlerAdapter
- **Type**: Display (calculated)
- **Matrix**: `ButlerAdapterHarns`
- **Matrix Inputs**: Make, Years, Axle, CabAxle, BedType
- **Description Lookup**: `OptionList_ButlerAdapter.xml`
- **Output**: Adapter part number(s) with descriptions
- **Special Handling**: Multiple adapters separated by dashes

## Matrix Structure

### ButlerMKOpt Matrix
```xml
<Matrix Name="ButlerMKOpt" Description="Butler Mount Kit" DataType="String">
  <Dimension Name="Make" />
  <Dimension Name="Year" />
  <Dimension Name="Axle" />
  <Dimension Name="Cab_Axle" />
  <Dimension Name="Bed_Type" />
  <Value>
    <Lookup>FD</Lookup>      <!-- Ford -->
    <Lookup>2024</Lookup>    <!-- Year -->
    <Lookup>D</Lookup>       <!-- Dual Rear Wheel -->
    <Lookup>LB56</Lookup>    <!-- Long Bed 56" -->
    <Lookup>R</Lookup>       <!-- Regular Bed -->
    <Value>205358</Value>    <!-- Mount Kit Part Number -->
  </Value>
</Matrix>
```

### ButlerAdapterHarns Matrix
```xml
<Matrix Name="ButlerAdapterHarns" Description="" DataType="String">
  <Dimension Name="Make" />
  <Dimension Name="Year" />
  <Dimension Name="Axle" />
  <Dimension Name="Cab_Axle" />
  <Dimension Name="Bed_Type" />
  <Value>
    <Lookup>FD</Lookup>      <!-- Ford -->
    <Lookup>2024</Lookup>    <!-- Year -->
    <Lookup>D</Lookup>       <!-- Dual Rear Wheel -->
    <Lookup>LB56</Lookup>    <!-- Long Bed 56" -->
    <Lookup>R</Lookup>       <!-- Regular Bed -->
    <Value>232162-232185-</Value>  <!-- Multiple Adapters -->
  </Value>
</Matrix>
```

## Key Features

### Progressive Field Revelation
Fields are revealed progressively as the user makes selections:
1. Make → Years → Axle → CabAxle → BedType
2. Calculated fields (MountKit, ButlerAdapter) appear after all required inputs

### Dynamic Option Loading
- Options are loaded from XML files at runtime
- Fallback to hardcoded options if XML loading fails
- Options are filtered based on previous selections

### Real-time Calculation
- Mount kit and adapter calculations happen immediately after BedType selection
- Results include both part numbers and descriptions
- Error handling for missing or invalid configurations

### Multiple Adapter Handling
- Adapter matrix can return multiple part numbers separated by dashes
- Each adapter is looked up individually for descriptions
- Results are combined into a single display string

## Error Handling

### Matrix Lookup Failures
- **No Match**: Display "No mount kit/adapter available for this configuration"
- **Matrix File Missing**: Log error and show error message
- **Invalid Input**: Validate inputs before matrix lookup

### Option List Failures
- **File Missing**: Fall back to hardcoded options
- **Parse Error**: Log error and use fallback options
- **Network Error**: Retry with cached data if available

### Common Error Scenarios
1. **Unsupported Configuration**: Some truck configurations may not have mount kits
2. **Missing Descriptions**: Part numbers without descriptions in option lists
3. **Multiple Adapters**: Parsing dash-separated adapter codes

## Testing

### Test Scenarios

#### Valid Configuration
```javascript
{
  Make: 'FD',        // Ford
  Years: '2024',     // 2024
  Axle: 'D',         // Dual Rear Wheel
  CabAxle: 'LB56',   // Long Bed 56"
  BedType: 'R'       // Regular Bed
}
// Expected: Mount kit and adapter results
```

#### Edge Cases
- Very old truck years (may not have mount kits)
- Uncommon axle/bed combinations
- Missing option list entries

### Debug Features
- Debug panel shows current form state and visible fields
- Console logging for matrix evaluations
- Error messages for failed lookups

## Development Guidelines

### Adding New Fields
1. Define field in `butler-manufacturing-fields.js`
2. Add to appropriate field group
3. Update dependency mapping if needed
4. Test with various configurations

### Modifying Matrices
1. Backup existing matrix file
2. Update XML maintaining dimension order
3. Test with representative configurations
4. Validate error handling

### Performance Optimization
- Matrix files are cached after first load
- Option lists loaded on demand
- Form updates are debounced to prevent excessive re-rendering

## Future Enhancements

### Potential Additions
1. **Pricing Integration** - Add pricing matrices for cost calculation
2. **Image Support** - Display product images from option lists
3. **Configuration Validation** - Advanced validation rules
4. **Export Functionality** - Export configuration as PDF/email
5. **Inventory Integration** - Real-time availability checking

### Technical Improvements
1. **Offline Support** - Service worker for offline capability
2. **Performance** - Lazy loading of large option lists
3. **Accessibility** - Enhanced keyboard navigation and screen reader support
4. **Mobile Optimization** - Responsive design improvements
