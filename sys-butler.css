body {
    font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    background: #f5f7fa;
    color: #2a3a4a;
}

main {
    max-width: 800px;
    margin: 2rem auto;
    padding: 2rem;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.07);
  padding: 2rem;
}
h1 {
    margin: 0;
    color: #2a3a4a;
    font-size: 1.8rem;
    font-weight: 600;
}

.config-step {
  margin-bottom: 2rem;
}
label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.5rem;
}
select, input[type="checkbox"], input[type="radio"] {
  margin-bottom: 1rem;
  font-size: 1rem;
  padding: 0.5rem;
  border-radius: 6px;
  border: 1px solid #d1d5db;
  width: 100%;
  box-sizing: border-box;
}

select,
input[type="number"] {
    width: 100%;
    max-width: 400px;
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 1rem;
    color: #2a3a4a;
    background: #fff;
    transition: all 0.2s ease;
}

select:hover,
input[type="number"]:hover {
    border-color: #cbd5e0;
}

select:focus,
input[type="number"]:focus {
    outline: none;
    border-color: #2a7ae2;
    box-shadow: 0 0 0 3px rgba(42, 122, 226, 0.15);
}

button {
  background: #2563eb;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}
button:disabled {
  background: #a5b4fc;
  cursor: not-allowed;
}
.summary {
  background: #f1f5f9;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 2rem;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.back-link {
    color: #2a7ae2;
    text-decoration: none;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.back-link:hover {
    text-decoration: underline;
}

#configurator {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.error-message {
    color: #e53935;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

/* Field groups */
.field-group {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.field-group h3 {
    margin: 0 0 1rem 0;
    color: #2a3a4a;
    font-size: 1.2rem;
    font-weight: 600;
    border-bottom: 2px solid #2a7ae2;
    padding-bottom: 0.5rem;
}

/* Input containers */
.input-container {
    display: flex;
    flex-direction: column;
    margin-bottom: 1.5rem;
}

.input-container label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #2a3a4a;
}

/* Display field styles */
.display-field {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 4px;
    padding: 0.75rem;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #495057;
    min-height: 1.5rem;
    line-height: 1.4;
}

.display-field.loading {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.display-field.error {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

/* Checkbox wrapper */
.checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-wrapper input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkbox-label {
    font-weight: normal;
    margin: 0;
}

/* Field errors */
.field-error {
    background: #f8d7da;
    color: #721c24;
    padding: 0.5rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    border: 1px solid #f5c6cb;
}

button[type="submit"] {
    background: #2a7ae2;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin-top: 1rem;
}

button[type="submit"]:hover {
    background: #1a5bb8;
}

button[type="submit"]:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(42, 122, 226, 0.25);
}
