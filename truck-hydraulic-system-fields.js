// truck-hydraulic-system-fields.js
// Field definitions and dynamic logic for the SYS-700CPK Configurator
//
// This module defines all form fields, their dependencies            // Engine selection debug info is logged only on erroramic option logic, and grouping for the configurator UI.
//
// Main concepts:
// - Each field is an entry in the `fields` object, with label, type, and logic.
// - Dynamic options are loaded via async getOptions(formState) functions.
// - Display/calculated fields (e.g., Adapter) use async getValue(formState).
// - Field groups (Truck Selections, AG Questions, Commercial Questions) are defined in getFieldGroups().
// - Field visibility is controlled by the `condition` property and isFieldVisible().
//
// To add or modify fields, edit the `fields` object and update getFieldGroups() as needed.
//
// See SYS-700CPK-Configurator-Documentation.md for a high-level overview and usage examples.

// Cache for engine options
var engineOptionsCache = (function () {
    var cache = {};
    return {
        getCacheKey: function (formState) {
            return [
                formState.Make,
                formState.Vehicle_Type,
                formState.Year,
                formState.Application
            ].join('|');
        },
        get: function (formState) {
            return cache[this.getCacheKey(formState)];
        },
        set: function (formState, options) {
            cache[this.getCacheKey(formState)] = options;
        },
        clear: function () {
            cache = {};
        }
    };
})();

// Field option caching
var optionsCache = {
    _data: {},
    _getCacheKey: function (fieldName, formState) {
        var stateStr = '';
        // Only include relevant state fields in cache key
        var relevantFields = {
            Engine: ['Make', 'Vehicle_Type', 'Year', 'Application'],
            Pump: ['Engine', 'Year', 'Vehicle_Type', 'Application']
        };
        if (relevantFields[fieldName]) {
            stateStr = relevantFields[fieldName].map(function (key) {
                return formState[key];
            }).join('|');
        }
        return fieldName + ':' + stateStr;
    },
    get: function (fieldName, formState) {
        var key = this._getCacheKey(fieldName, formState);
        return this._data[key];
    },
    set: function (fieldName, formState, options) {
        var key = this._getCacheKey(fieldName, formState);
        this._data[key] = options;
        console.debug('Cached options for ' + fieldName + ':', {
            key: key,
            optionsCount: options.length
        });
    },
    clear: function (fieldName) {
        if (fieldName) {
            // Clear only cache entries for this field
            var prefix = fieldName + ':';
            Object.keys(this._data).forEach(function (key) {
                if (key.startsWith(prefix)) {
                    delete this._data[key];
                }
            }, this);
        } else {
            this._data = {};
        }
    }
};

// Component attributes for the 700CPK configurator
import { evaluateMatrixValue } from './matrix-loader.js';

// Helper functions for group normalization
function cleanGroup(group) {
    if (group == null) return null;
    const str = group.toString().trim();
    // Remove any spaces and special characters but keep letters and numbers
    const cleaned = str.replace(/[^a-zA-Z0-9]/g, '');
    // Then extract just the numbers from the end of the string
    const numbers = cleaned.match(/\d+$/);
    return numbers ? numbers[0] : cleaned;
}

function padGroup(group) {
    if (group == null) return null;
    // Only pad if the group is numeric
    return /^\d+$/.test(group) ? group.padStart(3, '0') : group;
}

var fields = {
    Application: {
        label: "Application",
        type: "select",
        required: true,
        options: [
            { value: "AG", label: "Agriculture" },
            { value: "COM", label: "Commercial Application" },
            { value: "OTHER", label: "Other Application" },
            { value: "WRK", label: "Wrecker" }
        ]
    },

    AGKitLessMiniPack: {
        label: "Less AG Mini Pack?",
        type: "boolean",
        required: false,
        condition: function (formState) { return formState.Application === 'AG'; }
    },

    Make: {
        label: "Select Make of Vehicle",
        type: "select",
        required: true,
        condition: function (formState) { return formState.Application && formState.Application.length > 0; },
        getOptions: async function (formState) {
            console.log('Getting Make options for Application:', formState.Application);

            // Load Make option list from XML
            try {
                var response = await fetch('./data/Option_Lists/OptionList_Make.xml');
                if (!response.ok) throw new Error('Failed to load Make options');

                var text = await response.text();
                var parser = new DOMParser();
                var xmlDoc = parser.parseFromString(text, "text/xml");

                var makeOptions = [];
                var values = xmlDoc.querySelectorAll('Value');

                for (var i = 0; i < values.length; i++) {
                    var value = values[i];
                    var code = value.querySelector('Property[Name="Value"]');
                    var description = value.querySelector('Property[Name="Description"]');
                    code = code ? code.textContent : null;
                    description = description ? description.textContent : null;
                    var groupElems = value.querySelectorAll('Group');
                    var groups = [];
                    for (var j = 0; j < groupElems.length; j++) {
                        var groupName = groupElems[j].getAttribute('Name');
                        if (groupName) groups.push(groupName);
                    }

                    if (code && description) {
                        // Filter based on application - AG uses group "B", others use group "O"
                        var shouldInclude = formState.Application === 'AG' ?
                            groups.indexOf('B') !== -1 :
                            groups.indexOf('O') !== -1;

                        if (shouldInclude) {
                            makeOptions.push({ value: code, label: description });
                        }
                    }
                }

                // Sort by order if available, otherwise alphabetically
                makeOptions.sort((a, b) => a.label.localeCompare(b.label));

                console.log('Make options loaded:', makeOptions);
                return makeOptions;
            } catch (error) {
                console.error('Error loading Make options:', error);
                // Fallback options
                const fallbackOptions = [
                    { value: "FD", label: "Ford" },
                    { value: "DG", label: "Dodge/Ram" },
                    { value: "GM", label: "Chevrolet/GMC" },
                    { value: "FL", label: "Freightliner" }
                ];

                return formState.Application === 'AG' ?
                    fallbackOptions.filter(opt => ['FD', 'DG', 'GM'].includes(opt.value)) :
                    fallbackOptions;
            }
        }
    },

    Macfit: {
        label: "Click info link select yes if kit will fit:",
        type: "boolean",
        required: true,
        condition: (formState) => formState.Make === 'MC',
        infoLink: "/bulletin/IB7042201Mack.pdf",
        critical: true,
        errorMessage: "Will Not Fit"
    },

    Year: {
        label: "Truck Year",
        type: "select",
        required: true,
        condition: (formState) => formState.Make && formState.Make.length > 0,
        async getOptions(formState) {
            console.log('Getting Year options');
            const currentYear = new Date().getFullYear();
            const years = [];

            // Generate years from current year back to 2000
            for (let year = currentYear; year >= 2000; year--) {
                years.push({ value: year.toString(), label: year.toString() });
            }

            return years;
        }
    },

    Vehicle_Type: {
        label: "Select Vehicle Type",
        type: "select",
        required: true,
        condition: function (formState) {
            return formState.Make && formState.Year && formState.Year.length > 0;
        },
        getOptions: async function (formState) {
            // Skip matrix lookup if required fields aren't present
            if (!formState.Make || !formState.Year || !formState.Application) {
                return [{ value: "TRUCK", label: "Truck" }];
            }

            console.log('Getting Vehicle_Type options for:', {
                Make: formState.Make,
                Year: formState.Year
            });

            // Use VT_Filter matrix to determine available vehicle types
            var vtFilter = await evaluateMatrixValue('VT_Filter', {
                Make: formState.Make,
                Year: formState.Year,
                Application: formState.Application
            }, { defaultValue: "1" });  // Default to trucks only

            console.log('VT_Filter result:', vtFilter);

            // Map filter results to vehicle type options
            var allVehicleTypes = [
                { value: "TRUCK", label: "Truck" },
                { value: "VAN", label: "Van" }
            ];

            // Filter based on VT_Filter result
            if (vtFilter === "NOVAN" || vtFilter === "1") {
                return [{ value: "TRUCK", label: "Truck" }];
            } else if (vtFilter === "3") {
                return allVehicleTypes;
            } else {
                // Default to truck only
                return [{ value: "TRUCK", label: "Truck" }];
            }
        }
    },

    Engine: {
        label: "Select Engine",
        type: "select",
        required: true,
        _lastState: '',
        condition: function (formState) {
            var state = {
                make: formState.Make,
                year: formState.Year,
                vehicleType: formState.Vehicle_Type
            };

            var hasMake = state.make && state.make !== '';
            var hasYear = state.year && state.year !== '';
            var hasVehicleType = state.vehicleType && state.vehicleType !== '';
            var shouldShow = hasMake && hasYear && hasVehicleType;

            var stateStr = JSON.stringify(state);
            if (this._lastState !== stateStr) {
                this._lastState = stateStr;
                console.debug('Engine field visibility check:', {
                    ...state,
                    hasMake: hasMake,
                    hasYear: hasYear,
                    hasVehicleType: hasVehicleType,
                    shouldShow: shouldShow
                });

                // Clear cache and trigger dependency updates
                if (shouldShow !== this._lastVisible) {
                    this._lastVisible = shouldShow;
                    optionsCache.clear('Engine');
                }
            }
            return shouldShow;
        },
        async getOptions(formState) {
            // Check cache first
            var cachedOptions = optionsCache.get('Engine', formState);
            if (cachedOptions) {
                console.debug('Using cached Engine options:', {
                    count: cachedOptions.length,
                    Make: formState.Make,
                    Year: formState.Year,
                    Vehicle_Type: formState.Vehicle_Type
                });
                return cachedOptions;
            }

            console.debug('Getting Engine options for:', {
                Make: formState.Make,
                Vehicle_Type: formState.Vehicle_Type,
                Year: formState.Year,
                Application: formState.Application
            });

            // Map vehicle type from UI value to matrix value
            var matrixVehicleType = formState.Vehicle_Type === 'TRUCK' ? 'OT' : formState.Vehicle_Type;

            // Build lookup parameters - remove " =" suffix as matrix loader handles it
            const engineLookupParams = {
                Make: formState.Make,
                Vehicle_Type: matrixVehicleType,
                Year: formState.Year
            };

            console.debug('Looking up engine groups with:', engineLookupParams);

            // Get engine groups from MultipleEngineGroups matrix
            const engineGroups = await evaluateMatrixValue('MultipleEngineGroups', engineLookupParams, {
                debug: true,  // Enable detailed matrix evaluation logging
                defaultValue: null,  // Return null for no match
                fallbackValue: formState.Make === 'FD' ? 'FD67D' : null  // Default engine for Ford
            });

            console.debug('Engine groups result:', {
                inputs: {
                    make: formState.Make,
                    vehicleType: matrixVehicleType,
                    year: formState.Year
                },
                result: engineGroups
            });

            var options;
            if (!engineGroups || engineGroups === 'ZZ') {
                console.warn('Engine groups lookup failed - using fallback options');
                // Fallback based on make
                const fallbackEngines = {
                    'FD': [{ value: "FD67D", label: "Ford 6.7L Diesel" }],
                    'DG': [{ value: "DG67D", label: "Dodge/Ram 6.7L Diesel" }],
                    'GM': [{ value: "GM66D", label: "Chevrolet/GMC 6.6L Diesel" }],
                    'FL': [{ value: "FLM2C", label: "Freightliner M2 Cummins" }]
                };
                options = fallbackEngines[formState.Make] || [];
                console.debug('Using fallback engines:', options);
            } else {
                // Only try to load XML if we got valid engine groups
                try {
                    var response = await fetch('./data/Option_Lists/OptionList_Engine.xml');
                    if (!response.ok) throw new Error('Failed to load Engine options');

                    var text = await response.text();
                    var parser = new DOMParser();
                    var xmlDoc = parser.parseFromString(text, "text/xml");

                    var engineOptions = [];
                    var values = xmlDoc.querySelectorAll('Value');

                    for (var i = 0; i < values.length; i++) {
                        var value = values[i];
                        var code = value.querySelector('Property[Name="Value"]');
                        var description = value.querySelector('Property[Name="Description"]');
                        code = code ? code.textContent : null;
                        description = description ? description.textContent : null;
                        var groupElems = value.querySelectorAll('Group');
                        var groups = [];
                        for (var j = 0; j < groupElems.length; j++) {
                            var groupName = groupElems[j].getAttribute('Name');
                            if (groupName) groups.push(groupName);
                        }

                        if (code && description) {
                            // Check if engine belongs to any of the allowed groups
                            var groupsToCheck = Array.isArray(engineGroups) ? engineGroups : [engineGroups];
                            var shouldInclude = false;
                            for (var k = 0; k < groupsToCheck.length; k++) {
                                if (groups.indexOf(groupsToCheck[k]) !== -1) {
                                    shouldInclude = true;
                                    break;
                                }
                            }

                            if (shouldInclude) {
                                engineOptions.push({ value: code, label: description });
                            }
                        }
                    }

                    if (engineOptions.length === 0) {
                        console.error('🚫 No matching engines found for groups:', {
                            groups: engineGroups,
                            make: formState.Make,
                            year: formState.Year,
                            vehicleType: formState.Vehicle_Type
                        });
                    }
                    options = engineOptions;
                } catch (error) {
                    console.error('Error loading Engine options:', error);
                    // Fallback based on make
                    const fallbackEngines = {
                        'FD': [{ value: "FD67D", label: "Ford 6.7L Diesel" }],
                        'DG': [{ value: "DG67D", label: "Dodge/Ram 6.7L Diesel" }],
                        'GM': [{ value: "GM66D", label: "Chevrolet/GMC 6.6L Diesel" }],
                        'FL': [{ value: "FLM2C", label: "Freightliner M2 Cummins" }]
                    }; options = fallbackEngines[formState.Make] || [];
                    console.debug('Engine options fallback:', {
                        make: formState.Make,
                        options: options
                    });
                }
            }

            // Store in cache
            engineOptionsCache.set(formState, options);

            return options;
        }
    },

    Pump: {
        label: "Select Pump Size",
        type: "select",
        required: true,
        condition: (formState) => formState.Engine && formState.Engine !== 'NONE',
        async getOptions(formState) {
            // Skip matrix lookup if required fields aren't present
            if (!formState.Engine || !formState.Vehicle_Type || !formState.Year) {
                return [
                    { value: "9GL", label: "9 Gal Pump" },
                    { value: "12GL", label: "12 Gal Pump" }
                ];
            }

            // Use normalized values for matrix lookup
            const matrixVehicleType = normalizeVehicleType(formState.Vehicle_Type);
            const matrixAppType = normalizeApplicationType(formState.Application);

            try {
                // Get pump groups from matrix with normalized values
                const pumpGroups = await evaluateMatrixValue('PumpGroupMatrix', {
                    ...formState,
                    Vehicle_Type: matrixVehicleType,
                    Application: matrixAppType
                }, {
                    type: 'pump',
                    defaultValue: null
                });

                if (!pumpGroups) {
                    // Fallback pump options - using standard XGL format
                    return [
                        { value: "9GL", label: "9 Gal Pump" },
                        { value: "12GL", label: "12 Gal Pump" }
                    ];
                }

                // Try primary pump options list first
                const response = await fetch('./data/Option_Lists/OptionList_Pump_1T1.xml');
                const xmlText = await response.text();

                // If primary fails, try backup options
                if (!xmlText.includes('Value')) {
                    const altResponse = await fetch('./data/Option_Lists/OptionList_Pump.xml');
                    const altXmlText = await altResponse.text();
                    return parsePumpOptions(altXmlText, pumpGroups);
                }

                return parsePumpOptions(xmlText, pumpGroups);
            } catch (error) {
                console.error('Error loading pump options:', error);
                // Fallback with normalized values
                return [
                    { value: "9GL", label: "9 Gal Pump" },
                    { value: "12GL", label: "12 Gal Pump" }
                ];
            }
        }
    },

    Kit: {
        label: "Select Your Kit",
        type: "select",
        required: true,
        description: "Select the appropriate kit based on your vehicle configuration",
        helpText: "Kit options are filtered based on your vehicle make, year, engine, and pump selections",
        condition: (formState) => {
            // Only show after all required fields are selected
            if (!formState.Engine || !formState.Year || !formState.Pump ||
                !formState.Vehicle_Type || !formState.Application || !formState.Make) {
                return false;
            }

            // Show for all valid combinations
            return true;
        },
        async getOptions(formState) {
            console.debug('Getting Kit options for:', {
                Engine: formState.Engine,
                Year: formState.Year,
                Pump: formState.Pump,
                Vehicle_Type: formState.Vehicle_Type,
                Application: formState.Application,
                normalized: {
                    Engine: formState.Engine,
                    Year: formState.Year,
                    Pump: formState.Pump,
                    Vehicle_Type: normalizeVehicleType(formState.Vehicle_Type),
                    App: formState.Application === 'AG' ? 'B' : 'C'
                }
            });

            // Normalize values for matrix lookup
            const engineCode = formState.Engine;  // e.g., FD67D
            const normalizedPump = formState.Pump?.replace(/HP$/, '');  // Remove HP suffix if present

            console.debug('Normalized values for Kit lookup:', {
                original: {
                    Engine: formState.Engine,
                    Year: formState.Year,
                    Pump: formState.Pump,
                    Vehicle_Type: formState.Vehicle_Type,
                    Application: formState.Application
                },
                normalized: {
                    Engine: engineCode,
                    Year: formState.Year,
                    Pump: normalizedPump,
                    Vehicle_Type: 'OT',  // Always use OT for kit lookups
                    App: formState.Application === 'AG' ? 'A' : 'C'  // AG -> A, COM/OTHER -> C
                }
            });

            // Get kit groups from Kit_Group matrix using normalized values
            let kitGroups = await evaluateMatrixValue('Kit_Group', {
                Engine: engineCode,
                Year: formState.Year,
                Pump: normalizedPump,
                Vehicle_Type: 'OT',  // Always use OT for kit lookups
                App: formState.Application === 'AG' ? 'B' : 'C'  // AG -> B, COM/OTHER -> C
            }, {
                defaultValue: null,
                debug: true,
                fallbackValue: '620'  // Default kit group for new configurations
            });

            // If no match found, try without pump size
            if (!kitGroups || kitGroups === 'NA') {
                console.debug('Trying generic pump fallback...');
                kitGroups = await evaluateMatrixValue('Kit_Group', {
                    Engine: engineCode,
                    Year: formState.Year,
                    Pump: '*',  // Match any pump size
                    Vehicle_Type: 'OT',
                    App: formState.Application === 'AG' ? 'A' : 'C'
                }, {
                    defaultValue: null,
                    debug: true
                });
            }

            // If still no match, try previous year as fallback
            if (!kitGroups || kitGroups === 'NA') {
                const prevYear = (parseInt(formState.Year) - 1).toString();
                console.debug('Trying previous year fallback:', prevYear);
                kitGroups = await evaluateMatrixValue('Kit_Group', {
                    Engine: engineCode,
                    Year: prevYear,
                    Pump: normalizedPump,
                    Vehicle_Type: 'OT',
                    App: formState.Application === 'AG' ? 'A' : 'C'
                }, {
                    defaultValue: null,
                    debug: true
                });
            }

            console.log('Kit groups result:', kitGroups);

            console.debug('Kit groups result:', {
                groups: kitGroups,
                isNull: kitGroups === null,
                isNA: kitGroups === 'NA',
                type: typeof kitGroups
            });

            if (!kitGroups || kitGroups === 'NA') {
                console.warn('No kit options available - Matrix returned:', kitGroups);
                return [];
            }

            // Load Kit option list
            try {
                // First get available kits from hydraulic_kits.csv
                const kitsResponse = await fetch('./datasheets/hydraulic_kits.csv');
                if (!kitsResponse.ok) throw new Error('Failed to load kit data');

                const kitsText = await kitsResponse.text();
                const kits = kitsText.split('\n')
                    .slice(1) // Skip header row
                    .map(line => {
                        const [id, part_number, description, price, group, engine, pump, app_type] = line.split(',');
                        return {
                            id,
                            part_number,
                            description,
                            price: parseFloat(price),
                            group,
                            engine,
                            pump,
                            app_type
                        };
                    });

                // Filter kits based on current selections
                const matchingKits = kits.filter(kit => {
                    return (
                        kit.engine === engineCode &&
                        kit.pump === normalizedPump &&
                        kit.app_type === (formState.Application === 'AG' ? 'AG' : 'COM')
                    );
                });

                // Load XML options for additional metadata
                const response = await fetch('./data/Option_Lists/OptionList_Kit.xml');
                if (!response.ok) throw new Error('Failed to load Kit options');

                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");

                const kitOptions = [];
                const values = xmlDoc.querySelectorAll('Value');

                values.forEach(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;
                    const groups = Array.from(value.querySelectorAll('Group')).map(g => g.getAttribute('Name'));

                    if (code && description) {
                        // Split dash-separated groups and ensure we have arrays to work with
                        const groupsToCheck = Array.isArray(kitGroups) ?
                            kitGroups :
                            (typeof kitGroups === 'string' ? kitGroups.split('-') : [kitGroups]);

                        // Log raw values before normalization
                        console.debug('Raw values before normalization:', {
                            kitGroup: kitGroups,
                            xmlGroups: groups,
                            groupsToCheck
                        });

                        // Extract and normalize each kit group
                        const normalizedKitGroups = groupsToCheck.map(g =>
                            padGroup(cleanGroup(g.toString()))
                        );

                        // Normalize the XML groups
                        const normalizedItemGroups = groups
                            .filter(g => g != null)
                            .map(g => padGroup(cleanGroup(g)));

                        console.debug('Normalized values:', {
                            kitGroups: normalizedKitGroups,
                            xmlGroups: normalizedItemGroups
                        });

                        // Debug info for group matching
                        console.debug('Kit group matching:', {
                            kitGroups: normalizedKitGroups,
                            itemGroups: normalizedItemGroups,
                            original: {
                                kitGroups: groupsToCheck,
                                itemGroups: groups
                            }
                        });

                        // Check if any normalized kit group matches any of the normalized item groups
                        const shouldInclude = normalizedKitGroups.some(kg =>
                            normalizedItemGroups.some(ig => {
                                const matches = kg === ig;
                                console.debug('Comparing groups:', {
                                    kitGroup: kg,
                                    itemGroup: ig,
                                    matches
                                });
                                return matches;
                            })
                        );

                        console.debug('Kit option evaluation:', {
                            code,
                            description,
                            itemGroups: groups.map(g => ({
                                original: g,
                                cleaned: cleanGroup(g),
                                normalized: padGroup(cleanGroup(g)),
                                isNull: g == null,
                                type: typeof g,
                                stringified: JSON.stringify(g)
                            })),
                            kitGroups: groupsToCheck.map(g => ({
                                original: g,
                                cleaned: cleanGroup(g),
                                normalized: padGroup(cleanGroup(g)),
                                isNull: g == null,
                                type: typeof g,
                                stringified: JSON.stringify(g)
                            })),
                            normalizedComparison: {
                                kitGroups: normalizedKitGroups,
                                itemGroups: normalizedItemGroups
                            },
                            shouldInclude
                        });

                        if (shouldInclude) {
                            kitOptions.push({ value: code, label: code }); // Use code as label for kits
                        }
                    }
                });

                console.log('Kit options loaded:', kitOptions);
                return kitOptions;
            } catch (error) {
                console.error('Error loading Kit options:', error);
                // Fallback kit options based on video scenarios
                return [
                    { value: "700536", label: "700536" },
                    { value: "700607", label: "700607" }
                ];
            }
        }
    },

    Clutch: {
        label: "Select Your Clutch",
        type: "select",
        required: true,
        condition: (formState) => formState.Kit && formState.Kit.length > 0,
        async getOptions(formState) {
            console.log('Getting Clutch options for Kit:', formState.Kit);

            // Get clutch groups from matrices
            const clutchFilterPre = await evaluateMatrixValue('Clutch_Group', formState);
            const clutchExceptions = await evaluateMatrixValue('HD_Clutch', formState);
            const clutchFilter = clutchExceptions || clutchFilterPre;

            console.log('Clutch filter result:', clutchFilter);

            // Load Clutch option list
            try {
                const response = await fetch('./data/Option_Lists/OptionList_Clutch.xml');
                if (!response.ok) throw new Error('Failed to load Clutch options');

                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");

                const clutchOptions = [];
                const values = xmlDoc.querySelectorAll('Value');

                values.forEach(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;
                    const groups = Array.from(value.querySelectorAll('Group')).map(g => g.getAttribute('Name'));

                    if (code && description) {
                        const groupsToCheck = Array.isArray(clutchFilter) ? clutchFilter : [clutchFilter];
                        const shouldInclude = !clutchFilter || groupsToCheck.some(group => groups.includes(group));

                        if (shouldInclude) {
                            clutchOptions.push({ value: code, label: description });
                        }
                    }
                });

                console.log('Clutch options loaded:', clutchOptions);
                return clutchOptions;
            } catch (error) {
                console.error('Error loading Clutch options:', error);
                // Fallback clutch options from video scenarios
                return [
                    { value: "8GRVA", label: "8 Groove Ogura \"A\"" },
                    { value: "6GRVA", label: "6 Groove Ogura \"A\"" }
                ];
            }
        }
    },

    Adapter: {
        label: "Adapter",
        type: "display", // This will be a display field showing the calculated adapter
        required: true,
        condition: (formState) => formState.Kit && formState.Pump,
        getValue: async function (formState) {
            console.debug('Getting Adapter value for:', {
                Kit: formState.Kit,
                Pump: formState.Pump,
                Make: formState.Make,
                Engine: formState.Engine,
                Application: formState.Application
            });

            try {
                // Get adapter groups from MultipleAdapterGroups matrix
                // Extract last 4 digits of kit number as required by matrix
                const kitLast4 = formState.Kit ? formState.Kit.slice(-4) : '';

                console.debug('Adapter matrix lookup parameters:', {
                    originalKit: formState.Kit,
                    kitLast4: kitLast4,
                    pump: formState.Pump?.replace(/HP$/, ''),
                    application: formState.Application,
                    agValue: formState.Application === 'AG' ? 'B' : 'O'
                });

                const adapterGroup = await evaluateMatrixValue('MultipleAdapterGroups', {
                    'Kit (Skip first 4)': kitLast4,
                    'Pump': formState.Pump?.replace(/HP$/, ''),  // Remove HP suffix if present
                    'AG': formState.Application === 'AG' ? 'B' : 'O'
                }, {
                    debug: true,  // Enable detailed matrix evaluation logging
                    defaultValue: null
                });

                console.debug('Adapter group result:', {
                    result: adapterGroup,
                    type: typeof adapterGroup,
                    isNull: adapterGroup === null
                });

                if (!adapterGroup) {
                    // If no match in matrix, fall back to make-based defaults
                    const adapterMap = {
                        'FD': "03-51261; S=16HB-16MB90; P=10MJ-12MB45",
                        'FL': "03-6923; S=20MB90; P=12MJ-12MB90",
                        'DG': "03-1920; S=16HB-16MB90; P=10MJ-12MB90"
                    };
                    return adapterMap[formState.Make] || "Adapter TBD";
                }

                // Look up the full adapter description from the option list
                try {
                    const response = await fetch('./data/Option_Lists/OptionList_Adapter.xml');
                    if (!response.ok) throw new Error('Failed to load Adapter options');

                    const text = await response.text();
                    const parser = new DOMParser();
                    const xmlDoc = parser.parseFromString(text, "text/xml");

                    // Find the adapter entry that matches our result
                    const values = xmlDoc.querySelectorAll('Value');
                    for (const value of values) {
                        const code = value.querySelector('Property[Name="Value"]')?.textContent;
                        const description = value.querySelector('Property[Name="Description"]')?.textContent;

                        if (code === adapterGroup) {
                            console.debug('Found adapter description:', { code, description });
                            return description || adapterGroup;
                        }
                    }

                    // If not found in option list, return the matrix result
                    console.warn(`Adapter ${adapterGroup} not found in option list`);
                    return adapterGroup;

                } catch (error) {
                    console.error('Error loading adapter descriptions:', error);
                    // Fall back to matrix result if option list loading fails
                    return adapterGroup;
                }
            } catch (error) {
                console.error('Error getting adapter value:', error);
                return "Error determining adapter";
            }
        }
    },

    // AG-specific fields that appear in the "AG Questions" section
    Product: {
        label: "Select DewEze Product",
        type: "select",
        required: true,
        condition: (formState) => formState.Application === 'AG',
        async getOptions(formState) {
            try {
                const response = await fetch('./data/Option_Lists/OptionList_Product.xml');
                if (!response.ok) throw new Error('Failed to load Product options');

                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");

                const productOptions = [];
                const values = xmlDoc.querySelectorAll('Value');

                values.forEach(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;

                    if (code && description) {
                        productOptions.push({ value: code, label: description });
                    }
                });

                return productOptions;
            } catch (error) {
                console.error('Error loading Product options:', error);
                // Fallback from video scenarios
                return [
                    { value: "DB800", label: "Deweze Balebed 800-900 Series" },
                    { value: "RT800", label: "RT/XRT 800-900 Bed" }
                ];
            }
        }
    },

    Valve: {
        label: "What Type of valve do you want",
        type: "select",
        required: true,
        condition: (formState) => formState.Application === 'AG' && formState.Product && formState.Product !== '660',
        async getOptions(formState) {
            // Get valve groups from MultipleValveGroups matrix
            const valveGroups = await evaluateMatrixValue('MultipleValveGroups', formState);

            try {
                const response = await fetch('./data/Option_Lists/OptionList_Valve.xml');
                if (!response.ok) throw new Error('Failed to load Valve options');

                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");

                const valveOptions = [];
                const values = xmlDoc.querySelectorAll('Value');

                values.forEach(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;
                    const groups = Array.from(value.querySelectorAll('Group')).map(g => g.getAttribute('Name'));

                    if (code && description) {
                        const groupsToCheck = Array.isArray(valveGroups) ? valveGroups : [valveGroups];
                        const shouldInclude = !valveGroups || groupsToCheck.some(group => groups.includes(group));

                        if (shouldInclude) {
                            valveOptions.push({ value: code, label: description });
                        }
                    }
                });

                return valveOptions;
            } catch (error) {
                console.error('Error loading Valve options:', error);
                // Fallback from video scenarios
                return [
                    { value: "NONPROP", label: "Non-Proportional" },
                    { value: "3HFDB", label: "3 Bank Hydra Force Double Poppet" }
                ];
            }
        }
    },

    Cab: {
        label: "Select Cab Type",
        type: "select",
        required: true,
        condition: (formState) => formState.Application === 'AG',
        async getOptions(formState) {
            try {
                const response = await fetch('./data/Option_Lists/OptionList_Cab.xml');
                if (!response.ok) throw new Error('Failed to load Cab options');

                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");

                const cabOptions = [];
                const values = xmlDoc.querySelectorAll('Value');

                values.forEach(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;

                    if (code && description) {
                        cabOptions.push({ value: code, label: description });
                    }
                });

                return cabOptions;
            } catch (error) {
                console.error('Error loading Cab options:', error);
                // Fallback from video scenarios
                return [
                    { value: "CREW", label: "Crew Cab" },
                    { value: "REG", label: "Regular Cab" },
                    { value: "EXT", label: "Extended Cab" }
                ];
            }
        }
    },

    Chassis: {
        label: "Select Chassis Type",
        type: "select",
        required: true,
        condition: (formState) => formState.Application === 'AG',
        async getOptions(formState) {
            try {
                const response = await fetch('./data/Option_Lists/OptionList_Chassis.xml');
                if (!response.ok) throw new Error('Failed to load Chassis options');

                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");

                const chassisOptions = [];
                const values = xmlDoc.querySelectorAll('Value');

                values.forEach(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;

                    if (code && description) {
                        chassisOptions.push({ value: code, label: description });
                    }
                });

                return chassisOptions;
            } catch (error) {
                console.error('Error loading Chassis options:', error);
                // Fallback from video scenarios
                return [
                    { value: "BOX", label: "Box Take-Off" }
                ];
            }
        }
    },

    ACC_Hose: {
        label: "Do you want auxiliary hoses?",
        type: "boolean",
        required: true,
        condition: (formState) => formState.Application === 'AG' && formState.Valve &&
            ['3HF', '3HFDB', '3BCL', '4BCL'].includes(formState.Valve)
    },

    Aux_Hoses: {
        label: "Choose Auxiliary Hoses",
        type: "select",
        required: true,
        condition: (formState) => formState.ACC_Hose === true,
        async getOptions(formState) {
            try {
                const response = await fetch('./data/Option Lists/OptionList Aux_Hoses.xml');
                if (!response.ok) throw new Error('Failed to load Aux_Hoses options');

                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");

                const auxOptions = [];
                const values = xmlDoc.querySelectorAll('Value');

                values.forEach(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;

                    if (code && description) {
                        auxOptions.push({ value: code, label: description });
                    }
                });

                return auxOptions;
            } catch (error) {
                console.error('Error loading Aux_Hoses options:', error);
                return [
                    { value: "AUX1", label: "Standard Auxiliary Hoses" }
                ];
            }
        }
    },

    Control: {
        label: "Select Control Option",
        type: "select",
        required: true,
        condition: (formState) => formState.Application === 'AG',
        async getOptions(formState) {
            try {
                const response = await fetch('./data/Option_Lists/OptionList_Control.xml');
                if (!response.ok) throw new Error('Failed to load Control options');

                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");

                const controlOptions = [];
                const values = xmlDoc.querySelectorAll('Value');

                values.forEach(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;
                    const groups = Array.from(value.querySelectorAll('Group')).map(g => g.getAttribute('Name'));

                    if (code && description) {
                        // Filter based on product type (660 vs others)
                        const group = formState.Product === '660' ? '1' : '0';
                        const shouldInclude = groups.includes(group);

                        if (shouldInclude) {
                            controlOptions.push({ value: code, label: description });
                        }
                    }
                });

                return controlOptions;
            } catch (error) {
                console.error('Error loading Control options:', error);
                // Fallback from video scenarios
                return [
                    { value: "WIRELESS", label: "Wireless Remote" }
                ];
            }
        }
    },

    Harness: {
        label: "Select Your Adapter",
        type: "select",
        required: true,
        condition: (formState) => formState.Application === 'AG',
        async getOptions(formState) {
            // Skip matrix lookup if required fields aren't present
            if (!formState.Make || !formState.Year) {
                console.debug('Skipping harness matrix lookup - missing required fields');
                return [{ value: "Standard Harness", label: "Standard Harness" }];
            }

            // Get harness from HarnessMatrix - normalize Make to match matrix values
            let normalizedMake = formState.Make;
            if (formState.Make === 'Ford') normalizedMake = 'FD';
            else if (formState.Make === 'Dodge') normalizedMake = 'DG';
            else if (formState.Make === 'Chevrolet') normalizedMake = 'CH';

            console.debug('Looking up harness with:', {
                Year: formState.Year,
                Make: normalizedMake,
                Chassis: formState.Chassis || 'Unknown'
            });

            const harnessValue = await evaluateMatrixValue('HarnessMatrix', {
                'Year =': formState.Year,
                'Make =': normalizedMake,
                'Chassis =': formState.Chassis || ''
            }, { defaultValue: null });

            if (harnessValue) {
                return [{ value: harnessValue, label: harnessValue }];
            }

            // Fallback based on make and year
            const harnessMap = {
                'Ford': "Harness, FD Box Delete 2023+",
                'DG': "Harness Adapter, Dodge Box Delete 2019+"
            };

            const defaultHarness = harnessMap[formState.Make] || "Standard Harness";
            return [{ value: defaultHarness, label: defaultHarness }];
        }
    }
};

// Helper functions for normalization

// Convert UI vehicle type to matrix format
function normalizeVehicleType(uiVehicleType) {
    return uiVehicleType === 'TRUCK' ? 'OT' : uiVehicleType;
}

// Convert application type to matrix format
function normalizeApplicationType(application) {
    return application === 'AG' ? 'B' : 'O';
}

// Parse and normalize pump options from XML
function parsePumpOptions(xmlText, pumpGroups) {
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlText, "text/xml");
    const pumpOptions = [];

    // Convert comma-separated string to array if needed
    const groupsToCheck = typeof pumpGroups === 'string' ? pumpGroups.split(',') :
        Array.isArray(pumpGroups) ? pumpGroups : [pumpGroups];

    xmlDoc.querySelectorAll('Value').forEach(value => {
        const code = value.querySelector('Property[Name="Value"]')?.textContent;
        const description = value.querySelector('Property[Name="Description"]')?.textContent;
        const groups = Array.from(value.querySelectorAll('Group')).map(g => g.getAttribute('Name'));

        if (code && description) {
            // Check if pump belongs to any of the allowed groups
            const shouldInclude = !pumpGroups || groupsToCheck.some(group => groups.includes(group));

            if (shouldInclude) {
                // Ensure consistent value format using XGL notation
                const normalizedCode = code.endsWith('GL') ? code : `${code}GL`;
                pumpOptions.push({
                    value: normalizedCode,
                    label: description.includes('Gal') ? description : `${code} Gal Pump`
                });
            }
        }
    });

    // Sort by pump size numerically
    return pumpOptions.sort((a, b) => {
        const sizeA = parseInt(a.value.replace(/[^\d]/g, ''));
        const sizeB = parseInt(b.value.replace(/[^\d]/g, ''));
        return sizeA - sizeB;
    });
}

// Helper function to get field groups for display organization
function getFieldGroups() {
    return {
        truckSelections: {
            title: "Truck Selections",
            fields: ['Application', 'AGKitLessMiniPack', 'Make', 'Macfit', 'Year', 'Vehicle_Type', 'Engine', 'Pump', 'Kit', 'Clutch', 'Adapter']
        },
        agQuestions: {
            title: "AG Questions",
            fields: ['Product', 'Valve', 'Cab', 'Chassis', 'ACC_Hose', 'Aux_Hoses', 'Control', 'Harness'],
            condition: function (formState) { return formState.Application === 'AG'; }
        },
        commercialQuestions: {
            title: "Commercial Questions",
            fields: ['Product', 'Cab', 'Chassis', 'Control', 'Harness'],
            condition: function (formState) { return formState.Application === 'COM'; }
        }
    };
}

// Helper function to determine if a field should be visible
function isFieldVisible(fieldName, formState) {
    const field = fields[fieldName];
    if (!field) return false;

    if (field.condition) {
        return field.condition(formState);
    }

    return true;
}

// Export all module functions
export {
    fields,
    getFieldGroups,
    isFieldVisible,
    parsePumpOptions
};
