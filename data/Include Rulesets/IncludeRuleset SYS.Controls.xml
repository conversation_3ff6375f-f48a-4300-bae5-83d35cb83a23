<IncludeRuleset Namespace="SYS" Name="Controls" EntityId="0a307b80-6da0-4108-bfed-05487fdb4ba6">
  <Tags />
  <ComponentAttributes />
  <RuleTree Type="IncludeRuleset">
    <Rule Namespace="SYS" Ruleset="Controls" ChildType="true" RuleTypeName="Variable" Caption="Wired" ConditionExpression="" Enabled="false">
      <vars>
        <var name="BigMatrix">
          <value>=lookupmatrix("GroupIndex")</value>
        </var>
        <var name="Wired">
          <value>=BigMatrix["W"]</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="Controls" ChildType="true" RuleTypeName="Detail" Caption="&lt; New Detail Rule &gt;" ConditionExpression="" Enabled="true">
        <Details>
          <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Wired Control" Visible="=True" Value="=Wired" />
        </Details>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Controls" ChildType="true" RuleTypeName="Screen" Caption="Ask Control" ConditionExpression="" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="ControlType" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
      <Rule Namespace="SYS" Ruleset="Controls" ChildType="true" RuleTypeName="Variable" Caption="Wireless" ConditionExpression="=ControlType=True" Enabled="true">
        <vars>
          <var name="BigMatrix">
            <value>=lookupmatrix("GroupIndex")</value>
          </var>
          <var name="ControlType_sel">
            <value>=BigMatrix["C"]</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Controls" ChildType="true" RuleTypeName="Detail" Caption="Wireless Details" ConditionExpression="" Enabled="true">
          <Details>
            <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Wireless Remote" Visible="=True" Value="=ControlType_sel" />
            <Detail Category="PriceDetails" PrintCode="" PrintSequence="11" Description="C) Wireless Remote, Option" Visible="=True" Value="=Wireless_Price" />
          </Details>
        </Rule>
      </Rule>
    </Rule>
  </RuleTree>
</IncludeRuleset>