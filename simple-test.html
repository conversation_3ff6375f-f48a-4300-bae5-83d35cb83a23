<!DOCTYPE html>
<html>
<head>
    <title>Simple Matrix Test</title>
</head>
<body>
    <h1>Simple Matrix Test</h1>
    <button onclick="runTest()">Run Test</button>
    <div id="result"></div>

    <script type="module">
        async function runTest() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                // Test direct fetch first
                console.log('Testing direct fetch...');
                const response = await fetch('./data/Matrices/Matrix_MultipleAdapterGroups.xml');
                console.log('Fetch response:', response.status, response.statusText);
                
                if (!response.ok) {
                    resultDiv.innerHTML = `❌ Fetch failed: ${response.status} ${response.statusText}`;
                    return;
                }
                
                const text = await response.text();
                console.log('XML text length:', text.length);
                console.log('XML preview:', text.substring(0, 200));
                
                // Test XML parsing
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");
                
                const parseError = xmlDoc.querySelector('parsererror');
                if (parseError) {
                    resultDiv.innerHTML = `❌ XML parse error: ${parseError.textContent}`;
                    return;
                }
                
                const matrixElement = xmlDoc.querySelector('Matrix');
                if (!matrixElement) {
                    resultDiv.innerHTML = '❌ No Matrix element found';
                    return;
                }
                
                const dimensions = Array.from(matrixElement.querySelectorAll('Dimension'));
                const values = Array.from(matrixElement.querySelectorAll('Value'));
                
                console.log('Dimensions found:', dimensions.length);
                console.log('Values found:', values.length);
                
                // Look for our specific entry
                let foundMatch = false;
                for (const value of values) {
                    const lookups = Array.from(value.querySelectorAll('Lookup')).map(l => l.textContent);
                    if (lookups[0] === '0612' && lookups[1] === '9GL' && lookups[2] === 'B') {
                        const result = value.querySelector('Value')?.textContent;
                        resultDiv.innerHTML = `✅ Found match! Result: ${result}`;
                        foundMatch = true;
                        break;
                    }
                }
                
                if (!foundMatch) {
                    resultDiv.innerHTML = '❌ No matching entry found for 0612, 9GL, B';
                    // Show first few entries for debugging
                    const sampleEntries = values.slice(0, 5).map(v => {
                        const lookups = Array.from(v.querySelectorAll('Lookup')).map(l => l.textContent);
                        const result = v.querySelector('Value')?.textContent;
                        return { lookups, result };
                    });
                    console.log('Sample entries:', sampleEntries);
                }
                
            } catch (error) {
                console.error('Test error:', error);
                resultDiv.innerHTML = `❌ Error: ${error.message}`;
            }
        }
        
        window.runTest = runTest;
    </script>
</body>
</html>
