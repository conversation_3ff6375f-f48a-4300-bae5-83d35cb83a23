<OptionList Name="Turf_Deck" Description="Turf Deck" ImageSubFolder="">
  <Property Name="Value" DataType="String" DefaultValue="" Sequence="1" />
  <Property Name="Description" DataType="String" DefaultValue="" Sequence="2" />
  <Property Name="Order" DataType="Number" DefaultValue="1" Sequence="3" />
  <Property Name="Visible" DataType="Boolean" DefaultValue="True" Sequence="4" />
  <Property Name="Locked" DataType="Boolean" DefaultValue="False" Sequence="5" />
  <Property Name="Tooltip" DataType="String" DefaultValue="" Sequence="6" />
  <Property Name="ImageLink" DataType="String" DefaultValue="" Sequence="7" />
  <Property Name="Comments" DataType="String" DefaultValue="" Sequence="8" />
  <Group Name="A" />
  <Group Name="B" />
  <Value>
    <Property Name="Value">VC</Property>
    <Property Name="Description">VC Deck</Property>
    <Property Name="Order">1</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments"></Property>
    <Group Name="B" />
  </Value>
  <Value>
    <Property Name="Value">FINGER</Property>
    <Property Name="Description">Finger Reel</Property>
    <Property Name="Order">1</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments"></Property>
    <Group Name="A" />
    <Group Name="B" />
  </Value>
  <Value>
    <Property Name="Value">BROOM</Property>
    <Property Name="Description">Broom Deck</Property>
    <Property Name="Order">1</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments"></Property>
    <Group Name="A" />
    <Group Name="B" />
  </Value>
  <Tags />
</OptionList>