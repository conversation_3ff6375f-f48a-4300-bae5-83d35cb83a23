// sys-butler-fields.js
// This file defines the SYS.Butler component attributes as JS objects for dynamic form rendering
export const componentAttributes = [
    { Name: "Arms", DataType: "String", Caption: "Arm Length", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Arm_Length", DisplayType: "TypeableDropDown" },
    { Name: "Axle", DataType: "String", Caption: "Axle", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Axle", DisplayType: "TypeableDropDown" },
    { Name: "BallType", DataType: "String", Caption: "5th Wheel Ball", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Ball", DisplayType: "TypeableDropDown" },
    { Name: "Bed", DataType: "String", Caption: "Choose a bed", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Beds", DisplayType: "TypeableDropDown" },
    { Name: "BedFeature", DataType: "String", Caption: "Bed Feature", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Bed_Feature", DisplayType: "DropDown" },
    { Name: "BedType", DataType: "String", Caption: "Bed Type", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Bed_Type", DisplayType: "TypeableDropDown" },
    { Name: "CabAxle", DataType: "String", Caption: "Cab and Chassis or Bed Length", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Cab_Axle", DisplayType: "TypeableDropDown" },
    { Name: "ControlType", DataType: "Boolean", Caption: "Wireless Control?", IsLocked: false, IsVisible: true, IsRequired: true, DisplayType: "RadioButtonHorizontal" },
    { Name: "Headache", DataType: "String", Caption: "Headache Rack", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Headache_Rack", DisplayType: "TypeableDropDown" },
    { Name: "HydPowerFlat", DataType: "Boolean", Caption: "Power Unit?", IsLocked: false, IsVisible: true, IsRequired: true, DisplayType: "RadioButtonHorizontal" },
    { Name: "HydPwrUnit", DataType: "String", Caption: "Hydraulic Power Unit", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Hydraulic_Unit", DisplayType: "TypeableDropDown" },
    { Name: "ISFord", DataType: "Boolean", Caption: "Is Ford?", IsLocked: false, IsVisible: true, IsRequired: true, DisplayType: "RadioButtonHorizontal" },
    { Name: "LHToolbox", DataType: "String", Caption: "Left Hand Toolbox", IsLocked: false, IsVisible: true, IsRequired: false, OptionListID: "Toolboxes", DisplayType: "TypeableDropDown" },
    { Name: "Make", DataType: "String", Caption: "Make", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Make", DisplayType: "TypeableDropDown" },
    { Name: "MntTabs", DataType: "Boolean", Caption: "Mounting Tabs (Ford Only)?", IsLocked: false, IsVisible: true, IsRequired: true, DisplayType: "RadioButtonHorizontal" },
    { Name: "MountKit", DataType: "Boolean", Caption: "Bed Mounting Kit?", IsLocked: false, IsVisible: true, IsRequired: true, DisplayType: "RadioButtonHorizontal" },
    { Name: "Outlets", DataType: "Boolean", Caption: "Outlets?", IsLocked: false, IsVisible: true, IsRequired: true, DisplayType: "RadioButtonHorizontal" },
    { Name: "RHToolbox", DataType: "String", Caption: "Right Hand Toolbox", IsLocked: false, IsVisible: true, IsRequired: false, OptionListID: "Toolboxes", DisplayType: "TypeableDropDown" },
    { Name: "RubRails", DataType: "Boolean", Caption: "Rub Rails?", IsLocked: false, IsVisible: true, IsRequired: true, DisplayType: "RadioButtonHorizontal" },
    { Name: "SideRails", DataType: "Boolean", Caption: "Side Rails?", IsLocked: false, IsVisible: true, IsRequired: true, DisplayType: "RadioButtonHorizontal" },
    { Name: "Skirted", DataType: "Boolean", Caption: "Skirted Bed?", IsLocked: false, IsVisible: true, IsRequired: true, DisplayType: "RadioButtonHorizontal" },
    { Name: "Spikes", DataType: "String", Caption: "Spikes", IsLocked: false, IsVisible: true, IsRequired: false, OptionListID: "Spikes", DisplayType: "TypeableDropDown" },
    { Name: "SpinLength", DataType: "String", Caption: "Spinners", IsLocked: false, IsVisible: true, IsRequired: false, OptionListID: "Spinners", DisplayType: "TypeableDropDown" },
    { Name: "Tailboard", DataType: "Boolean", Caption: "Receiver Hitch?", IsLocked: false, IsVisible: true, IsRequired: true, DisplayType: "RadioButtonHorizontal" },
    { Name: "TruckConfig", DataType: "Boolean", Caption: "Know what bed you want?", IsLocked: false, IsVisible: true, IsRequired: true, DisplayType: "RadioButtonHorizontal" },
    { Name: "Wireharness", DataType: "Boolean", Caption: "Wire Harness?", IsLocked: false, IsVisible: true, IsRequired: true, DisplayType: "RadioButtonHorizontal" },
    { Name: "WorkLights", DataType: "Boolean", Caption: "Work Lights?", IsLocked: false, IsVisible: true, IsRequired: true, DisplayType: "RadioButtonHorizontal" },
    { Name: "XBed", DataType: "String", Caption: "Across the Bed Toolbox", IsLocked: false, IsVisible: true, IsRequired: false, OptionListID: "Toolboxes", DisplayType: "TypeableDropDown" },
    { Name: "Years", DataType: "String", Caption: "Truck Year", IsLocked: false, IsVisible: true, IsRequired: true, OptionListID: "Years", DisplayType: "DropDown" },
    { Name: "ButlerAdapter", DataType: "String", Caption: "Butler Adapter", IsLocked: false, IsVisible: true, IsRequired: false, OptionListID: "ButlerAdapter", DisplayType: "TypeableDropDown" }
    // ...add more fields as needed from the Butler ruleset...
];

// Show only the first field initially, then reveal each subsequent field only after the previous one is filled out
export function getVisibleFields(formData) {
    // Always show the first visible field, then reveal each next after previous is filled
    const firstIdx = getFirstVisibleFieldIndex();
    let visibleCount = 0;
    for (let i = 0; i < componentAttributes.length; i++) {
        const field = componentAttributes[i];
        if (!field.IsVisible) continue;
        if (i === firstIdx || (formData[field.Name] !== undefined && formData[field.Name] !== null && formData[field.Name] !== "")) {
            visibleCount++;
        } else {
            break;
        }
    }
    let count = 0;
    return componentAttributes.map((field, i) => {
        if (!field.IsVisible) return false;
        if (count < visibleCount) {
            count++;
            return true;
        }
        return false;
    });
}
