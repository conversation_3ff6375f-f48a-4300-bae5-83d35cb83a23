<Ruleset Namespace="SYS" Name="*ALL" EntityId="3bb0239b-45fa-422f-87ca-af79e2ea3791">
  <Tags />
  <Ruleset Namespace="SYS" Name="*700CPK" DefaultPageCaption="Truck Selections" EntityId="6f1a5024-490e-4212-810c-5f76435667e2">
    <Tags />
    <Ruleset Namespace="SYS" Name="700CPK" EntityId="ee4257ed-d4d5-4160-9aab-7eef52278869">
      <Tags />
    </Ruleset>
  </Ruleset>
  <ComponentAttributes>
    <ComponentAttribute Namespace="SYS" Ruleset="*ALL" Name="Application" DataType="String" Caption="Application" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="0" IsRequired="true" DefaultGroup="" OptionListID="Application" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="*ALL" Name="Year" DataType="String" Caption="" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="0" IsRequired="true" DefaultGroup="" OptionListID="Years" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="*ALL" Name="Vehicle_Type" DataType="String" Caption="" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="0" IsRequired="true" DefaultGroup="" OptionListID="Vehicle_Type" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="*ALL" Name="Make" DataType="String" Caption="" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="0" IsRequired="true" DefaultGroup="" OptionListID="Make" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="*ALL" Name="Engine" DataType="String" Caption="" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="0" IsRequired="true" DefaultGroup="" OptionListID="Engine" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="*ALL" Name="Pump" DataType="String" Caption="" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="0" IsRequired="true" DefaultGroup="" OptionListID="Pump" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="*ALL" Name="Kit" DataType="String" Caption="" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="0" IsRequired="true" DefaultGroup="" OptionListID="Kit" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="*ALL" Name="Clutch" DataType="String" Caption="" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="0" IsRequired="true" DefaultGroup="" OptionListID="Clutch" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="*ALL" Name="Adapter" DataType="String" Caption="" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="0" IsRequired="true" DefaultGroup="" OptionListID="Adapter" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="*ALL" Name="Product" DataType="String" Caption="" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="0" IsRequired="true" DefaultGroup="" OptionListID="Product" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="*ALL" Name="Cab" DataType="String" Caption="" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="0" IsRequired="true" DefaultGroup="" OptionListID="Cab" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonVertical" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="*ALL" Name="Chassis" DataType="String" Caption="" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="0" IsRequired="true" DefaultGroup="" OptionListID="Chassis" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonVertical" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="*ALL" Name="Reservoir" DataType="Boolean" Caption="" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="0" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonVertical" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="*ALL" Name="Hoses660" DataType="Boolean" Caption="" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="0" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonVertical" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="*ALL" Name="Valve" DataType="String" Caption="" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="0" IsRequired="true" DefaultGroup="" OptionListID="Valve" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="*ALL" Name="ACC_Hose" DataType="Boolean" Caption="" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="0" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonVertical" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="*ALL" Name="Aux_Hoses" DataType="String" Caption="" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="0" IsRequired="true" DefaultGroup="" OptionListID="Aux_Hoses" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="*ALL" Name="Control" DataType="String" Caption="" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="0" IsRequired="true" DefaultGroup="" OptionListID="Control" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="*ALL" Name="Harness" DataType="String" Caption="" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="0" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="TextBox" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="*700CPK" Name="AGKitLessMiniPack" DataType="Boolean" Caption="" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="0" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="CheckBox" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="*700CPK" Name="ValveYN" DataType="Boolean" Caption="" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="0" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="CheckBox" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="*700CPK" Name="Macfit" DataType="Boolean" Caption="Click info link select yes if kti will  fit:" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="/bulletin/IB7042201Mack.pdf" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
  </ComponentAttributes>
  <RuleTree Type="Interactive">
    <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Variable" Caption="Select Database" ConditionExpression="" Enabled="true">
      <vars>
        <var name="ApDB">
          <value>=IF(sys.Application IN {"SalesPortal", "SYTELINE_PROD"},"HARPER_APP","PILOT_APP")</value>
        </var>
        <var name="SL_BILLTO_ID">
          <value>=IF(sys.Application &lt;&gt; "SALES BUYDESIGN","SL_BILLTO","SYS_BILLTO_ID")</value>
        </var>
      </vars>
    </Rule>
    <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Variable" Caption="Global Default Variables" ConditionExpression="" Enabled="true">
      <vars>
        <var name="ConfiguredPrice">
          <value>=0</value>
        </var>
      </vars>
    </Rule>
    <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Variable" Caption="Set Default Variables" ConditionExpression="=sys.Application in {&quot;Syteline&quot;,&quot;SytelineProd&quot;,&quot;SalesPortal&quot;}" Enabled="true">
      <vars>
        <var name="HeaderID">
          <value>=Left(sys.headerid,Length(sys.headerid) - 2)</value>
        </var>
        <var name="CustCode">
          <value>=usr.GetCustCode</value>
        </var>
        <var name="FirstPrice">
          <value>=usr.GetFirstPrice</value>
        </var>
        <var name="Markup">
          <value>=IF(FirstPrice &gt; 0,TRUE,FALSE)</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="false" RuleTypeName="Variable" Caption="Set Default Variables" ConditionExpression="" Enabled="true">
        <vars>
          <var name="FirstPrice">
            <value>=0</value>
          </var>
          <var name="CustCode">
            <value>=usr.GetCustCodeDev</value>
          </var>
          <var name="Markup">
            <value>=FALSE</value>
          </var>
        </vars>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Screen" Caption="Select Application and Pump" ConditionExpression="" Enabled="true">
      <Screen Title="=&quot;Truck &amp; System Specifications&quot;" ShowTitle="=True" ShowLine="=True" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
        <ScreenOption Sequence="1" Name="Application" AutoContinue="=2" OptionListID="Application" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
      <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Screen" Caption="AG Kit Less Mini Pack" ConditionExpression="=Application = &quot;AG&quot;" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
          <ScreenOption Sequence="1" Name="AGKitLessMiniPack" Caption="Less AG Mini Pack?" AutoContinue="=2" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Variable" Caption="Filter Application Option List Group" ConditionExpression="" Enabled="true">
      <vars>
        <var name="Make.OptionListGroup">
          <value>=IF(Application ="AG","B","O")</value>
        </var>
        <var name="App">
          <value>=IF(Application IN{"COM","OTHER","WRK"},"C","B")</value>
        </var>
        <var name="AG">
          <value>=IF(Application ="AG","B","O")</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Screen" Caption="Select Year" ConditionExpression="" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
          <ScreenOption Sequence="1" Name="Year" Caption="=&quot;Enter 4 Digit Truck Year&quot;" AutoContinue="=2" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
      </Rule>
      <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Screen" Caption="Select Make" ConditionExpression="" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
          <ScreenOption Sequence="1" Name="Make" Caption="=&quot;Select Make of Vehicle&quot;" AutoContinue="=2" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Condition" Caption="Mac Special Condition" ConditionExpression="=Make=&quot;MC&quot;" Enabled="true">
          <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Screen" Caption="Does Mac Work" ConditionExpression="" Enabled="true">
            <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
              <ScreenOption Sequence="1" Name="Macfit" SelectOptionsImageLinkFieldExpression="ImageLink" />
            </Screen>
          </Rule>
          <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Message" Caption="Critical Failure " ConditionExpression="=Macfit=false" Enabled="true">
            <Message Value="Will Not FIt" MessageLevel="1" Title="" MessageStyle="Default" />
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Variable" Caption="Call VT_Filter Matrix" ConditionExpression="" Enabled="true">
      <vars>
        <var name="Yr_NUM">
          <value>=ToNumber(Year)</value>
        </var>
        <var name="VT_Filter">
          <value>=LookUpMatrix("VT_Filter",Make, Yr_NUM)</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Variable" Caption="Filter Vehicle Type" ConditionExpression="=Make IN{&quot;FD&quot;,&quot;GM&quot;,&quot;SP&quot;} And (Application &lt;&gt; &quot;AG&quot;) And VT_Filter = &quot;ZZ&quot;" Enabled="true">
        <vars>
          <var name="VT_Filter">
            <value>="3"</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="false" RuleTypeName="Variable" Caption="VT_Filter = &quot;NOVAN&quot;" ConditionExpression="=VT_Filter = &quot;ZZ&quot;" Enabled="true">
          <vars>
            <var name="VT_Filter">
              <value>="NOVAN"</value>
            </var>
          </vars>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Screen" Caption="Select Vehicle Type" ConditionExpression="" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
        <ScreenOption Sequence="1" Name="Vehicle_Type" Caption="=&quot;Select Vehicle Type&quot;" AutoContinue="=2" OptionListGroup="=VT_Filter" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
    </Rule>
    <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Variable" Caption="Set Engine Matrix Call for &quot;AG&quot;" ConditionExpression="" Enabled="true">
      <vars>
        <var name="AppZ">
          <value>=IF(Application="AG","B","O")</value>
        </var>
      </vars>
    </Rule>
    <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Variable" Caption="Set Engine Group for &quot;B&quot; in AppZ" ConditionExpression="" Enabled="true">
      <vars>
        <var name="EngineGroup">
          <value>=LookupMatrix("MultipleEngineGroups", Make, Vehicle_Type, Year)</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Message" Caption="NoEngine = True" ConditionExpression="=EngineGroup = &quot;ZZ&quot; or EngineGroup = &quot;&quot;" Enabled="true">
        <Message Value="=&quot;No Engine Kit Available. Contact Sales at 800-835-1042&quot;" MessageLevel="1" Title="" MessageStyle="" />
      </Rule>
      <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Variable" Caption="Set Engine Option List Group" ConditionExpression="" Enabled="true">
        <vars>
          <var name="Engine.OptionListGroup">
            <value>=If(Search(",",EngineGroup) &gt; 0, GroupUnion(split(EngineGroup,",")),EngineGroup)</value>
          </var>
        </vars>
      </Rule>
      <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Screen" Caption="Select Engine" ConditionExpression="" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
          <ScreenOption Sequence="1" Name="Engine" Caption="=&quot;Select Engine&quot;" AutoContinue="=2" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Variable" Caption="Set Pump Group" ConditionExpression="" Enabled="true">
      <vars>
        <var name="Pump.OptionListGroup">
          <value>=GroupUnion(Split(LookupMatrix("PumpGroupMatrix",Engine,AppZ,Vehicle_Type,Year),","))</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Screen" Caption="Select Pump" ConditionExpression="" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
          <ScreenOption Sequence="1" Name="Pump" Caption="=&quot;Select Pump Size&quot;" AutoContinue="=2" OptionListID="Pump_1T1" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Variable" Caption="Call Kit_Group Matrix" ConditionExpression="" Enabled="true">
      <vars>
        <var name="Kit.OptionListGroup">
          <value>=GroupUnion(Split(LookUpMatrix("Kit_Group",Engine, Year, Pump, Vehicle_Type, App),"-"))</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Message" Caption="Kit = NA" ConditionExpression="=isNullorBlank(kit.optionlistgroup) or kit.optionlistgroup = &quot;NA&quot;" Enabled="true">
        <Message Value="=&quot;No kit exists. Please contact Harper.&quot;" MessageLevel="1" Title="" MessageStyle="" />
      </Rule>
      <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Screen" Caption="Select Kit" ConditionExpression="" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
          <ScreenOption Sequence="1" Name="Kit" Caption="=&quot;Select Your Kit&quot;" AutoContinue="=2" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Variable" Caption="Set Pump_PartNumber" ConditionExpression="" Enabled="true">
          <vars>
            <var name="Pump_PartNumber">
              <value>=LookupMatrix("Pump_Selection")</value>
            </var>
          </vars>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Variable" Caption="Call Part Number and Clutch Filter Matrices" ConditionExpression="" Enabled="true">
      <vars>
        <var name="Pump_PartNumber">
          <value>=LookupMatrix("Pump_Selection")</value>
        </var>
        <var name="Clutch_Filter_Pre">
          <value>=LookupMatrix("Clutch_Group")</value>
        </var>
        <var name="Clutch_Exceptions">
          <value>=LookupMatrix("HD_Clutch")</value>
        </var>
        <var name="Clutch_Filter">
          <value>=IF(Clutch_Exceptions = "",Clutch_Filter_Pre,Clutch_Exceptions)</value>
        </var>
      </vars>
    </Rule>
    <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Screen" Caption="Select Clutch" ConditionExpression="" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
        <ScreenOption Sequence="1" Name="Clutch" Caption="=&quot;Select Your Clutch&quot;" AutoContinue="=2" OptionListGroup="=Clutch_Filter" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
    </Rule>
    <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Variable" Caption="Set Adapter Group" ConditionExpression="" Enabled="true">
      <vars>
        <var name="Temp">
          <value>=Substring(kit,3,length(kit)-1)</value>
        </var>
        <var name="AdapterGroup">
          <value>=LookupMatrix("MultipleAdapterGroups",Substring(kit,3,length(kit)-1),Pump,AG)</value>
        </var>
        <var name="Adapter.OptionListGroup">
          <value>=If(Search(",",AdapterGroup) &gt; 0, GroupUnion(split(AdapterGroup,",")),AdapterGroup)</value>
        </var>
      </vars>
    </Rule>
    <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Screen" Caption="Adapter Selection" ConditionExpression="" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
        <ScreenOption Sequence="1" Name="Adapter" Caption="=&quot;Select Adapter&quot;" AutoContinue="=2" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
      <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Message" Caption="Warning Message if Kit = 700455" ConditionExpression="=Kit = &quot;700455&quot;" Enabled="true">
        <Message Value="=&quot;There are additional modifications that need to be made for this kit to fit. Please contact DewEze customer service for instructions.&quot;" MessageLevel="4" Title="" MessageStyle="" />
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Variable" Caption="Set Pricing Rules" ConditionExpression="=AGKitLessMiniPack != True" Enabled="true">
      <vars>
        <var name="Kit_Price">
          <value>=LookupMatrix("Pricing_Kit")</value>
        </var>
        <var name="Pump_Price">
          <value>=LookupMatrix("Pricing_Pump", Pump_PartNumber)</value>
        </var>
        <var name="Clutch_Price">
          <value>=LookupMatrix("Pricing_Clutch")</value>
        </var>
        <var name="Adapter_Price">
          <value>=LookupMatrix("Pricing_Adapter")</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Condition" Caption="If Customer Code has Value" ConditionExpression="=CustCode &lt;&gt; &quot;&quot;" Enabled="true">
        <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Variable" Caption="If Markup is True" ConditionExpression="=Markup = TRUE" Enabled="true">
          <vars>
            <var name="Kit_Price">
              <value>=Kit_Price * (1+(FirstPrice/100))</value>
            </var>
            <var name="Pump_Price">
              <value>=Pump_Price * (1+(FirstPrice/100))</value>
            </var>
            <var name="Clutch_Price">
              <value>=Clutch_Price * (1+(FirstPrice/100))</value>
            </var>
            <var name="Adapter_Price">
              <value>=Adapter_Price * (1+(FirstPrice/100))</value>
            </var>
            <var name="Total_Price1">
              <value>=Kit_Price + Pump_Price + Clutch_Price + Adapter_Price</value>
            </var>
            <var name="ConfiguredPrice">
              <value>=Total_Price1</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Detail" Caption="Display Base Price" ConditionExpression="" Enabled="false">
            <Details>
              <Detail Category="PriceDetails" PrintCode="" PrintSequence="0" Description="Base Price" Visible="=True" Value="=Total_Price1" />
            </Details>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Variable" Caption="If Markup is False" ConditionExpression="=Markup = FALSE" Enabled="true">
          <vars>
            <var name="Kit_Price">
              <value>=Kit_Price</value>
            </var>
            <var name="Pump_Price">
              <value>=Pump_Price</value>
            </var>
            <var name="Clutch_Price">
              <value>=Clutch_Price</value>
            </var>
            <var name="Adapter_Price">
              <value>=Adapter_Price</value>
            </var>
            <var name="Total_Price1">
              <value>=((Kit_Price + Pump_Price + Clutch_Price + Adapter_Price) * ((100 - abs(FirstPrice)) / 100))</value>
            </var>
            <var name="ConfiguredPrice">
              <value>=Total_Price1</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Detail" Caption="Display Base Price" ConditionExpression="" Enabled="false">
            <Details>
              <Detail Category="PriceDetails" PrintCode="" PrintSequence="0" Description="Base Price" Visible="=True" Value="=Total_Price1" />
            </Details>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Screen" Caption="Call AG" ConditionExpression="=Application = &quot;AG&quot;" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=True" PageCaption="AG Questions" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" />
      <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Include Ruleset" Caption="Call AG Product Ruleset" ConditionExpression="" Enabled="true">
        <RuleSet namespace="SYS" type="SR" rulesetid="AG Products" />
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Condition" Caption="Begin Manufacturing Rules" ConditionExpression="" Enabled="true">
      <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Variable" Caption="Set Smart Part" ConditionExpression="" Enabled="true">
        <vars>
          <var name="ConfigurationCode">
            <value>="SMART" + Kit + "CPK"</value>
          </var>
        </vars>
      </Rule>
      <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="MFG Component" Caption="Manufacturing Component" ConditionExpression="" Enabled="true">
        <Parameters>
          <Parameter Key="Component ID" Value="=sys.partnumber" Description="Unique ID for this Job" IsRequired="true" IsSystem="true" DataType="String" />
          <Parameter Key="Parent Component ID" Value="" Description="Parent BOM Component ID (Leave Blank for top level)" IsRequired="false" IsSystem="true" DataType="String" />
          <Parameter Key="Print Code" Value="I" Description="I for Internal, E for External" IsRequired="true" IsSystem="true" DataType="String" />
          <Parameter Key="Config Component Sequence" Value="=ToString(ComponentSequence)" Description="" IsRequired="false" IsSystem="true" DataType="String" />
          <Parameter Key="Part Number" Value="=sys.partnumber" Description="Required for Sub Jobs.  Part Number must exist in SyteLine." IsRequired="false" IsSystem="true" DataType="String" />
          <Parameter Key="Quantity" Value="=input.quantity" Description="Required for SubJobs" IsRequired="false" IsSystem="true" DataType="Number" />
          <Parameter Key="Operation No" Value="" Description="Valid Operation No From Parent Job/Not Required for Top Level Job" IsRequired="false" IsSystem="true" DataType="String" />
          <Parameter Key="job.contains_tax_free_matl" Value="=null" Description="Syteline Schema Mapping to job. contains_tax_free_matl field." IsRequired="false" IsSystem="false" DataType="Number" />
          <Parameter Key="job.description" Value="" Description="Syteline Schema Mapping to job. description field." IsRequired="false" IsSystem="false" DataType="String" />
          <Parameter Key="job.export_type" Value="" Description="Syteline Schema Mapping to job. export_type field." IsRequired="false" IsSystem="false" DataType="String" />
          <Parameter Key="job.midnight_of_job_sch_compdate" Value="" Description="Syteline Schema Mapping to job. midnight_of_job_sch_compdate field." IsRequired="false" IsSystem="false" DataType="String" />
          <Parameter Key="job.midnight_of_job_sch_end_date" Value="" Description="Syteline Schema Mapping to job. midnight_of_job_sch_end_date field." IsRequired="false" IsSystem="false" DataType="String" />
          <Parameter Key="job.rcpt_rqmt" Value="" Description="Syteline Schema Mapping to job. rcpt_rqmt field." IsRequired="false" IsSystem="false" DataType="String" />
          <Parameter Key="job.rework" Value="=null" Description="Syteline Schema Mapping to job. rework field." IsRequired="false" IsSystem="false" DataType="Number" />
          <Parameter Key="job.scheduled" Value="=null" Description="Syteline Schema Mapping to job. scheduled field." IsRequired="false" IsSystem="false" DataType="Number" />
          <Parameter Key="job.unlinked_xref" Value="=null" Description="Syteline Schema Mapping to job. unlinked_xref field." IsRequired="false" IsSystem="false" DataType="Number" />
          <Parameter Key="job.whse" Value="" Description="Syteline Schema Mapping to job. whse field." IsRequired="false" IsSystem="false" DataType="String" />
          <Parameter Key="job.text" Value="" Description="Syteline Schema Mapping to job. text field." IsRequired="false" IsSystem="false" DataType="String" />
          <Parameter Key="ConfigurationCode" Value="=ConfigurationCode" Description="Syteline Schema Mapping to IPN field." IsRequired="false" IsSystem="true" DataType="String" />
          <Parameter Key="co.pricecode" Value="=CustCode" Description="" IsRequired="false" IsSystem="false" DataType="String" />
          <Parameter Key="co.inv_freq" Value="=&quot;D&quot;" Description="" IsRequired="false" IsSystem="false" DataType="String" />
          <Parameter Key="Plant ID" Value="=sys.namespace" Description="" IsRequired="false" IsSystem="false" DataType="String" />
        </Parameters>
        <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="MFG Operation" Caption="Operation 10 - Shipping" ConditionExpression="" Enabled="true">
          <Parameters>
            <Parameter Key="Component or Material ID" Value="=sys.partnumber" Description="Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="Operation No" Value="=&quot;10&quot;" Description="Required" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="Operation Name" Value="" Description="Operation Name or Work Center ID" IsRequired="false" IsSystem="true" DataType="String" />
            <Parameter Key="Operation Description" Value="" Description="Description of the Operation" IsRequired="false" IsSystem="true" DataType="String" />
            <Parameter Key="Work Center ID" Value="600" Description="Valid Work Center ID.  Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="Print Code" Value="I" Description="I for Internal, E for External" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="jrt_sch.run_lbr_hrs" Value="=.4" Description="Used for &quot;H&quot; Hours/Piece --- Enter Hours" IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.pcs_per_lbr_hr" Value="=0" Description="Used for &quot;P&quot; Piece/Hour --- Enter Pieces" IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.bflush_type" Value="" Description="Syteline Schema Mapping to jobroute. bflush_type field." IsRequired="false" IsSystem="false" DataType="String" />
            <Parameter Key="jobroute.cntrl_point" Value="=null" Description="Syteline Schema Mapping to jobroute. cntrl_point field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.efficiency" Value="=null" Description="Syteline Schema Mapping to jobroute. efficiency field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.fixovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. fixovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.fovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. fovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.run_basis_lbr" Value="" Description="P = Pieces/Hour or H = Hours/Piece" IsRequired="false" IsSystem="false" DataType="String" />
            <Parameter Key="jobroute.run_basis_mch" Value="" Description="Syteline Schema Mapping to jobroute. run_basis_mch field." IsRequired="false" IsSystem="false" DataType="String" />
            <Parameter Key="jobroute.run_rate_lbr" Value="=null" Description="Syteline Schema Mapping to jobroute. run_rate_lbr field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.setup_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. setup_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.varovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. varovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.vovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. vovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.finish_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. finish_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.move_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. move_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.offset_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. offset_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.pcs_per_mch_hr" Value="=null" Description="Syteline Schema Mapping to jrt_sch. pcs_per_mch_hr field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.queue_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. queue_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.run_mch_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. run_mch_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.sched_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. sched_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.setup_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. setup_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.whenrule" Value="=null" Description="Syteline Schema Mapping to jrt_sch. whenrule field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.text" Value="" Description="Syteline Schema Mapping to jobroute. text field." IsRequired="false" IsSystem="false" DataType="String" />
          </Parameters>
          <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Detail" Caption="Build Bom Display Up" ConditionExpression="" Enabled="true">
            <Details>
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="52" Description="=&quot;---------------------------------------------------&quot;" Visible="=False" Value="" />
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="53" Description="=&quot;BOM&quot;" Visible="=True" Value="" />
            </Details>
          </Rule>
          <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="MFG Material" Caption="Material Rule for Manufacturing" ConditionExpression="=AGKitLessMiniPack != True" Enabled="true">
            <RuleData>
              <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
              <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
              <Column Key="Operation No" ExpressionType="Expression" />
              <Column Key="Plant ID" ExpressionType="Expression" />
              <Column Key="Part Number" ExpressionType="Expression" />
              <Column Key="Quantity" ExpressionType="Expression" />
              <Value>
                <Column Value="1" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;10&quot;" />
                <Column Value="" />
                <Column Value="=kit" />
                <Column Value="1" />
              </Value>
              <Value>
                <Column Value="2" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;10&quot;" />
                <Column Value="" />
                <Column Value="=Pump_PartNumber" />
                <Column Value="1" />
              </Value>
              <Value>
                <Column Value="3" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;10&quot;" />
                <Column Value="" />
                <Column Value="=clutch" />
                <Column Value="1" />
              </Value>
              <Value>
                <Column Value="4" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;10&quot;" />
                <Column Value="" />
                <Column Value="=Adapter" />
                <Column Value="1" />
              </Value>
            </RuleData>
            <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Detail" Caption="Display Materials" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="54" Description="=&quot;Kit&quot;" Visible="=True" Value="=Kit" />
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="55" Description="=&quot;Pump&quot;" Visible="=True" Value="=Pump_PartNumber" />
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="56" Description="=&quot;Clutch&quot;" Visible="=True" Value="=Clutch" />
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="57" Description="=&quot;Adapter&quot;" Visible="=True" Value="=Adapter" />
              </Details>
            </Rule>
          </Rule>
          <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="MFG Material" Caption="Hose, Harness material Product not equal to 660" ConditionExpression="=Product != &quot;660&quot; AND AG = &quot;B&quot;" Enabled="true">
            <RuleData>
              <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
              <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
              <Column Key="Operation No" ExpressionType="Expression" />
              <Column Key="Plant ID" ExpressionType="Expression" />
              <Column Key="Part Number" ExpressionType="Expression" />
              <Column Key="Quantity" ExpressionType="Expression" />
              <Value>
                <Column Value="5" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;10&quot;" />
                <Column Value="" />
                <Column Value="=Hose_PartNumber" />
                <Column Value="1" />
              </Value>
              <Value>
                <Column Value="8" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;10&quot;" />
                <Column Value="" />
                <Column Value="=Harness_PartNumber" />
                <Column Value="1" />
              </Value>
            </RuleData>
            <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Detail" Caption="Display AG Materials" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="58" Description="=&quot;Valve Hose&quot;" Visible="=True" Value="=Hose_PartNumber" />
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="62" Description="=&quot;Harness&quot;" Visible="=True" Value="=Harness_PartNumber" />
              </Details>
            </Rule>
          </Rule>
          <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Condition" Caption="AG Materials" ConditionExpression="=AG = &quot;B&quot;" Enabled="true">
            <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="MFG Material" Caption="Valve Material" ConditionExpression="=Product &lt;&gt; &quot;660&quot;" Enabled="true">
              <RuleData>
                <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                <Column Key="Operation No" ExpressionType="Expression" />
                <Column Key="Plant ID" ExpressionType="Expression" />
                <Column Key="Part Number" ExpressionType="Expression" />
                <Column Key="Quantity" ExpressionType="Expression" />
                <Value>
                  <Column Value="6" />
                  <Column Value="=sys.partnumber" />
                  <Column Value="=&quot;10&quot;" />
                  <Column Value="" />
                  <Column Value="=Valve_PartNumber" />
                  <Column Value="1" />
                </Value>
              </RuleData>
              <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Detail" Caption="Display AG Materials" ConditionExpression="" Enabled="true">
                <Details>
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="59" Description="=&quot;Valve&quot;" Visible="=True" Value="=Valve_PartNumber" />
                </Details>
              </Rule>
            </Rule>
            <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="MFG Material" Caption="Control Material" ConditionExpression="=!IsNullOrBlank(Control)" Enabled="true">
              <RuleData>
                <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                <Column Key="Operation No" ExpressionType="Expression" />
                <Column Key="Plant ID" ExpressionType="Expression" />
                <Column Key="Part Number" ExpressionType="Expression" />
                <Column Key="Quantity" ExpressionType="Expression" />
                <Value>
                  <Column Value="7" />
                  <Column Value="=sys.partnumber" />
                  <Column Value="=&quot;10&quot;" />
                  <Column Value="" />
                  <Column Value="=Control_PartNumber" />
                  <Column Value="1" />
                </Value>
              </RuleData>
              <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Detail" Caption="Display AG Materials" ConditionExpression="" Enabled="true">
                <Details>
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="61" Description="=&quot;Control&quot;" Visible="=True" Value="=Control_PartNumber" />
                </Details>
              </Rule>
            </Rule>
            <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="MFG Material" Caption="AG Material Rules" ConditionExpression="=Acc_Hose = True and !IsNull(Aux_PartNumber)" Enabled="true">
              <RuleData>
                <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                <Column Key="Operation No" ExpressionType="Expression" />
                <Column Key="Plant ID" ExpressionType="Expression" />
                <Column Key="Part Number" ExpressionType="Expression" />
                <Column Key="Quantity" ExpressionType="Expression" />
                <Value>
                  <Column Value="9" />
                  <Column Value="=sys.partnumber" />
                  <Column Value="=&quot;10&quot;" />
                  <Column Value="" />
                  <Column Value="=Aux_PartNumber" />
                  <Column Value="1" />
                </Value>
              </RuleData>
              <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Detail" Caption="Display AG Materials" ConditionExpression="" Enabled="true">
                <Details>
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="63" Description="=&quot;Auxillary Hoses&quot;" Visible="=True" Value="=Aux_PartNumber" />
                </Details>
              </Rule>
            </Rule>
          </Rule>
          <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Condition" Caption="If 660Hoses" ConditionExpression="=Hoses660 = TRUE and Product = &quot;660&quot;" Enabled="true">
            <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="MFG Material" Caption="Hoses660 Part" ConditionExpression="" Enabled="true">
              <RuleData>
                <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                <Column Key="Operation No" ExpressionType="Expression" />
                <Column Key="Plant ID" ExpressionType="Expression" />
                <Column Key="Part Number" ExpressionType="Expression" />
                <Column Key="Quantity" ExpressionType="Expression" />
                <Value>
                  <Column Value="13" />
                  <Column Value="=sys.partnumber" />
                  <Column Value="=&quot;10&quot;" />
                  <Column Value="" />
                  <Column Value="=Hoses660_PartNumber" />
                  <Column Value="1" />
                </Value>
              </RuleData>
              <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Detail" Caption="Display AG Materials" ConditionExpression="" Enabled="true">
                <Details>
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="64" Description="=&quot;Valve Hose&quot;" Visible="=True" Value="=Hoses660_PartNumber" />
                </Details>
              </Rule>
            </Rule>
          </Rule>
          <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="MFG Material" Caption="Valve Part Number" ConditionExpression="=Product = &quot;660&quot; and ValveYN" Enabled="true">
            <RuleData>
              <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
              <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
              <Column Key="Operation No" ExpressionType="Expression" />
              <Column Key="Plant ID" ExpressionType="Expression" />
              <Column Key="Part Number" ExpressionType="Expression" />
              <Column Key="Quantity" ExpressionType="Expression" />
              <Value>
                <Column Value="6" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;10&quot;" />
                <Column Value="" />
                <Column Value="=Valve_PartNumber" />
                <Column Value="1" />
              </Value>
            </RuleData>
            <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Detail" Caption="Display Reservoir Materials" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="65" Description="=&quot;Valve Part Number&quot;" Visible="=True" Value="=Valve_PartNumber" />
              </Details>
            </Rule>
          </Rule>
          <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="MFG Material" Caption="Reservoir Part Number" ConditionExpression="=Product = &quot;660&quot; and Reservoir" Enabled="true">
            <RuleData>
              <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
              <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
              <Column Key="Operation No" ExpressionType="Expression" />
              <Column Key="Plant ID" ExpressionType="Expression" />
              <Column Key="Part Number" ExpressionType="Expression" />
              <Column Key="Quantity" ExpressionType="Expression" />
              <Value>
                <Column Value="14" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;10&quot;" />
                <Column Value="" />
                <Column Value="=Res_PartNumber" />
                <Column Value="1" />
              </Value>
            </RuleData>
            <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Detail" Caption="Display Reservoir Materials" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="65" Description="=&quot;Reservoir Part&quot;" Visible="=True" Value="=Res_PartNumber" />
              </Details>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="*700CPK" ChildType="true" RuleTypeName="Detail" Caption="Display List Price" ConditionExpression="" Enabled="true">
      <Details>
        <Detail Category="OrderDetails" PrintCode="" PrintSequence="98" Description="=&quot;---------------------------------------------------------&quot;" Visible="=True" Value="" />
        <Detail Category="PriceDetails" PrintCode="" PrintSequence="99" Description="=&quot;Total List Price&quot;" Visible="=True" Value="=ConfiguredPrice / (1+(FirstPrice/100))" />
        <Detail Category="PriceDetails" PrintCode="" PrintSequence="100" Description="=&quot;Total Discounted Price&quot;" Visible="=True" Value="=ConfiguredPrice" />
      </Details>
    </Rule>
  </RuleTree>
  <RuleTree Type="PostComponent" />
  <RuleTree Type="PostConfiguration" />
</Ruleset>