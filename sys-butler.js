import { fields, getFieldGroups, isFieldVisible } from './butler-manufacturing-fields.js';

// Initialize state
let formState = {};

// Helper function for field validation
function validateField(fieldName, value, fieldDef) {
  if (!value && fieldDef.required) {
    return { isValid: false, message: `${fieldDef.label} is required` };
  }
  return { isValid: true };
}

// Update debug panel
function updateDebugPanel() {
  const debugContent = document.getElementById('debug-content');
  if (debugContent) {
    const visibleFields = Object.keys(fields).filter(fieldName => isFieldVisible(fieldName, formState));

    debugContent.innerHTML = `
      Form State: ${JSON.stringify(formState)}<br>
      Visible Fields: ${visibleFields.join(', ')}<br>
      Total Fields: ${Object.keys(fields).length}
    `;
  }
}

// Update display fields with calculated values
async function updateDisplayFields() {
  const displayFields = document.querySelectorAll('[data-display-field="true"]');

  console.log(`Found ${displayFields.length} display fields to update`);
  updateDebugPanel();

  for (const displayElement of displayFields) {
    const fieldName = displayElement.dataset.fieldName;
    const fieldDef = fields[fieldName];

    console.log(`Updating display field: ${fieldName}`);

    if (fieldDef && fieldDef.getValue && typeof fieldDef.getValue === 'function') {
      try {
        // Show loading state
        displayElement.textContent = 'Calculating...';
        displayElement.classList.add('loading');

        // Get the calculated value
        const value = await fieldDef.getValue(formState);

        // Update the display
        displayElement.textContent = value || 'Not available';
        displayElement.classList.remove('loading');

        console.log(`Updated display field ${fieldName}:`, value);
      } catch (error) {
        console.error(`Error updating display field ${fieldName}:`, error);
        displayElement.textContent = 'Error calculating value';
        displayElement.classList.remove('loading');
        displayElement.classList.add('error');
      }
    } else {
      console.warn(`Display field ${fieldName} has no getValue function`);
    }
  }
}

// Create input element for a field
async function createInput(fieldName, fieldDef, options = []) {
  const container = document.createElement('div');
  container.className = 'input-container';
  container.id = `${fieldName}-container`;

  // Create label
  const label = document.createElement('label');
  label.htmlFor = fieldName;
  label.textContent = fieldDef.label;
  if (fieldDef.required) {
    label.textContent += ' *';
  }
  container.appendChild(label);

  // Create input element
  let input;
  if (fieldDef.type === 'select') {
    input = document.createElement('select');
    input.name = fieldName;
    input.id = fieldName;
    input.required = fieldDef.required;

    // Add loading state
    input.disabled = true;
    const loadingOption = document.createElement('option');
    loadingOption.value = '';
    loadingOption.textContent = 'Loading options...';
    input.appendChild(loadingOption);

    // Use either provided dynamic options or static options
    const optionsToUse = options.length > 0 ? options : (fieldDef.options || []);

    // Clear loading state and add options
    input.innerHTML = '';
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = `Select ${fieldDef.label}`;
    input.appendChild(defaultOption);

    optionsToUse.forEach(opt => {
      const option = document.createElement('option');
      option.value = opt.value;
      option.textContent = opt.label || opt.value;
      input.appendChild(option);
    });
    input.disabled = false;

  } else if (fieldDef.type === 'boolean') {
    const wrapper = document.createElement('div');
    wrapper.className = 'checkbox-wrapper';

    input = document.createElement('input');
    input.type = 'checkbox';
    input.name = fieldName;
    input.id = fieldName;

    const checkboxLabel = document.createElement('span');
    checkboxLabel.className = 'checkbox-label';
    checkboxLabel.textContent = fieldDef.checkboxLabel || 'Yes';

    wrapper.appendChild(input);
    wrapper.appendChild(checkboxLabel);
    container.appendChild(wrapper);
    return container;

  } else if (fieldDef.type === 'display') {
    // Handle display fields that show calculated values
    input = document.createElement('div');
    input.className = 'display-field';
    input.id = fieldName;
    input.textContent = 'Calculating...';

    // Mark as display field for special handling
    input.dataset.displayField = 'true';
    input.dataset.fieldName = fieldName;

  } else {
    input = document.createElement('input');
    input.type = fieldDef.type || 'text';
    input.name = fieldName;
    input.id = fieldName;
    input.required = fieldDef.required;

    if (fieldDef.pattern) {
      input.pattern = fieldDef.pattern;
    }
    if (fieldDef.minLength) {
      input.minLength = fieldDef.minLength;
    }
    if (fieldDef.maxLength) {
      input.maxLength = fieldDef.maxLength;
    }
  }

  container.appendChild(input);
  return container;
}



// Render the form based on current state
async function renderForm() {
  const container = document.getElementById('configurator');
  if (!container) return;

  // Clear existing content
  container.innerHTML = '';

  // Create form element
  const form = document.createElement('form');
  form.id = 'butler-form';
  form.className = 'configurator-form';
  container.appendChild(form);

  // Get field groups
  const fieldGroups = getFieldGroups();

  // Render each group
  for (const [groupName, fieldNames] of Object.entries(fieldGroups)) {
    const groupContainer = document.createElement('div');
    groupContainer.className = 'field-group';

    const groupTitle = document.createElement('h3');
    groupTitle.textContent = groupName;
    groupContainer.appendChild(groupTitle);

    let hasVisibleFields = false;

    // Render fields in this group
    for (const fieldName of fieldNames) {
      const fieldDef = fields[fieldName];
      if (!fieldDef) continue;

      // Check if field should be visible
      if (!isFieldVisible(fieldName, formState)) continue;

      hasVisibleFields = true;

      try {
        let options = [];

        // Get dynamic options if field has getOptions function
        if (fieldDef.getOptions && typeof fieldDef.getOptions === 'function') {
          console.log(`Loading options for ${fieldName}...`);
          options = await fieldDef.getOptions(formState);
        }

        // Create input element
        const inputContainer = await createInput(fieldName, fieldDef, options);
        groupContainer.appendChild(inputContainer);

        // Restore field value if it exists in form state
        const fieldElement = inputContainer.querySelector(`#${fieldName}`);
        if (fieldElement && formState[fieldName] !== undefined) {
          if (fieldElement.type === 'checkbox') {
            fieldElement.checked = formState[fieldName];
          } else {
            fieldElement.value = formState[fieldName];
          }
        }

      } catch (error) {
        console.error(`Error rendering field ${fieldName}:`, error);

        // Show error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.textContent = `Error loading ${fieldDef.label}`;
        groupContainer.appendChild(errorDiv);
      }
    }

    // Only add group if it has visible fields
    if (hasVisibleFields) {
      form.appendChild(groupContainer);
    }
  }

  console.log('Form rendered with state:', formState);
}

// Update form state when a field changes
async function updateFormState(fieldName, value) {
  console.log(`🔧 Updating field: ${fieldName} = ${value}`);

  // Update the state for the changed field
  formState[fieldName] = value;

  // Update debug panel
  updateDebugPanel();

  // Clear dependent fields when a parent field changes
  const dependentFields = getDependentFields(fieldName);
  dependentFields.forEach(depField => {
    if (formState[depField] !== undefined) {
      console.log(`🔧 Clearing dependent field: ${depField}`);
      delete formState[depField];
    }
  });

  console.log('🔧 Re-rendering form with state:', formState);
  // Re-render form to reflect changes
  await renderForm();

  // Update display fields with calculated values
  await updateDisplayFields();

  // Log visible fields after render
  const visibleFields = Object.keys(fields).filter(fieldName => {
    const fieldDef = fields[fieldName];
    return !fieldDef.condition || fieldDef.condition(formState);
  });
  console.log('🔧 Visible fields after render:', visibleFields);

  // Restore focus to the updated field
  const updatedFieldContainer = document.getElementById(`${fieldName}-container`);
  if (updatedFieldContainer) {
    const inputElement = updatedFieldContainer.querySelector('input, select');
    if (inputElement) {
      inputElement.focus();
    }
  }

  return true;
}

// Get fields that depend on the given field
function getDependentFields(fieldName) {
  // Define dependency relationships
  const dependencies = {
    'Make': ['Years', 'Axle', 'CabAxle', 'BedType', 'MountKit', 'ButlerAdapter'],
    'Years': ['Axle', 'CabAxle', 'BedType', 'MountKit', 'ButlerAdapter'],
    'Axle': ['CabAxle', 'BedType', 'MountKit', 'ButlerAdapter'],
    'CabAxle': ['BedType', 'MountKit', 'ButlerAdapter'],
    'BedType': ['MountKit', 'ButlerAdapter']
  };

  return dependencies[fieldName] || [];
}

// Initialize the configurator
async function initializeConfigurator() {
  console.log('🚀 Initializing Butler Manufacturing Configurator');

  // Initial render
  await renderForm();

  // Update display fields after initial render
  await updateDisplayFields();
}

// Handle field changes
async function handleFieldChange(event) {
  const fieldName = event.target.name || event.target.id;
  let value;

  if (event.target.type === 'checkbox') {
    value = event.target.checked;
  } else {
    value = event.target.value;
  }

  console.log(`🔧 Field changed: ${fieldName} = ${value}`);

  // Update form state and re-render
  await updateFormState(fieldName, value);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
  console.log('🌟 DOM loaded, starting Butler configurator...');

  // Initialize the configurator
  await initializeConfigurator();

  // Attach event listeners for form changes
  document.addEventListener('change', async (event) => {
    if (event.target.closest('#butler-form')) {
      await handleFieldChange(event);
    }
  });

  // Also handle input events for immediate feedback
  document.addEventListener('input', async (event) => {
    if (event.target.closest('#butler-form') && event.target.type !== 'checkbox') {
      // Debounce input events
      clearTimeout(event.target.inputTimeout);
      event.target.inputTimeout = setTimeout(async () => {
        await handleFieldChange(event);
      }, 300);
    }
  });

  console.log('🎉 Butler configurator initialized successfully');
});
