<IncludeRuleset Namespace="SYS" Name="Truck" EntityId="b2f67a0b-b53f-4cd0-bed8-f221a992dcc8">
  <Tags />
  <ComponentAttributes />
  <RuleTree Type="IncludeRuleset">
    <Rule Namespace="SYS" Ruleset="Truck" ChildType="true" RuleTypeName="Screen" Caption="Ask Make" ConditionExpression="" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="Make" OptionListID="Make" OptionListGroup="B" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Truck" ChildType="true" RuleTypeName="Screen" Caption="Ask Year" ConditionExpression="" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="Years" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Truck" ChildType="true" RuleTypeName="Screen" Caption="Ask Axle" ConditionExpression="" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="Axle" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Truck" ChildType="true" RuleTypeName="Screen" Caption="Ask Bed Length" ConditionExpression="" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="CabAxle" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
    </Rule>
  </RuleTree>
</IncludeRuleset>