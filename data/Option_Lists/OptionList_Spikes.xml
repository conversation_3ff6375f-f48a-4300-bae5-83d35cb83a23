<OptionList Name="Spikes">
  <Property Name="Value" DataType="String" DefaultValue="" Sequence="1" />
  <Property Name="Description" DataType="String" DefaultValue="" Sequence="2" />
  <Property Name="Order" DataType="Number" DefaultValue="1" Sequence="3" />
  <Property Name="Visible" DataType="Boolean" DefaultValue="True" Sequence="4" />
  <Property Name="Locked" DataType="Boolean" DefaultValue="False" Sequence="5" />
  <Property Name="Tooltip" DataType="String" DefaultValue="" Sequence="6" />
  <Property Name="ImageLink" DataType="String" DefaultValue="" Sequence="7" />
  <Property Name="Comments" DataType="String" DefaultValue="" Sequence="8" />
  <Property Name="Price" DataType="Number" DefaultValue="" Sequence="0" />
  <Property Name="Part Number" DataType="String" DefaultValue="" Sequence="0" />
  <Value>
    <Property Name="Value">BR</Property>
    <Property Name="Description">Black Spike</Property>
    <Property Name="Order">1</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments"></Property>
    <Property Name="Price">0</Property>
    <Property Name="Part Number"></Property>
  </Value>
  <Value>
    <Property Name="Value">RF</Property>
    <Property Name="Description">Red Spike</Property>
    <Property Name="Order">1</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments"></Property>
    <Property Name="Price">60.00</Property>
    <Property Name="Part Number"></Property>
  </Value>
  <Tags />
</OptionList>