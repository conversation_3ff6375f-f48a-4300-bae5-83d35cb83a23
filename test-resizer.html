<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Resizer Test</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: Arial, sans-serif;
      background: #1e1e1e;
      color: #fff;
      height: 100vh;
      overflow: hidden;
    }
    
    .container {
      display: flex;
      height: 100vh;
      width: 100vw;
    }
    
    .left-pane {
      flex: 1;
      background: #2d2d30;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 200px;
    }
    
    .right-pane {
      flex: 1;
      background: #fff;
      color: #333;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 200px;
    }
    
    .resizer {
      width: 8px;
      background: #3e3e42;
      cursor: col-resize;
      position: relative;
      transition: background-color 0.2s;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .resizer:hover {
      background: #007acc;
    }
    
    .resizer.dragging {
      background: #007acc;
    }
    
    .resizer::after {
      content: '⋮';
      color: rgba(255,255,255,0.6);
      font-size: 18px;
      line-height: 1;
      pointer-events: none;
    }
    
    .resizer:hover::after {
      color: rgba(255,255,255,0.9);
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="left-pane">
      <h2>Left Pane</h2>
    </div>
    
    <div class="resizer" id="resizer"></div>
    
    <div class="right-pane">
      <h2>Right Pane</h2>
    </div>
  </div>
  
  <script>
    let isResizing = false;
    let startX = 0;
    let startWidth = 0;
    
    const resizer = document.getElementById('resizer');
    const container = document.querySelector('.container');
    const leftPane = document.querySelector('.left-pane');
    const rightPane = document.querySelector('.right-pane');
    
    resizer.addEventListener('mousedown', (e) => {
      isResizing = true;
      startX = e.clientX;
      startWidth = leftPane.offsetWidth;
      
      resizer.classList.add('dragging');
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
      
      console.log('Start resize:', { startX, startWidth });
    });
    
    document.addEventListener('mousemove', (e) => {
      if (!isResizing) return;
      
      const deltaX = e.clientX - startX;
      const newWidth = startWidth + deltaX;
      const containerWidth = container.offsetWidth;
      const minWidth = 200;
      const maxWidth = containerWidth - minWidth - 8; // 8px for resizer
      
      if (newWidth >= minWidth && newWidth <= maxWidth) {
        leftPane.style.width = newWidth + 'px';
        rightPane.style.width = (containerWidth - newWidth - 8) + 'px';
      }
      
      console.log('Resizing:', { deltaX, newWidth, containerWidth });
    });
    
    document.addEventListener('mouseup', () => {
      if (isResizing) {
        isResizing = false;
        resizer.classList.remove('dragging');
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
        console.log('Stop resize');
      }
    });
  </script>
</body>
</html>
