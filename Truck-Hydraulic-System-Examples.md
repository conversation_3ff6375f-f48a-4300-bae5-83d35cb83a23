# Truck Hydraulic System Configurator - Examples and Use Cases

## Complete Configuration Example

This example walks through the exact scenario you provided, showing how each field is processed:

### User Input Scenario
- **Application**: Agriculture
- **Less AG Mini Pack**: No (Unchecked)
- **Make**: Ford
- **Year**: 2024
- **Vehicle Type**: Truck
- **Engine**: Ford 6.7L Diesel
- **Pump**: 9 Gal Pump
- **Kit**: 700612 Single Alt/1 Sided Belt
- **Clutch**: 8 Groove Ogura "A"

### Step-by-Step Processing

#### Step 1: Application Selection
```
User Input: "Agriculture"
Field Value: "AG"
Matrix Impact: AG flag = "B" for subsequent lookups
Next Fields Enabled: Make, AGKitLessMiniPack
```

#### Step 2: Make Selection
```
Data Source: OptionList_Make.xml
Filtering: Groups with "B" (Agriculture applications)
Available Options: Ford (FD), Dodge (DG), Chevrolet (GM), etc.
User Selection: "FD" (Ford)
Next Fields Enabled: Year, Vehicle_Type
```

#### Step 3: Year Selection
```
Data Source: Programmatically generated (2000-2025)
User Selection: "2024"
Matrix Impact: Used in all subsequent compatibility checks
```

#### Step 4: Vehicle Type Selection
```
Matrix: VT_Filter
Matrix Inputs: {Make: "FD", Year: "2024", Application: "AG"}
Matrix Result: "3" (allows both Truck and Van)
Available Options: Truck, Van
User Selection: "TRUCK"
Normalized Value: "OT" for matrix lookups
```

#### Step 5: Engine Selection
```
Matrix: MultipleEngineGroups
Matrix Inputs: {Make: "FD", Vehicle_Type: "OT", Year: "2024"}
Matrix Result: "FD67D" (engine group)
Data Source: OptionList_Engine.xml
Filtering: Engines in group "FD67D"
Available Options: Ford 6.7L Diesel (FD67D), etc.
User Selection: "FD67D"
```

#### Step 6: Pump Selection
```
Matrix: PumpGroupMatrix
Matrix Inputs: {Engine: "FD67D", AppZ: "B", Vehicle_Type: "OT", Year: "2024"}
Matrix Result: "7GL,9GL,12GL,17GL" (comma-separated pump groups)
Data Source: OptionList_Pump_1T1.xml
Filtering: Pumps in groups 7GL, 9GL, 12GL, 17GL
Available Options: 7 Gal Pump, 9 Gal Pump, 12 Gal Pump, 17 Gal Pump
User Selection: "9GL"
```

#### Step 7: Kit Selection
```
Matrix: Kit_Group
Matrix Inputs: {Engine: "FD67D", Year: "2024", Pump: "9GL", Vehicle_Type: "OT", App: "B"}
Matrix Result: "620-612" (dash-separated kit groups)
Group Normalization: ["620", "612"]
Data Source: OptionList_Kit.xml
Filtering: Kits in groups 620 or 612
Available Options: 700612 Single Alt/1 Sided Belt, etc.
User Selection: "700612"
```

#### Step 8: Clutch Selection
```
Matrix: Clutch_Group (with HD_Clutch override check)
Matrix Inputs: Current form state
Matrix Result: "8GRV" (clutch group)
Data Source: OptionList_Clutch.xml
Filtering: Clutches in group "8GRV"
Available Options: 8 Groove Ogura "A" (8GRVA), etc.
User Selection: "8GRVA"
```

#### Step 9: Adapter Calculation (Automatic)
```
Matrix: MultipleAdapterGroups
Matrix Inputs: {
  "Kit (Skip first 4)": "0612",  // Last 4 digits of "700612"
  "Pump": "9GL",
  "AG": "B"
}
Matrix Result: "03-51261" (adapter code)

Option List Lookup: OptionList_Adapter.xml
Search For: Value = "03-51261"
Found Description: "03-51261; S=16HB-16MB90; P=10MJ-12MB45"
Final Result: "03-51261; S=16HB-16MB90; P=10MJ-12MB45"
```

## Matrix Lookup Examples

### Example 1: Engine Group Lookup
```xml
<!-- Matrix_MultipleEngineGroups.xml -->
<Value>
  <Lookup>FD</Lookup>      <!-- Make -->
  <Lookup>OT</Lookup>      <!-- Vehicle_Type -->
  <Lookup>2024</Lookup>    <!-- Year -->
  <Value>FD67D</Value>     <!-- Engine Group -->
</Value>
```

### Example 2: Pump Group Lookup
```xml
<!-- Matrix_PumpGroupMatrix.xml -->
<Value>
  <Lookup>FD67D</Lookup>   <!-- Engine -->
  <Lookup>B</Lookup>       <!-- AppZ (AG flag) -->
  <Lookup>OT</Lookup>      <!-- Vehicle_Type -->
  <Lookup>2024</Lookup>    <!-- Year -->
  <Value>7GL,9GL,12GL,17GL</Value>  <!-- Pump Groups -->
</Value>
```

### Example 3: Adapter Lookup
```xml
<!-- Matrix_MultipleAdapterGroups.xml -->
<Value>
  <Lookup>0612</Lookup>    <!-- Kit (last 4 digits) -->
  <Lookup>9GL</Lookup>     <!-- Pump -->
  <Lookup>B</Lookup>       <!-- AG flag -->
  <Value>03-51261</Value>  <!-- Adapter Code -->
</Value>
```

## Option List Examples

### Example 1: Engine Option
```xml
<!-- OptionList_Engine.xml -->
<Value>
  <Property Name="Value">FD67D</Property>
  <Property Name="Description">Ford 6.7L Diesel</Property>
  <Group Name="FD67D" />
</Value>
```

### Example 2: Adapter Option with S=/P= Values
```xml
<!-- OptionList_Adapter.xml -->
<Value>
  <Property Name="Value">03-51261</Property>
  <Property Name="Description">03-51261; S=16HB-16MB90; P=10MJ-12MB45</Property>
  <Group Name="03-51261" />
</Value>
```

## Common Error Scenarios

### Scenario 1: No Engine Options Available
```
Problem: Engine field shows no options
Cause: MultipleEngineGroups matrix returns no match for Make/Year/Vehicle_Type
Solution: Check matrix for exact input values, verify normalization
Debug: Look for matrix entry with Make="FD", Vehicle_Type="OT", Year="2024"
```

### Scenario 2: Adapter Shows Only Code
```
Problem: Adapter shows "03-51261" instead of full specification
Cause: OptionList_Adapter.xml lookup failed
Solution: Verify adapter code exists in option list with proper description
Debug: Search OptionList_Adapter.xml for Value="03-51261"
```

### Scenario 3: Kit Field Empty
```
Problem: Kit field shows no options after pump selection
Cause: Kit_Group matrix returns no matching groups
Solution: Check matrix inputs, verify group normalization in option list
Debug: Verify Kit_Group matrix has entry for Engine/Year/Pump/Vehicle_Type/App combination
```

## Testing Scenarios

### Test Case 1: Ford Agriculture 2024
```javascript
const testState = {
  Application: 'AG',
  Make: 'FD',
  Year: '2024',
  Vehicle_Type: 'TRUCK',
  Engine: 'FD67D',
  Pump: '9GL',
  Kit: '700612',
  Clutch: '8GRVA'
};
// Expected Adapter: "03-51261; S=16HB-16MB90; P=10MJ-12MB45"
```

### Test Case 2: Commercial Freightliner 2024
```javascript
const testState = {
  Application: 'COM',
  Make: 'FL',
  Year: '2024',
  Vehicle_Type: 'TRUCK',
  Engine: 'FLM2C',
  Pump: '12GL',
  Kit: '700607',
  Clutch: '6GRVA'
};
// Expected Adapter: "03-6923; S=20MB90; P=12MJ-12MB90"
```

### Test Case 3: Edge Case - No Matrix Match
```javascript
const testState = {
  Application: 'AG',
  Make: 'UNKNOWN',
  Year: '1999',
  Vehicle_Type: 'TRUCK'
};
// Expected: Fallback to hardcoded options
```

## Debugging Tools

### Console Logging
Enable debug logging by setting `debug: true` in matrix evaluation options:
```javascript
const result = await evaluateMatrixValue('MultipleAdapterGroups', inputs, {
  debug: true
});
```

### Browser Developer Tools
1. Open Network tab to monitor XML file loading
2. Check Console for matrix evaluation logs
3. Use Application tab to inspect cached matrices
4. Monitor form state changes in real-time

### Debug Panel
The configurator includes a debug panel showing:
- Current form state
- Adapter field visibility status
- Field condition evaluation results
- Matrix lookup parameters and results
