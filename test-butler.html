<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Butler Configurator Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .result {
            background: #e8f5e8;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .error {
            background: #ffe8e8;
            color: #d00;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <h1>Butler Configurator Test</h1>
    
    <div class="test-section">
        <h2>Test Configuration</h2>
        <p><strong>Make:</strong> Ford (FD)</p>
        <p><strong>Year:</strong> 2024</p>
        <p><strong>Axle:</strong> Dual Rear Wheel (D)</p>
        <p><strong>Cab/Axle:</strong> Long Bed 56" (LB56)</p>
        <p><strong>Bed Type:</strong> Regular/Flat (R)</p>
    </div>

    <div class="test-section">
        <h2>Matrix Lookup Tests</h2>
        <button onclick="testMountKitLookup()">Test Mount Kit Lookup</button>
        <button onclick="testAdapterLookup()">Test Adapter Lookup</button>
        <div id="test-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>Field Tests</h2>
        <button onclick="testFieldDefinitions()">Test Field Definitions</button>
        <button onclick="testOptionLists()">Test Option Lists</button>
        <div id="field-result" class="result" style="display: none;"></div>
    </div>

    <script type="module">
        import { evaluateMatrixValue } from './matrix-loader.js';
        import { fields } from './butler-manufacturing-fields.js';

        window.testMountKitLookup = async function() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.style.display = 'block';
            
            try {
                const testState = {
                    Make: 'FD',
                    Years: '2024',
                    Axle: 'D',
                    CabAxle: 'LB56',
                    BedType: 'R'
                };

                console.log('Testing mount kit lookup with:', testState);
                
                const mountKit = await evaluateMatrixValue('ButlerMKOpt', {
                    'Make': testState.Make,
                    'Year': testState.Years,
                    'Axle': testState.Axle,
                    'Cab_Axle': testState.CabAxle,
                    'Bed_Type': testState.BedType
                }, { debug: true });
                
                resultDiv.innerHTML = `
                    <h3>Mount Kit Lookup Result:</h3>
                    <strong>Input:</strong> Make=${testState.Make}, Year=${testState.Years}, Axle=${testState.Axle}, CabAxle=${testState.CabAxle}, BedType=${testState.BedType}<br>
                    <strong>Result:</strong> ${mountKit || 'null'}<br>
                    <strong>Status:</strong> ${mountKit ? '✅ Found' : '❌ Not found'}
                `;
                
            } catch (error) {
                console.error('Mount kit test error:', error);
                resultDiv.innerHTML = `<span class="error">❌ Error: ${error.message}</span>`;
            }
        };

        window.testAdapterLookup = async function() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.style.display = 'block';
            
            try {
                const testState = {
                    Make: 'FD',
                    Years: '2024',
                    Axle: 'D',
                    CabAxle: 'LB56',
                    BedType: 'R'
                };

                console.log('Testing adapter lookup with:', testState);
                
                const adapter = await evaluateMatrixValue('ButlerAdapterHarns', {
                    'Make': testState.Make,
                    'Year': testState.Years,
                    'Axle': testState.Axle,
                    'Cab_Axle': testState.CabAxle,
                    'Bed_Type': testState.BedType
                }, { debug: true });
                
                resultDiv.innerHTML += `
                    <h3>Adapter Lookup Result:</h3>
                    <strong>Input:</strong> Make=${testState.Make}, Year=${testState.Years}, Axle=${testState.Axle}, CabAxle=${testState.CabAxle}, BedType=${testState.BedType}<br>
                    <strong>Result:</strong> ${adapter || 'null'}<br>
                    <strong>Status:</strong> ${adapter && adapter !== 'NA' ? '✅ Found' : '❌ Not found or NA'}
                `;
                
            } catch (error) {
                console.error('Adapter test error:', error);
                resultDiv.innerHTML += `<br><span class="error">❌ Error: ${error.message}</span>`;
            }
        };

        window.testFieldDefinitions = async function() {
            const resultDiv = document.getElementById('field-result');
            resultDiv.style.display = 'block';
            
            try {
                const fieldCount = Object.keys(fields).length;
                const fieldNames = Object.keys(fields);
                
                resultDiv.innerHTML = `
                    <h3>Field Definitions Test:</h3>
                    <strong>Total Fields:</strong> ${fieldCount}<br>
                    <strong>Field Names:</strong> ${fieldNames.join(', ')}<br>
                    <strong>Status:</strong> ✅ Fields loaded successfully
                `;
                
            } catch (error) {
                console.error('Field test error:', error);
                resultDiv.innerHTML = `<span class="error">❌ Error: ${error.message}</span>`;
            }
        };

        window.testOptionLists = async function() {
            const resultDiv = document.getElementById('field-result');
            resultDiv.style.display = 'block';
            
            try {
                // Test loading Make options
                const makeField = fields.Make;
                if (makeField && makeField.getOptions) {
                    const makeOptions = await makeField.getOptions();
                    
                    resultDiv.innerHTML += `
                        <h3>Option Lists Test:</h3>
                        <strong>Make Options Count:</strong> ${makeOptions.length}<br>
                        <strong>Sample Make Options:</strong> ${makeOptions.slice(0, 3).map(opt => `${opt.value}:${opt.label}`).join(', ')}<br>
                        <strong>Status:</strong> ✅ Option lists loading successfully
                    `;
                } else {
                    resultDiv.innerHTML += `<br><span class="error">❌ Make field or getOptions not found</span>`;
                }
                
            } catch (error) {
                console.error('Option list test error:', error);
                resultDiv.innerHTML += `<br><span class="error">❌ Error: ${error.message}</span>`;
            }
        };
    </script>
</body>
</html>
