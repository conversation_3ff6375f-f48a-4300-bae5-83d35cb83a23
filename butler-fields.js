// Component attributes for the Butler Manufacturing configurator
export const componentAttributes = {
    BuildingType: {
        name: "BuildingType",
        dataType: "String",
        caption: "Select Building Type",
        isRequired: true,
        isVisible: true,
        displayType: "TypeableDropDown",
        options: [
            { value: "COMMERCIAL", label: "Commercial" },
            { value: "INDUSTRIAL", label: "Industrial" },
            { value: "AGRICULTURAL", label: "Agricultural" }
        ]
    },
    Width: {
        name: "Width",
        dataType: "Number",
        caption: "Building Width (ft)",
        isRequired: true,
        isVisible: true,
        displayType: "Number",
        validation: {
            min: 20,
            max: 300,
            step: 1
        }
    },
    Length: {
        name: "Length",
        dataType: "Number",
        caption: "Building Length (ft)",
        isRequired: true,
        isVisible: true,
        displayType: "Number",
        validation: {
            min: 20,
            max: 600,
            step: 1
        }
    },
    EaveHeight: {
        name: "EaveHeight",
        dataType: "Number",
        caption: "Eave Height (ft)",
        isRequired: true,
        isVisible: true,
        displayType: "Number",
        validation: {
            min: 10,
            max: 40,
            step: 0.5
        }
    },
    RoofPitch: {
        name: "RoofPitch",
        dataType: "String",
        caption: "Roof Pitch",
        isRequired: true,
        isVisible: true,
        displayType: "TypeableDropDown",
        options: [
            { value: "1:12", label: "1:12" },
            { value: "2:12", label: "2:12" },
            { value: "3:12", label: "3:12" },
            { value: "4:12", label: "4:12" }
        ]
    },
    BaySpacing: {
        name: "BaySpacing",
        dataType: "Number",
        caption: "Bay Spacing (ft)",
        isRequired: true,
        isVisible: true,
        displayType: "TypeableDropDown",
        options: [
            { value: 20, label: "20 ft" },
            { value: 25, label: "25 ft" },
            { value: 30, label: "30 ft" }
        ]
    }
};

// Matrix definitions for component filtering
export const matrices = {
    PricingMatrix: {
        name: "PricingMatrix",
        inputs: ["BuildingType", "Width", "Length", "EaveHeight", "RoofPitch"],
        outputFormat: "price-per-sqft"
    },
    BaySpacingMatrix: {
        name: "BaySpacingMatrix",
        inputs: ["Width", "Length"],
        outputFormat: "allowed-spacings"
    }
};

// Pricing matrices
export const pricingMatrices = {
    BasePrice: {
        name: "BasePrice",
        inputs: ["BuildingType", "Width", "Length", "EaveHeight"],
        output: "base-price"
    },
    RoofPitchModifier: {
        name: "RoofPitchModifier",
        input: "RoofPitch",
        output: "multiplier"
    }
};

// Helper function to evaluate conditional expressions
function evaluateCondition(condition, state) {
    try {
        // Replace field references with state lookups
        const evaluatedCondition = condition.replace(/([A-Za-z_][A-Za-z0-9_]*)/g, (match) => {
            return state.hasOwnProperty(match) ? `state['${match}']` : match;
        });

        // Create a function that takes state as a parameter
        const fn = new Function('state', `return ${evaluatedCondition};`);
        return fn(state);
    } catch (e) {
        console.warn('Error evaluating condition:', condition, e);
        return true; // Show field by default if evaluation fails
    }
}

// Helper function to determine which fields should be visible based on the current state
export function getVisibleFields(state = {}) {
    const visibleFields = [];

    for (const [fieldName, field] of Object.entries(componentAttributes)) {
        // Show field if:
        // 1. It has no condition, or
        // 2. Its condition evaluates to true with the current state
        if (!field.condition || evaluateCondition(field.condition, state)) {
            visibleFields.push(fieldName);
        }
    }

    return visibleFields;
}
