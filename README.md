# Harper Configurator System

## Overview

This project contains a comprehensive web-based configurator system with two fully functional configurators for truck hydraulic systems and manufacturing components:

- **Truck Hydraulic System Configurator (SYS-700CPK)**: A matrix-driven configurator for truck hydraulic systems with real-time adapter calculation.
- **Butler Manufacturing Configurator**: A specialized configurator for Butler truck bed systems with mount kit and adapter selection.

Both configurators use a sophisticated **matrix-driven architecture** with XML data sources, progressive field revelation, and real-time calculations.

---

## System Architecture

Both configurators use a **matrix-driven filtering system** where:

1. **Matrices** (`data/Matrices/`) contain compatibility logic tables
2. **Option Lists** (`data/Option_Lists/`) provide component catalogs with descriptions
3. **Field Definitions** define UI behavior and business logic
4. **Matrix Loader** (`matrix-loader.js`) provides the runtime evaluation engine

### Data Flow

```
User Selection → Matrix Lookup → Filter Groups → Option List Filtering → Display Results
```

## Configurator Details

### Truck Hydraulic System Configurator (SYS-700CPK)

- **Files:** `sys-700cpk.html`, `sys-700cpk.js`, `truck-hydraulic-system-fields.js`
- **Purpose:** Configure complete truck hydraulic systems with automatic adapter calculation
- **Key Features:**
  - **Progressive Field Revelation**: Fields appear based on previous selections
  - **Real-time Adapter Calculation**: Automatically calculates adapter with S=/P= specifications
  - **Matrix-driven Compatibility**: Uses 6+ matrices for component filtering
  - **Agriculture-specific Options**: Special fields for AG applications
- **Core Fields:**
  - Application → Make → Year → Vehicle Type → Engine → Pump → Kit → Clutch
  - **Calculated:** Adapter (with full specifications like "03-51261; S=16HB-16MB90; P=10MJ-12MB45")
- **Data Sources:**
  - 15+ matrices including `MultipleAdapterGroups`, `PumpGroupMatrix`, `Kit_Group`
  - 10+ option lists with component catalogs and descriptions

### Butler Manufacturing Configurator

- **Files:** `sys-butler.html`, `sys-butler.js`, `butler-manufacturing-fields.js`
- **Purpose:** Configure Butler truck bed systems with mount kit and adapter selection
- **Key Features:**
  - **Progressive Truck Selection**: Make → Years → Axle → CabAxle → BedType
  - **Automatic Mount Kit Calculation**: Uses `ButlerMKOpt` matrix for compatibility
  - **Multi-Adapter Support**: Handles multiple adapter harnesses with descriptions
  - **Rich Component Descriptions**: Shows part numbers with full specifications
- **Core Fields:**
  - Make → Years → Axle → Cab/Axle → Bed Type
  - **Calculated:** Mount Kit, Butler Adapter (with descriptions)
- **Data Sources:**
  - 2 matrices: `ButlerMKOpt`, `ButlerAdapterHarns`
  - 7 option lists including truck specifications and component catalogs

---

## How to Run the Configurators

1. **Start a local web server** (Python 3 required):

   ```bash
   python -m http.server 8001
   ```

2. **Open your browser** and navigate to:

   - **Main Index**: [http://localhost:8001/index.html](http://localhost:8001/index.html)
   - **Truck Hydraulic System**: [http://localhost:8001/sys-700cpk.html](http://localhost:8001/sys-700cpk.html)
   - **Butler Manufacturing**: [http://localhost:8001/sys-butler.html](http://localhost:8001/sys-butler.html)

3. **Test Suites** (for development/debugging):
   - [http://localhost:8001/test-adapter.html](http://localhost:8001/test-adapter.html) - Truck adapter testing
   - [http://localhost:8001/test-butler.html](http://localhost:8001/test-butler.html) - Butler configurator testing

---

## Key Features

### Real Data Integration

- **XML Data Sources**: All data loaded from XML matrices and option lists
- **Matrix Evaluation Engine**: Sophisticated compatibility checking
- **Dynamic Option Filtering**: Options change based on previous selections
- **Fallback Handling**: Graceful degradation when data is unavailable

### User Experience

- **Progressive Disclosure**: Fields appear as needed
- **Real-time Feedback**: Immediate calculation of results
- **Debug Panels**: Development tools for troubleshooting
- **Responsive Design**: Works on desktop and mobile devices

### Technical Architecture

- **Modular Design**: Each configurator is self-contained
- **Shared Components**: Common matrix loader and utilities
- **Modern JavaScript**: ES6 modules, async/await patterns
- **Comprehensive Documentation**: Detailed technical documentation included

---

## Documentation

Comprehensive documentation is available:

- **`Truck-Hydraulic-System-Data-Architecture.md`** - Complete technical documentation for the truck hydraulic system
- **`Truck-Hydraulic-System-Examples.md`** - Practical examples and use cases
- **`Truck-Hydraulic-System-Quick-Reference.md`** - Quick reference guide
- **`Butler-Manufacturing-Configurator-Documentation.md`** - Butler configurator documentation

---

## Development

### File Structure

```
configurator/
├── index.html                          # Main landing page
├── sys-700cpk.html                     # Truck hydraulic configurator
├── sys-butler.html                     # Butler manufacturing configurator
├── matrix-loader.js                    # Shared matrix evaluation engine
├── truck-hydraulic-system-fields.js    # Truck configurator field definitions
├── butler-manufacturing-fields.js      # Butler configurator field definitions
├── data/
│   ├── Matrices/                       # Compatibility logic tables
│   └── Option_Lists/                   # Component catalogs
└── docs/                              # Documentation files
```

### Adding New Configurators

1. Create field definitions following the established pattern
2. Define matrices and option lists for your domain
3. Use the shared matrix loader for consistency
4. Follow the progressive field revelation pattern

---

For questions, technical support, or customization requests, contact the project maintainer.
