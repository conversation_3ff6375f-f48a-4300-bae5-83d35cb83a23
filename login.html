<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Login - Harper Configurator</title>
  <link rel="stylesheet" href="sys-700cpk.css">
  <style>
    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    .login-container {
      background: white;
      padding: 3rem;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      width: 100%;
      max-width: 400px;
      text-align: center;
    }
    
    .login-header {
      margin-bottom: 2rem;
    }
    
    .login-header h1 {
      color: #2a3a4a;
      margin: 0 0 0.5rem 0;
      font-size: 2rem;
      font-weight: 600;
    }
    
    .login-header p {
      color: #6b7280;
      margin: 0;
    }
    
    .form-group {
      margin-bottom: 1.5rem;
      text-align: left;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 0.5rem;
      color: #374151;
      font-weight: 500;
    }
    
    .form-group input {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      font-size: 1rem;
      transition: border-color 0.2s, box-shadow 0.2s;
      box-sizing: border-box;
    }
    
    .form-group input:focus {
      outline: none;
      border-color: #2563eb;
      box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }
    
    .login-btn {
      width: 100%;
      background: #2563eb;
      color: white;
      border: none;
      padding: 0.875rem;
      border-radius: 6px;
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s;
      margin-bottom: 1rem;
    }
    
    .login-btn:hover {
      background: #1d4ed8;
    }
    
    .login-btn:disabled {
      background: #9ca3af;
      cursor: not-allowed;
    }
    
    .error-message {
      background: #fef2f2;
      color: #dc2626;
      padding: 0.75rem;
      border-radius: 6px;
      margin-bottom: 1rem;
      border: 1px solid #fecaca;
      display: none;
    }
    
    .demo-credentials {
      background: #f0f9ff;
      border: 1px solid #bae6fd;
      border-radius: 6px;
      padding: 1rem;
      margin-top: 1.5rem;
      text-align: left;
    }
    
    .demo-credentials h3 {
      margin: 0 0 0.5rem 0;
      color: #0369a1;
      font-size: 0.875rem;
      font-weight: 600;
    }
    
    .demo-credentials p {
      margin: 0.25rem 0;
      font-size: 0.875rem;
      color: #0369a1;
    }
  </style>
</head>
<body>
  <div class="login-container">
    <div class="login-header">
      <h1>Welcome Back</h1>
      <p>Sign in to access Harper Configurator</p>
    </div>
    
    <form id="login-form">
      <div class="error-message" id="error-message"></div>
      
      <div class="form-group">
        <label for="username">Username</label>
        <input type="text" id="username" name="username" required autocomplete="username">
      </div>
      
      <div class="form-group">
        <label for="password">Password</label>
        <input type="password" id="password" name="password" required autocomplete="current-password">
      </div>
      
      <button type="submit" class="login-btn" id="login-btn">Sign In</button>
    </form>
    
    <div class="demo-credentials">
      <h3>Demo Credentials:</h3>
      <p><strong>Username:</strong> demo | <strong>Password:</strong> demo</p>
      <p><strong>Username:</strong> admin | <strong>Password:</strong> password</p>
      <p><strong>Username:</strong> user1 | <strong>Password:</strong> pass123</p>
    </div>
  </div>
  
  <script src="auth.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const loginForm = document.getElementById('login-form');
      const errorMessage = document.getElementById('error-message');
      const loginBtn = document.getElementById('login-btn');
      
      // If already authenticated, redirect to dashboard
      if (authManager.isAuthenticated()) {
        authManager.redirectToDashboard();
        return;
      }
      
      loginForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        
        // Disable button and show loading state
        loginBtn.disabled = true;
        loginBtn.textContent = 'Signing in...';
        errorMessage.style.display = 'none';
        
        // Simulate network delay for better UX
        setTimeout(() => {
          const result = authManager.login(username, password);
          
          if (result.success) {
            // Redirect to dashboard or original page
            authManager.redirectToDashboard();
          } else {
            // Show error message
            errorMessage.textContent = result.error;
            errorMessage.style.display = 'block';
            
            // Reset button
            loginBtn.disabled = false;
            loginBtn.textContent = 'Sign In';
          }
        }, 500);
      });
    });
  </script>
</body>
</html>
