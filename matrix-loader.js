// Matrix loading and evaluation functions
// Cache initialization moved to top
if (!window.matrixCache) {
    window.matrixCache = new Map();
}

// Debug logging function
function debugMatrix(message, data = {}) {
    if (data.debug) {
        console.debug(`🔍 Matrix: ${message}`, data);
    }
}

// Matrix helper functions
function getMatrix(key) {
    return window.matrixCache.get(key);
}

function setMatrix(key, value) {
    window.matrixCache.set(key, value);
    return value;
}

function hasMatrix(key) {
    return window.matrixCache.has(key);
}

// Matrix loading function
async function loadMatrix(matrixName, options = {}) {
    // Check cache first
    if (hasMatrix(matrixName)) {
        debugMatrix(`Using cached matrix ${matrixName}`, options);
        return getMatrix(matrixName);
    }

    try {
        debugMatrix(`Loading matrix ${matrixName}`, options);

        // Load matrix XML file
        const response = await fetch(`./data/Matrices/Matrix_${matrixName}.xml`);
        if (!response.ok) {
            console.error(`Failed to load matrix ${matrixName}: ${response.status} ${response.statusText}`);
            return null;
        }

        const text = await response.text();
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(text, "text/xml");

        // Check for parsing errors
        const parseError = xmlDoc.querySelector('parsererror');
        if (parseError) {
            console.error('XML parsing error:', parseError.textContent);
            return null;
        }

        // Get matrix root element
        const matrixElement = xmlDoc.querySelector('Matrix');
        if (!matrixElement) {
            console.error('No Matrix element found in XML');
            return null;
        }

        // Parse dimensions
        const dimensions = Array.from(matrixElement.querySelectorAll('Dimension')).map(dim => ({
            name: dim.getAttribute('Name')?.replace(' =', '') || '',
            accessMethod: dim.getAttribute('AccessMethod') || '='
        }));

        // Parse values
        const values = Array.from(matrixElement.querySelectorAll('Value')).map(value => {
            const lookups = Array.from(value.querySelectorAll('Lookup')).map(l => l.textContent || '');
            const result = value.querySelector('Value')?.textContent || '';
            return { lookups, result };
        });

        debugMatrix(`Parsed matrix ${matrixName}:`, {
            dimensions: dimensions.length,
            values: values.length,
            ...options
        });

        return setMatrix(matrixName, { name: matrixName, dimensions, values });
    } catch (error) {
        console.error(`Error loading matrix ${matrixName}:`, error);
        return null;
    }
}

// Matrix evaluation function
async function evaluateMatrixValue(matrixName, inputs, options = {}) {
    try {
        const matrix = await loadMatrix(matrixName);
        if (!matrix || !matrix.values) {
            console.warn(`Matrix ${matrixName} not found or invalid`);
            return options.fallbackValue || options.defaultValue || null;
        }

        console.debug(`Evaluating ${matrixName} with:`, { inputs, dimensions: matrix.dimensions });

        // Find matching value
        for (const entry of matrix.values) {
            let matches = true;
            for (let i = 0; i < matrix.dimensions.length; i++) {
                const dim = matrix.dimensions[i];
                const lookup = entry.lookups[i] || '';
                const input = inputs[dim.name] || '';

                if (lookup === '*' || input === '*') continue; // Wildcards match anything
                if (lookup !== input) {
                    matches = false;
                    break;
                }
            }
            if (matches) {
                console.debug(`Found match in ${matrixName}:`, { entry, inputs });
                return entry.result;
            }
        }

        console.debug(`No match found in ${matrixName}`, { inputs });
        return options.fallbackValue || options.defaultValue || null;
    } catch (error) {
        console.error(`Error evaluating matrix ${matrixName}:`, error);
        return options.fallbackValue || options.defaultValue || null;
    }
}

// Set on window for backward compatibility
window.evaluateMatrixValue = evaluateMatrixValue;

// Export module functions
export {
    evaluateMatrixValue,
    loadMatrix,
    getMatrix,
    setMatrix,
    hasMatrix
};

