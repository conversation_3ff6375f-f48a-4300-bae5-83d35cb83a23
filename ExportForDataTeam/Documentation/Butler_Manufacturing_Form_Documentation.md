# Butler Manufacturing Form Documentation (sys-butler.html)

## Overview

This document provides comprehensive documentation for every field in the Butler Manufacturing configurator form (sys-butler.html), including data sources, dependencies, and business logic.

## Form Field Documentation

### 1. Customer Order Status

**Field Name:** CustomerOrder  
**Form Label:** "Is this a Customer Order (No is Harper Stock)?"  
**Data Type:** Radio Button (Yes/No)  
**Required:** Yes  
**Source Data Sheet:** N/A (Boolean flag)  
**Business Logic:**

- Determines pricing structure and inventory handling
- Customer orders may have different lead times and pricing
- Harper stock items have standard pricing and immediate availability
- Affects final pricing calculations and delivery scheduling

### 2. Bed Type Selection

**Field Name:** BedType  
**Form Label:** "Bed Type"  
**Data Type:** Dropdown  
**Required:** Yes  
**Source Data Sheet:** `butler_beds.csv` (bed_type column)  
**Options:** Arm Bed, Flat Bed, Service Bed, Dump Bed  
**Business Logic:**

- Determines available bed models and configurations
- Filters subsequent bed selection options
- Affects pricing structure and available features
- Influences component compatibility and options

### 3. Bed Knowledge Preference

**Field Name:** TruckConfig  
**Form Label:** "Do you know what bed you want?"  
**Data Type:** Radio Button (Yes/No)  
**Required:** Yes  
**Source Data Sheet:** N/A (Boolean flag)  
**Dependencies:** BedType selection  
**Business Logic:**

- When "Yes": Shows specific bed selection dropdown
- When "No": Shows guided configuration questions
- Determines the configuration flow path
- Affects which subsequent fields are displayed

### 4. Specific Bed Selection

**Field Name:** Bed  
**Form Label:** "Choose a bed"  
**Data Type:** Dropdown  
**Required:** Yes (when TruckConfig = Yes)  
**Source Data Sheet:** `butler_beds.csv`  
**Dependencies:** BedType and TruckConfig = Yes  
**Business Logic:**

- Filtered by selected bed_type
- Displays bed_part_number and dimensions
- Sets base pricing from base_price column
- Determines available ball types and features
- Auto-populates related bed specifications

### 5. Fifth Wheel Ball Type

**Field Name:** BallType  
**Form Label:** "5th Wheel Ball"  
**Data Type:** Dropdown  
**Required:** Yes  
**Source Data Sheet:** `butler_beds.csv` (ball_type column)  
**Options:** Recessed, Flush, None, Turnover  
**Dependencies:** Selected bed compatibility  
**Business Logic:**

- Must be compatible with selected bed
- Affects pricing (different ball types have price variations)
- Influences bed part number selection
- Some bed types may not support all ball options

### 6. Bed Feature Configuration

**Field Name:** BedFeature  
**Form Label:** "Straight/Tapered/Troughed/Skirted"  
**Data Type:** Dropdown  
**Required:** Yes  
**Source Data Sheet:** `butler_beds.csv` (bed_feature column)  
**Options:** Skirted Std Straight/Tapered, Standard, Service Body, Dump Body  
**Dependencies:** BedType and selected bed  
**Business Logic:**

- Determines bed styling and functionality
- Affects pricing (skirted beds typically cost more)
- Influences available accessories and options
- Must match bed_feature in selected bed record

### 7. Cab/Axle Length

**Field Name:** CabAxle  
**Form Label:** "What is your cab/Axle Length"  
**Data Type:** Dropdown  
**Required:** Yes  
**Source Data Sheet:** `butler_trucks.csv` (cab_axle_config column)  
**Options:** 56, 60, 72, 84, 96  
**Dependencies:** Make and truck configuration  
**Business Logic:**

- Determines truck compatibility and bed mounting
- Affects bed positioning and installation requirements
- Influences pricing based on complexity
- Must match available truck configurations

### 8. Headache Rack

**Field Name:** Headache  
**Form Label:** "Headache Rack"  
**Data Type:** Dropdown  
**Required:** Yes  
**Source Data Sheet:** `butler_components.csv` (component_type = "Headache Rack")  
**Options:** None, Standard, Tall, Tall with Lights  
**Business Logic:**

- Optional safety and utility feature
- Affects pricing based on selection
- "Tall with Lights" includes electrical components
- Influences overall bed height and clearance

### 9. Spike Collars in Crosstube

**Field Name:** Spikes  
**Form Label:** "Do you want spikes collars included in crosstube?"  
**Data Type:** Radio Button (Yes/No)  
**Required:** Yes  
**Source Data Sheet:** `butler_options.csv` (option_type = "Spikes")  
**Business Logic:**

- Optional feature for securing loads
- Adds to component cost when selected
- Affects crosstube part number and configuration
- Influences installation complexity

### 10. Outlets

**Field Name:** Outlets  
**Form Label:** "Do you want outlets?"  
**Data Type:** Radio Button (Yes/No)  
**Required:** Yes  
**Source Data Sheet:** `butler_options.csv` (option_type = "Outlets")  
**Business Logic:**

- Electrical power outlets for tools/equipment
- Requires electrical harness when selected
- Affects pricing and installation requirements
- May influence harness selection

### 11. Wireless Control

**Field Name:** ControlType  
**Form Label:** "Do you want a wireless control?"  
**Data Type:** Radio Button (Yes/No)  
**Required:** Yes  
**Source Data Sheet:** `butler_options.csv` (option_type = "Control")  
**Business Logic:**

- Premium feature for remote bed operation
- Significantly affects pricing
- Requires compatible electrical system
- Influences harness and control module selection

### 12. Rub Rails

**Field Name:** RubRails  
**Form Label:** "Do you want rub rails?"  
**Data Type:** Radio Button (Yes/No)  
**Required:** Yes  
**Source Data Sheet:** `butler_options.csv` (option_type = "RubRails")  
**Business Logic:**

- Protective feature for bed sides
- Adds to material and labor costs
- Affects bed appearance and durability
- Optional aesthetic and functional enhancement

### 13. Receiver Hitch

**Field Name:** Tailboard  
**Form Label:** "Do you want a receiver hitch?"  
**Data Type:** Radio Button (Yes/No)  
**Required:** Yes  
**Source Data Sheet:** `butler_options.csv` (option_type = "ReceiverHitch")  
**Business Logic:**

- Towing capability addition
- Affects rear bed configuration
- Adds to component cost and complexity
- May influence tailboard design

### 14. Work Lights

**Field Name:** WorkLights  
**Form Label:** "Do you want work lights?"  
**Data Type:** Radio Button (Yes/No)  
**Required:** Yes  
**Source Data Sheet:** `butler_options.csv` (option_type = "WorkLights")  
**Business Logic:**

- LED lighting for work area illumination
- Requires electrical harness and wiring
- Affects pricing and installation complexity
- May influence headache rack selection

### 15. Across the Bed Toolbox

**Field Name:** XBed  
**Form Label:** "Across the Bed Toolbox"  
**Data Type:** Dropdown  
**Required:** No  
**Source Data Sheet:** `butler_components.csv` (component_type = "Toolbox")  
**Options:** None, Standard, Deep, Extra Deep  
**Business Logic:**

- Optional storage solution
- Affects bed clearance and capacity
- Pricing varies by toolbox size and type
- May influence other component placement

### 16. Mudflaps

**Field Name:** MudFlaps  
**Form Label:** "Do you want mudflaps?"  
**Data Type:** Radio Button (Yes/No)  
**Required:** Yes  
**Source Data Sheet:** `butler_options.csv` (option_type = "Mudflaps")  
**Business Logic:**

- Protective feature for vehicle and environment
- Adds mounting hardware and material costs
- Affects rear bed configuration
- Optional regulatory compliance feature

### 17. Adapter Harness

**Field Name:** Wireharness  
**Form Label:** "Do you want an adapter harness?"  
**Data Type:** Radio Button (Yes/No)  
**Required:** Yes  
**Source Data Sheet:** `butler_harnesses.csv`  
**Dependencies:** Make, Year, electrical options  
**Business Logic:**

- Required for electrical features (lights, controls, outlets)
- Harness selection based on truck make/year
- Affects pricing and installation complexity
- Enables electrical feature functionality

### 18. Bed Mounting Kit

**Field Name:** MountKit  
**Form Label:** "Do you want a bed mounting kit?"  
**Data Type:** Radio Button (Yes/No)  
**Required:** Yes  
**Source Data Sheet:** `butler_options.csv` (option_type = "MountKit")  
**Business Logic:**

- Hardware for bed installation
- May be included with certain bed types
- Affects installation ease and cost
- Required for proper bed mounting

### 19. Mounting Tabs (Ford Only)

**Field Name:** MntTabs  
**Form Label:** "Do you want mounting tabs (Ford Only)?"  
**Data Type:** Radio Button (Yes/No)  
**Required:** Yes (when Make = Ford)  
**Source Data Sheet:** `butler_options.csv` (option_type = "MountingTabs")  
**Dependencies:** Make = Ford  
**Business Logic:**

- Ford-specific mounting hardware
- Only visible/applicable for Ford trucks
- Affects Ford bed installation process
- Specialized hardware for Ford chassis

### 20. Customization with Added Price

**Field Name:** CustomPrice  
**Form Label:** "Does this order have customization with added price?"  
**Data Type:** Radio Button (Yes/No)  
**Required:** Yes  
**Source Data Sheet:** N/A (Boolean flag)  
**Business Logic:**

- Indicates non-standard modifications
- Affects pricing calculations
- May require special handling or approval
- Influences lead time and manufacturing process

### 21. Program Bed Status

**Field Name:** ProgramBed  
**Form Label:** "Is this a program bed?"  
**Data Type:** Dropdown  
**Required:** Yes  
**Source Data Sheet:** N/A (Business rule)  
**Options:** NOT A PROGRAMMED BED, PROGRAM BED  
**Business Logic:**

- Determines if bed is part of special program pricing
- Affects discount structure and availability
- May have different lead times
- Influences final pricing calculations

## Detailed Results Section

The form generates detailed component results including:

### Component Part Numbers:

- **Bed:** From `butler_beds.csv` bed_part_number
- **Deck:** Component part number for deck assembly
- **LH/RH Skirt:** Left and right skirt part numbers
- **Headache Rack:** Part number based on selection
- **Ball:** Ball type part number
- **Arms & Side Rails:** Component assembly part numbers
- **Spinners:** Spinner component part numbers
- **Crosstube:** Crosstube assembly part number
- **Hydraulic Plumbing:** Hydraulic system components
- **Toolboxes:** Front, rear, and across-bed toolbox part numbers
- **Harnesses:** Electrical harness part numbers

### Model Code Generation:

Complex alphanumeric code representing complete configuration:

- Base bed part number + feature codes + option codes
- Example: `210940101SKTLRST56FB30NNNNNNNNALNNZZZ`

### Pricing Calculations:

- **Base Price:** From selected bed base_price
- **Upgrades:** Skirting, lights, toolboxes, etc.
- **Options:** Individual option pricing
- **Total Package Price:** Sum of all components
- **Discount:** 3% if paid in 10 days
- **Final Total:** Package price minus applicable discounts

## Data Relationships and Cross-References

### Primary Lookup Chains:

1. **Bed Selection Chain:**

   - BedType → Bed → BallType + BedFeature
   - Uses `butler_beds.csv` for filtering and pricing

2. **Truck Compatibility Chain:**

   - Make → Year → CabAxle
   - References `butler_trucks.csv` for compatibility

3. **Component Selection Chain:**

   - Selected options → Component part numbers
   - Uses `butler_components.csv` for part numbers and pricing

4. **Harness Selection Chain:**
   - Make + Year + Electrical Options → Harness
   - References `butler_harnesses.csv` for compatibility

### Pricing Rules:

- Base pricing from `butler_beds.csv`
- Option pricing from `butler_options.csv`
- Component pricing from `butler_components.csv`
- Discount rules from `butler_pricing_rules.csv`

### Special Business Rules:

1. **Ford-Specific Options:** Mounting tabs only for Ford trucks
2. **Electrical Dependencies:** Harness required for electrical options
3. **Bed Type Restrictions:** Some options only available with certain bed types
4. **Program Bed Pricing:** Special pricing structure for program beds
5. **Customer vs Stock:** Different pricing and handling procedures

## Form Validation Rules:

1. All required fields must be completed
2. Ford-specific options only appear for Ford trucks
3. Electrical options require compatible harness
4. Bed selection must match bed type
5. Component compatibility verified before final configuration

## Integration Points:

- **Butler Beds Database:** Primary bed specifications and pricing
- **Component Catalog:** Individual component part numbers and costs
- **Pricing Engine:** Complex pricing calculations with discounts
- **Inventory System:** Stock availability and lead times
- **Order Management:** Customer vs stock order processing
