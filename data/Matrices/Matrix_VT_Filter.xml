<?xml version="1.0" encoding="UTF-8"?>
<Matrix Name="VT_Filter">
    <Row>
        <Condition>
            <Group Name="Make">FD</Group>
            <Group Name="Year">2024</Group>
            <Group Name="Application">AG</Group>
        </Condition>
        <Value>1</Value>
    </Row>
    <Row>
        <Condition>
            <Group Name="Make">FD</Group>
            <Group Name="Application">COM</Group>
        </Condition>
        <Value>3</Value>
    </Row>
    <Row>
        <Condition>
            <Group Name="Make">DG</Group>
            <Group Name="Application">AG</Group>
        </Condition>
        <Value>1</Value>
    </Row>
    <!-- Default to truck only -->
    <Row>
        <Condition/>
        <Value>1</Value>
    </Row>
</Matrix>
