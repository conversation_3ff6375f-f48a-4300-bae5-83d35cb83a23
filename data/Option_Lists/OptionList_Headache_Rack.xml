<OptionList Name="Headache_Rack">
  <Property Name="Value" DataType="String" DefaultValue="" Sequence="1" />
  <Property Name="Description" DataType="String" DefaultValue="" Sequence="2" />
  <Property Name="Order" DataType="Number" DefaultValue="1" Sequence="3" />
  <Property Name="Visible" DataType="Boolean" DefaultValue="True" Sequence="4" />
  <Property Name="Locked" DataType="Boolean" DefaultValue="False" Sequence="5" />
  <Property Name="Tooltip" DataType="String" DefaultValue="" Sequence="6" />
  <Property Name="ImageLink" DataType="String" DefaultValue="" Sequence="7" />
  <Property Name="Comments" DataType="String" DefaultValue="" Sequence="8" />
  <Property Name="Price" DataType="Number" DefaultValue="" Sequence="0" />
  <Group Name="BoxRemoval" />
  <Group Name="BoxRemoval80" />
  <Group Name="CabChassis" />
  <Group Name="CabChassis80" />
  <Group Name="Deweze" />
  <Value>
    <Property Name="Value">SN</Property>
    <Property Name="Description">Short</Property>
    <Property Name="Order">1</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments">Butler</Property>
    <Property Name="Price"></Property>
    <Group Name="BoxRemoval" />
  </Value>
  <Value>
    <Property Name="Value">MN</Property>
    <Property Name="Description">Medium</Property>
    <Property Name="Order">2</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments">Butler</Property>
    <Property Name="Price"></Property>
    <Group Name="BoxRemoval" />
    <Group Name="BoxRemoval80" />
  </Value>
  <Value>
    <Property Name="Value">TN</Property>
    <Property Name="Description">Tall</Property>
    <Property Name="Order">3</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments">Butler</Property>
    <Property Name="Price"></Property>
    <Group Name="BoxRemoval" />
    <Group Name="BoxRemoval80" />
    <Group Name="CabChassis" />
    <Group Name="CabChassis80" />
  </Value>
  <Value>
    <Property Name="Value">SL</Property>
    <Property Name="Description">Short with Lights</Property>
    <Property Name="Order">6</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments">Butler</Property>
    <Property Name="Price">152.00</Property>
    <Group Name="BoxRemoval" />
  </Value>
  <Value>
    <Property Name="Value">ML</Property>
    <Property Name="Description">Medium with Lights</Property>
    <Property Name="Order">7</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments">Butler</Property>
    <Property Name="Price">152.00</Property>
    <Group Name="BoxRemoval" />
    <Group Name="BoxRemoval80" />
  </Value>
  <Value>
    <Property Name="Value">TL</Property>
    <Property Name="Description">Tall with Lights</Property>
    <Property Name="Order">8</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments">Butler</Property>
    <Property Name="Price">152.00</Property>
    <Group Name="BoxRemoval" />
    <Group Name="BoxRemoval80" />
    <Group Name="CabChassis" />
    <Group Name="CabChassis80" />
  </Value>
  <Value>
    <Property Name="Value">XT</Property>
    <Property Name="Description">Extra Tall</Property>
    <Property Name="Order">4</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments">Butler</Property>
    <Property Name="Price"></Property>
    <Group Name="BoxRemoval" />
    <Group Name="BoxRemoval80" />
    <Group Name="CabChassis" />
    <Group Name="CabChassis80" />
  </Value>
  <Value>
    <Property Name="Value">XL</Property>
    <Property Name="Description">Extra Tall with Lights</Property>
    <Property Name="Order">9</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments">Butler</Property>
    <Property Name="Price">152.00</Property>
    <Group Name="BoxRemoval" />
    <Group Name="BoxRemoval80" />
    <Group Name="CabChassis" />
    <Group Name="CabChassis80" />
  </Value>
  <Value>
    <Property Name="Value">ST</Property>
    <Property Name="Description">Standard (Most BTO &amp; CC, 37.375")</Property>
    <Property Name="Order">1</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments">89Deweze</Property>
    <Property Name="Price"></Property>
    <Group Name="Deweze" />
  </Value>
  <Value>
    <Property Name="Value">MD</Property>
    <Property Name="Description">Medium (Chevy 4500/5500/6500 Ford 450/550/F660 Inter CV, 44.125")</Property>
    <Property Name="Order">2</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments">89Deweze</Property>
    <Property Name="Price"></Property>
    <Group Name="Deweze" />
  </Value>
  <Value>
    <Property Name="Value">LG</Property>
    <Property Name="Description">Large (2.5 Ton Millitary Trucks, 52.25")</Property>
    <Property Name="Order">3</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments">89Deweze</Property>
    <Property Name="Price"></Property>
    <Group Name="Deweze" />
  </Value>
  <Value>
    <Property Name="Value">ET</Property>
    <Property Name="Description">Extra Extra Tall</Property>
    <Property Name="Order">5</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments">Butler</Property>
    <Property Name="Price"></Property>
    <Group Name="CabChassis" />
    <Group Name="CabChassis80" />
  </Value>
  <Value>
    <Property Name="Value">EL</Property>
    <Property Name="Description">Extra Extra Tall with Lights</Property>
    <Property Name="Order">10</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments">Butler</Property>
    <Property Name="Price"></Property>
    <Group Name="CabChassis" />
    <Group Name="CabChassis80" />
  </Value>
  <Tags />
</OptionList>