<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Full Adapter Test</title>
    <link rel="stylesheet" href="sys-700cpk.css">
    <style>
        .test-controls {
            background: #e8f4f8;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 2px solid #007cba;
        }
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #005a87;
        }
        .test-result {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #007cba;
        }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
    </style>
</head>
<body>
    <main>
        <header>
            <h1>Full Adapter Field Test</h1>
        </header>
        
        <div class="test-controls">
            <h2>Test Controls</h2>
            <button class="test-button" onclick="simulateUserInput()">Simulate User Input</button>
            <button class="test-button" onclick="testAdapterDirectly()">Test Adapter Function</button>
            <button class="test-button" onclick="checkFormState()">Check Form State</button>
            <div id="test-results"></div>
        </div>

        <div id="configurator">
            <form id="sys-700cpk-form" class="configurator-form">
                <!-- Fields will be dynamically inserted here -->
            </form>
        </div>
    </main>

    <script type="module" src="matrix-loader.js"></script>
    <script type="module" src="truck-hydraulic-system-fields.js"></script>
    <script type="module">
        import { fields, getFieldGroups, isFieldVisible } from './truck-hydraulic-system-fields.js';
        import { evaluateMatrixValue } from './matrix-loader.js';

        // Simplified form state for testing
        let testFormState = {};

        // Test functions
        window.simulateUserInput = async function() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="test-result">Simulating user input...</div>';
            
            try {
                // Set the exact form state from user input
                testFormState = {
                    Application: 'AG',
                    AGKitLessMiniPack: false,
                    Make: 'FD',
                    Year: '2024',
                    Vehicle_Type: 'TRUCK',
                    Engine: 'FD67D',
                    Pump: '9GL',
                    Kit: '700612',
                    Clutch: '8GRVA'
                };

                console.log('Test form state set:', testFormState);
                
                // Test if adapter field condition is met
                const adapterField = fields.Adapter;
                const conditionMet = adapterField.condition ? adapterField.condition(testFormState) : true;
                
                resultsDiv.innerHTML += `<div class="test-result">
                    <strong>Form State Set:</strong> ✅<br>
                    <strong>Adapter Field Condition Met:</strong> ${conditionMet ? '✅' : '❌'}<br>
                    <strong>Kit:</strong> ${testFormState.Kit}<br>
                    <strong>Pump:</strong> ${testFormState.Pump}
                </div>`;
                
                if (conditionMet && adapterField.getValue) {
                    const adapterValue = await adapterField.getValue(testFormState);
                    const expectedFull = '03-51261; S=16HB-16MB90; P=10MJ-12MB45';
                    const isFullMatch = adapterValue === expectedFull;
                    const isPartialMatch = adapterValue === '03-51261';

                    resultsDiv.innerHTML += `<div class="test-result ${isFullMatch ? 'success' : (isPartialMatch ? '' : 'error')}">
                        <strong>Adapter Value:</strong> ${adapterValue}<br>
                        <strong>Expected Full:</strong> ${expectedFull}<br>
                        <strong>Match:</strong> ${isFullMatch ? '✅ Full Match' : (isPartialMatch ? '🔶 Partial Match (code only)' : '❌ No Match')}
                    </div>`;
                }
                
            } catch (error) {
                console.error('Simulation error:', error);
                resultsDiv.innerHTML += `<div class="test-result error">❌ Error: ${error.message}</div>`;
            }
        };

        window.testAdapterDirectly = async function() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="test-result">Testing adapter matrix lookup directly...</div>';
            
            try {
                // Test the matrix lookup directly
                const kitLast4 = '0612';
                const pump = '9GL';
                const ag = 'B';
                
                console.log('Direct matrix test:', { kitLast4, pump, ag });
                
                const result = await evaluateMatrixValue('MultipleAdapterGroups', {
                    'Kit (Skip first 4)': kitLast4,
                    'Pump': pump,
                    'AG': ag
                }, { debug: true });
                
                resultsDiv.innerHTML += `<div class="test-result ${result === '03-51261' ? 'success' : 'error'}">
                    <strong>Direct Matrix Lookup:</strong><br>
                    Kit (last 4): ${kitLast4}<br>
                    Pump: ${pump}<br>
                    AG: ${ag}<br>
                    <strong>Result:</strong> ${result || 'null'}<br>
                    <strong>Expected:</strong> 03-51261<br>
                    <strong>Match:</strong> ${result === '03-51261' ? '✅' : '❌'}
                </div>`;
                
            } catch (error) {
                console.error('Direct test error:', error);
                resultsDiv.innerHTML += `<div class="test-result error">❌ Error: ${error.message}</div>`;
            }
        };

        window.checkFormState = function() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = `<div class="test-result">
                <strong>Current Test Form State:</strong><br>
                <pre>${JSON.stringify(testFormState, null, 2)}</pre>
            </div>`;
        };

        // Initialize
        console.log('Full test page loaded');
    </script>
</body>
</html>
