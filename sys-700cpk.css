body {
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
}

main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

header {
    margin-bottom: 2rem;
}

h1 {
    color: #2c3e50;
    margin: 0 0 1rem 0;
}

.back-link {
    color: #3498db;
    text-decoration: none;
    display: inline-block;
    margin-bottom: 1rem;
}

.back-link:hover {
    text-decoration: underline;
}

/* Form styles */
.configurator-form {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.field-container {
    margin-bottom: 1.5rem;
    position: relative;
    transition: all 0.3s ease;
}

.input-container {
    display: flex;
    flex-direction: column;
}

/* Display field styles */
.display-field {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 4px;
    padding: 0.75rem;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #495057;
    min-height: 1.5rem;
    line-height: 1.4;
}

.display-field.loading {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.display-field.error {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
    gap: 0.5rem;
}

.input-container label {
    font-weight: 500;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.required-indicator {
    color: #e74c3c;
    font-weight: bold;
}

.field-help-text {
    font-size: 0.875rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.field-error {
    color: #e74c3c;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    animation: fadeIn 0.2s ease-in;
}

.input-container select,
.input-container input[type="text"],
.input-container input[type="number"] {
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    width: 100%;
    max-width: 400px;
    transition: all 0.2s ease;
}

.input-container select:hover,
.input-container input[type="text"]:hover,
.input-container input[type="number"]:hover {
    border-color: #3498db;
}

.input-container select:focus,
.input-container input[type="text"]:focus,
.input-container input[type="number"]:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.field-container.has-error input,
.field-container.has-error select {
    border-color: #e74c3c;
}

.field-container.has-error .field-error {
    display: block;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-wrapper input[type="checkbox"] {
    width: 1.25rem;
    height: 1.25rem;
    cursor: pointer;
}

.checkbox-label {
    font-size: 1rem;
    color: #2c3e50;
}

/* Form container */
#configurator {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    padding: 2rem;
}

/* Field groups */
.field-group {
    margin-bottom: 2.5rem;
    padding: 1.5rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.group-title {
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #eee;
    color: #2c3e50;
    font-size: 1.25rem;
    font-weight: 600;
}

/* Form elements */
.form-group {
    margin-bottom: 1.5rem;
}

/* Display values */
.display-value {
    padding: 0.75rem;
    background: #f8f9fa;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #2a3a4a;
    min-height: 1.5rem;
}

label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #2a3a4a;
}
select, input[type="checkbox"], input[type="radio"] {
  margin-bottom: 1rem;
  font-size: 1rem;
  padding: 0.5rem;
  border-radius: 6px;
  border: 1px solid #d1d5db;
  width: 100%;
  box-sizing: border-box;
}

select,
input[type="number"],
input[type="text"] {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 1rem;
    color: #2a3a4a;
    background: #fff;
    transition: all 0.2s ease;
}

select:hover,
input[type="number"]:hover,
input[type="text"]:hover {
    border-color: #cbd5e0;
}

select:focus,
input[type="number"]:focus,
input[type="text"]:focus {
    outline: none;
    border-color: #2a7ae2;
    box-shadow: 0 0 0 3px rgba(42, 122, 226, 0.15);
}

/* Radio buttons and checkboxes */
.radio-group {
    display: flex;
    gap: 1.5rem;
    align-items: center;
}

input[type="radio"],
input[type="checkbox"] {
    margin-right: 0.5rem;
}

/* Error messages */
.error-message {
    color: #e53935;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

/* Submit button */
button[type="submit"] {
    background: #2a7ae2;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin-top: 1rem;
    width: 100%;
}

button[type="submit"]:hover {
    background: #1a5bb8;
}

button[type="submit"]:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(42, 122, 226, 0.25);
}

/* Form buttons */
.form-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #eee;
}

.button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.button-primary {
    background-color: #3498db;
    color: white;
}

.button-primary:hover:not(:disabled) {
    background-color: #2980b9;
}

.button-secondary {
    background-color: #95a5a6;
    color: white;
}

.button-secondary:hover:not(:disabled) {
    background-color: #7f8c8d;
}

/* Form-level error message */
.form-error {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: #fdeaea;
    border-left: 4px solid #e74c3c;
    color: #c0392b;
    border-radius: 4px;
    animation: fadeIn 0.2s ease-in;
}

/* Field groups animation */
.field-group {
    opacity: 0;
    transform: translateY(10px);
    animation: fadeInUp 0.3s ease-out forwards;
}

/* Field container animation */
.field-container {
    opacity: 0;
    transform: translateY(5px);
    animation: fadeInUp 0.2s ease-out forwards;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading spinner for buttons */
.button:disabled::after {
    content: '';
    display: inline-block;
    width: 1rem;
    height: 1rem;
    margin-left: 0.5rem;
    border: 2px solid currentColor;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

/* Form description */
.form-description {
    margin-bottom: 2rem;
    color: #666;
    font-size: 1.1rem;
    line-height: 1.5;
}

/* Group description */
.group-description {
    margin: -1rem 0 1.5rem;
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-0.25rem); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes spin {
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Loading states */
.field-container.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}

.field-container.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1.5rem;
    height: 1.5rem;
    border: 2px solid #3498db;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
    z-index: 1;
}

/* Responsive adjustments */
@media (min-width: 640px) {
    button[type="submit"] {
        width: auto;
    }
    
    select,
    input[type="number"],
    input[type="text"] {
        max-width: 400px;
    }
}

@media (max-width: 768px) {
    .configurator-form {
        padding: 1rem;
    }

    .field-group {
        padding: 1rem;
    }

    .input-container select,
    .input-container input[type="text"],
    .input-container input[type="number"] {
        max-width: 100%;
    }
}
