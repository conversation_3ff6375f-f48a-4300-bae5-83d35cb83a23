// Butler Manufacturing Configurator Field Definitions
// Based on SYS.Butler_Manufacturing ruleset and available matrices/option lists

import { evaluateMatrixValue } from './matrix-loader.js';

export const fields = {
    // Initial question to determine configuration path
    TruckConfig: {
        type: 'boolean',
        label: 'Know what bed you want?',
        required: true,
        checkboxLabel: 'Yes, I know the specific bed I want',
        condition: () => true
    },

    // Core truck information fields
    Make: {
        type: 'select',
        label: 'Make',
        required: true,
        condition: (state) => true,
        getOptions: async () => {
            try {
                const response = await fetch('./data/Option_Lists/OptionList_Make.xml');
                if (!response.ok) throw new Error('Failed to load Make options');
                
                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");
                
                const values = xmlDoc.querySelectorAll('Value');
                return Array.from(values).map(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;
                    return {
                        value: code,
                        label: description || code
                    };
                });
            } catch (error) {
                console.error('Error loading Make options:', error);
                return [
                    { value: 'FD', label: 'Ford' },
                    { value: 'GM', label: 'Chevrolet/GMC' },
                    { value: 'DG', label: 'Dodge/Ram' }
                ];
            }
        }
    },

    Years: {
        type: 'select',
        label: 'Truck Year',
        required: true,
        condition: (state) => state.Make,
        getOptions: async () => {
            try {
                const response = await fetch('./data/Option_Lists/OptionList_Years.xml');
                if (!response.ok) throw new Error('Failed to load Years options');
                
                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");
                
                const values = xmlDoc.querySelectorAll('Value');
                return Array.from(values).map(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;
                    return {
                        value: code,
                        label: description || code
                    };
                }).sort((a, b) => parseInt(b.value) - parseInt(a.value)); // Sort years descending
            } catch (error) {
                console.error('Error loading Years options:', error);
                // Generate years from 2000 to current year + 1
                const currentYear = new Date().getFullYear();
                const years = [];
                for (let year = currentYear + 1; year >= 2000; year--) {
                    years.push({ value: year.toString(), label: year.toString() });
                }
                return years;
            }
        }
    },

    Axle: {
        type: 'select',
        label: 'Axle',
        required: true,
        condition: (state) => state.Make && state.Years,
        getOptions: async () => {
            try {
                const response = await fetch('./data/Option_Lists/OptionList_Axle.xml');
                if (!response.ok) throw new Error('Failed to load Axle options');
                
                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");
                
                const values = xmlDoc.querySelectorAll('Value');
                return Array.from(values).map(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;
                    return {
                        value: code,
                        label: description || code
                    };
                });
            } catch (error) {
                console.error('Error loading Axle options:', error);
                return [
                    { value: 'S', label: 'Single Rear Wheel' },
                    { value: 'D', label: 'Dual Rear Wheel' }
                ];
            }
        }
    },

    CabAxle: {
        type: 'select',
        label: 'Cab and Chassis or Bed Length',
        required: true,
        condition: (state) => state.Make && state.Years && state.Axle,
        getOptions: async () => {
            try {
                const response = await fetch('./data/Option_Lists/OptionList_Cab_Axle.xml');
                if (!response.ok) throw new Error('Failed to load Cab_Axle options');
                
                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");
                
                const values = xmlDoc.querySelectorAll('Value');
                return Array.from(values).map(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;
                    return {
                        value: code,
                        label: description || code
                    };
                });
            } catch (error) {
                console.error('Error loading Cab_Axle options:', error);
                return [
                    { value: 'SB40', label: 'Short Bed (40")' },
                    { value: 'LB56', label: 'Long Bed (56")' },
                    { value: 'CC84', label: 'Cab & Chassis (84")' }
                ];
            }
        }
    },

    BedType: {
        type: 'select',
        label: 'Bed Type',
        required: true,
        condition: (state) => state.Make && state.Years && state.Axle && state.CabAxle,
        getOptions: async () => {
            try {
                const response = await fetch('./data/Option_Lists/OptionList_Bed_Type.xml');
                if (!response.ok) throw new Error('Failed to load Bed_Type options');
                
                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");
                
                const values = xmlDoc.querySelectorAll('Value');
                return Array.from(values).map(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;
                    return {
                        value: code,
                        label: description || code
                    };
                });
            } catch (error) {
                console.error('Error loading Bed_Type options:', error);
                return [
                    { value: 'R', label: 'Regular/Flat Bed' },
                    { value: 'A', label: 'Aluminum Bed' },
                    { value: 'S', label: 'Steel Bed' }
                ];
            }
        }
    },

    // Calculated fields based on selections
    MountKit: {
        type: 'display',
        label: 'Mount Kit',
        condition: (state) => state.Make && state.Years && state.Axle && state.CabAxle && state.BedType,
        getValue: async (formState) => {
            try {
                console.debug('Mount Kit lookup with state:', formState);
                
                const mountKit = await evaluateMatrixValue('ButlerMKOpt', {
                    'Make': formState.Make,
                    'Year': formState.Years,
                    'Axle': formState.Axle,
                    'Cab_Axle': formState.CabAxle,
                    'Bed_Type': formState.BedType
                }, {
                    debug: true,
                    defaultValue: null
                });

                if (!mountKit) {
                    return 'No mount kit available for this configuration';
                }

                // Look up the mount kit description from the option list
                try {
                    const response = await fetch('./data/Option_Lists/OptionList_ButlerMountKits.xml');
                    if (!response.ok) throw new Error('Failed to load mount kit descriptions');

                    const text = await response.text();
                    const parser = new DOMParser();
                    const xmlDoc = parser.parseFromString(text, "text/xml");

                    const values = xmlDoc.querySelectorAll('Value');
                    for (const value of values) {
                        const code = value.querySelector('Property[Name="Value"]')?.textContent;
                        const description = value.querySelector('Property[Name="Description"]')?.textContent;
                        
                        if (code === mountKit) {
                            return `${mountKit} - ${description}`;
                        }
                    }
                    
                    return mountKit; // Return code if description not found
                    
                } catch (error) {
                    console.error('Error loading mount kit descriptions:', error);
                    return mountKit;
                }
                
            } catch (error) {
                console.error('Error calculating mount kit:', error);
                return 'Error calculating mount kit';
            }
        }
    },

    ButlerAdapter: {
        type: 'display',
        label: 'Butler Adapter',
        condition: (state) => state.Make && state.Years && state.Axle && state.CabAxle && state.BedType,
        getValue: async (formState) => {
            try {
                console.debug('Butler Adapter lookup with state:', formState);
                
                const adapterCodes = await evaluateMatrixValue('ButlerAdapterHarns', {
                    'Make': formState.Make,
                    'Year': formState.Years,
                    'Axle': formState.Axle,
                    'Cab_Axle': formState.CabAxle,
                    'Bed_Type': formState.BedType
                }, {
                    debug: true,
                    defaultValue: null
                });

                if (!adapterCodes || adapterCodes === 'NA') {
                    return 'No adapter required';
                }

                // Parse multiple adapter codes (separated by dashes)
                const codes = adapterCodes.split('-').filter(code => code.trim() !== '');
                
                if (codes.length === 0) {
                    return 'No adapter required';
                }

                // Look up descriptions for each adapter code
                try {
                    const response = await fetch('./data/Option_Lists/OptionList_ButlerAdapter.xml');
                    if (!response.ok) throw new Error('Failed to load adapter descriptions');

                    const text = await response.text();
                    const parser = new DOMParser();
                    const xmlDoc = parser.parseFromString(text, "text/xml");

                    const descriptions = [];
                    const values = xmlDoc.querySelectorAll('Value');
                    
                    for (const code of codes) {
                        let found = false;
                        for (const value of values) {
                            const valueCode = value.querySelector('Property[Name="Value"]')?.textContent;
                            const description = value.querySelector('Property[Name="Description"]')?.textContent;
                            
                            if (valueCode === code) {
                                descriptions.push(`${code} - ${description}`);
                                found = true;
                                break;
                            }
                        }
                        if (!found) {
                            descriptions.push(code);
                        }
                    }
                    
                    return descriptions.join('; ');
                    
                } catch (error) {
                    console.error('Error loading adapter descriptions:', error);
                    return adapterCodes;
                }
                
            } catch (error) {
                console.error('Error calculating Butler adapter:', error);
                return 'Error calculating adapter';
            }
        }
    }
};

// Helper function to check if a field should be visible
export function isFieldVisible(fieldName, formState) {
    const field = fields[fieldName];
    if (!field) return false;
    
    return !field.condition || field.condition(formState);
}

// Get field groups for rendering
export function getFieldGroups() {
    return {
        'Truck Information': ['TruckConfig', 'Make', 'Years', 'Axle', 'CabAxle', 'BedType'],
        'Configuration Results': ['MountKit', 'ButlerAdapter']
    };
}
