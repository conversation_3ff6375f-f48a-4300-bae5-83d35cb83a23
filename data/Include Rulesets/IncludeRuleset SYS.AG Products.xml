<IncludeRuleset Namespace="SYS" Name="AG Products" EntityId="550b506f-b76e-4309-a140-b76589d57284">
  <Tags />
  <ComponentAttributes />
  <RuleTree Type="IncludeRuleset">
    <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Screen" Caption="Select DewEze Product" ConditionExpression="" Enabled="true">
      <Screen Title="=&quot;AG Questions&quot;" ShowTitle="=True" ShowLine="=True" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
        <ScreenOption Sequence="1" Name="Product" Caption="=&quot;Select DewEze Product&quot;" AutoContinue="=2" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
      <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="Set XRM Flag" ConditionExpression="" Enabled="true">
        <vars>
          <var name="XRM">
            <value>=IF((Search("X",Product)&gt;0),TRUE,FALSE)</value>
          </var>
          <var name="Prod660">
            <value>=IF(Product = "660",1,0)</value>
          </var>
          <var name="ProdRT">
            <value>=IF(Product = "RT",1,0)</value>
          </var>
        </vars>
      </Rule>
      <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Message" Caption="Information about Mount Kit" ConditionExpression="=((Search(Product,&quot;X&quot;)&gt;0) and (Search(Pump,&quot;.&quot;)&gt;0))" Enabled="true">
        <Message Value="=&quot;Warning! AA pumps are not a valid option to run an XRM Flatbed. Please return to previous page and select an A mount pump.&quot;" MessageLevel="4" Title="" MessageStyle="" />
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Screen" Caption="Select Cab Type" ConditionExpression="" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
        <ScreenOption Sequence="1" Name="Cab" Caption="=&quot;Select Cab Type&quot;" AutoContinue="=2" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
    </Rule>
    <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Condition" Caption="If Product Selected is 660" ConditionExpression="=Product = &quot;660&quot;" Enabled="true">
      <Rule Namespace="SYS" Ruleset="AG Products" ChildType="false" RuleTypeName="Screen" Caption="Select Chassis" ConditionExpression="" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
          <ScreenOption Sequence="1" Name="Chassis" Caption="=&quot;Select Chassis Type&quot;" AutoContinue="=2" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
      </Rule>
      <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Screen" Caption="Select Reservoir" ConditionExpression="" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
          <ScreenOption Sequence="1" Name="Reservoir" Caption="=&quot;Do you want a reservoir?&quot;" AutoContinue="=2" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="Set Reservoir Hose Part Number and Price" ConditionExpression="=Reservoir" Enabled="true">
          <vars>
            <var name="Res_PartNumber">
              <value>="06-7005"</value>
            </var>
            <var name="Reservoir_Price">
              <value>=LookupMatrix("Pricing_Reservoir")</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Condition" Caption="If Customer Code has Value" ConditionExpression="=CustCode &lt;&gt; &quot;&quot;" Enabled="true">
            <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="If Markup is True" ConditionExpression="=Markup = TRUE" Enabled="true">
              <vars>
                <var name="Total_Price2">
                  <value>=Reservoir_Price * (1+(FirstPrice/100))</value>
                </var>
                <var name="ConfiguredPrice">
                  <value>=ConfiguredPrice + Total_Price2</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Detail" Caption="Display Reservoir Price" ConditionExpression="" Enabled="false">
                <Details>
                  <Detail Category="PriceDetails" PrintCode="" PrintSequence="0" Description="Res Cost" Visible="=True" Value="=Reservoir_Price" />
                </Details>
              </Rule>
            </Rule>
            <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="If Markup is False" ConditionExpression="=Markup = FALSE" Enabled="true">
              <vars>
                <var name="Total_Price2">
                  <value>=Reservoir_Price * ((100 - abs(FirstPrice)) / 100)</value>
                </var>
                <var name="ConfiguredPrice">
                  <value>=ConfiguredPrice + Total_Price2</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Detail" Caption="Display Reservoir Price" ConditionExpression="" Enabled="false">
                <Details>
                  <Detail Category="PriceDetails" PrintCode="" PrintSequence="0" Description="Res Cost" Visible="=True" Value="=Reservoir_Price" />
                </Details>
              </Rule>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Screen" Caption="660 Hose Selection" ConditionExpression="" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
          <ScreenOption Sequence="1" Name="Hoses660" Caption="=&quot;Do you want Hoses?&quot;" AutoContinue="=2" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Condition" Caption="If User selects Hoses" ConditionExpression="=Hoses660 = TRUE" Enabled="true">
          <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="Set Hose Part Number and Price" ConditionExpression="" Enabled="true">
            <vars>
              <var name="Hoses660_PartNumber">
                <value>=LookupMatrix("Hose_Feature")</value>
              </var>
              <var name="Hoses660_Price">
                <value>=LookupMatrix("Pricing_Hoses",Hoses660_PartNumber)</value>
              </var>
            </vars>
          </Rule>
          <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Condition" Caption="If Customer Code has Value" ConditionExpression="=CustCode &lt;&gt; &quot;&quot;" Enabled="true">
            <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="If Markup is True" ConditionExpression="=Markup = TRUE" Enabled="true">
              <vars>
                <var name="Total_Price3">
                  <value>=Hoses660_Price * (1+(FirstPrice/100))</value>
                </var>
                <var name="ConfiguredPrice">
                  <value>=ConfiguredPrice + Total_Price3</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Detail" Caption="Display Hoses Prices" ConditionExpression="" Enabled="false">
                <Details>
                  <Detail Category="PriceDetails" PrintCode="" PrintSequence="0" Description="Hose Cost" Visible="=True" Value="=Hoses660_Price" />
                </Details>
              </Rule>
            </Rule>
            <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="If Markup is False" ConditionExpression="=Markup = FALSE" Enabled="true">
              <vars>
                <var name="Total_Price3">
                  <value>=Hoses660_Price  * ((100 - abs(FirstPrice)) / 100)</value>
                </var>
                <var name="ConfiguredPrice">
                  <value>=ConfiguredPrice + Total_Price3</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Detail" Caption="Display Hoses Prices" ConditionExpression="" Enabled="false">
                <Details>
                  <Detail Category="PriceDetails" PrintCode="" PrintSequence="0" Description="Hose Cost" Visible="=True" Value="=Hoses660_Price" />
                </Details>
              </Rule>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Condition" Caption="If Product Selected is not 660" ConditionExpression="=Product &lt;&gt; &quot;660&quot;" Enabled="true">
      <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="Set Hose Part Number and Price" ConditionExpression="" Enabled="true">
        <vars>
          <var name="Hose_PartNumber">
            <value>=LookupMatrix("Hose_Feature")</value>
          </var>
          <var name="Hoses_Price">
            <value>=LookupMatrix("Pricing_Hoses")</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Condition" Caption="If Customer Code has Value" ConditionExpression="=CustCode &lt;&gt; &quot;&quot;" Enabled="true">
          <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="If Markup is True" ConditionExpression="=Markup = TRUE" Enabled="true">
            <vars>
              <var name="Total_Price4">
                <value>=Hoses_Price * (1+(FirstPrice/100))</value>
              </var>
              <var name="ConfiguredPrice">
                <value>=ConfiguredPrice + Total_Price4</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Detail" Caption="Display Hoses Prices" ConditionExpression="" Enabled="false">
              <Details>
                <Detail Category="PriceDetails" PrintCode="" PrintSequence="0" Description="Hose Cost" Visible="=True" Value="=Hoses_Price" />
              </Details>
            </Rule>
          </Rule>
          <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="If Markup is False" ConditionExpression="=Markup = FALSE" Enabled="true">
            <vars>
              <var name="Total_Price4">
                <value>=Hoses_Price  * ((100 - abs(FirstPrice)) / 100)</value>
              </var>
              <var name="ConfiguredPrice">
                <value>=ConfiguredPrice + Total_Price4</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Detail" Caption="Display Hoses Prices" ConditionExpression="" Enabled="false">
              <Details>
                <Detail Category="PriceDetails" PrintCode="" PrintSequence="0" Description="Hose Cost" Visible="=True" Value="=Hoses_Price" />
              </Details>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Condition" Caption="If 660 Then ask Valve" ConditionExpression="=Product = &quot;660&quot;" Enabled="true">
      <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Screen" Caption="Valve YN" ConditionExpression="" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
          <ScreenOption Sequence="1" Name="ValveYN" Caption="Do you want a valve?" DisplayType="8" AutoContinue="=2" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="Set Valve Group" ConditionExpression="" Enabled="true">
          <vars>
            <var name="ValveGroup">
              <value>=LookupMatrix("MultipleValveGroups")</value>
            </var>
            <var name="Valve.OptionListGroup">
              <value>=If(Search(",",ValveGroup) &gt; 0, GroupUnion(split(ValveGroup,",")),ValveGroup)</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Screen" Caption="Select Valve" ConditionExpression="" Enabled="true">
            <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
              <ScreenOption Sequence="1" Name="Valve" Caption="=&quot;Select Valve Option&quot;" AutoContinue="=2" SelectOptionsImageLinkFieldExpression="ImageLink" />
            </Screen>
            <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="Set Valve Part Number and Price" ConditionExpression="" Enabled="true">
              <vars>
                <var name="Valve_PartNumber">
                  <value>=LookupMatrix("Valve_Feature")</value>
                </var>
                <var name="Valve_Price">
                  <value>=LookupMatrix("Pricing_Valve")</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Condition" Caption="If Customer Code has Value" ConditionExpression="=CustCode &lt;&gt; &quot;&quot;" Enabled="true">
                <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="If Markup is True" ConditionExpression="=Markup = TRUE" Enabled="true">
                  <vars>
                    <var name="Total_Price5">
                      <value>=Valve_Price * (1+(FirstPrice/100))</value>
                    </var>
                    <var name="ConfiguredPrice">
                      <value>=ConfiguredPrice + Total_Price5</value>
                    </var>
                  </vars>
                  <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Detail" Caption="Display Valve Prices" ConditionExpression="" Enabled="false">
                    <Details>
                      <Detail Category="PriceDetails" PrintCode="" PrintSequence="0" Description="Valve Cost" Visible="=True" Value="=Valve_Price" />
                    </Details>
                  </Rule>
                </Rule>
                <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="If Markup is False" ConditionExpression="=Markup = FALSE" Enabled="true">
                  <vars>
                    <var name="Total_Price5">
                      <value>=Valve_Price  * ((100 - abs(FirstPrice)) / 100)</value>
                    </var>
                    <var name="ConfiguredPrice">
                      <value>=ConfiguredPrice + Total_Price5</value>
                    </var>
                  </vars>
                  <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Detail" Caption="Display Valve Prices" ConditionExpression="" Enabled="false">
                    <Details>
                      <Detail Category="PriceDetails" PrintCode="" PrintSequence="0" Description="Valve Cost" Visible="=True" Value="=Valve_Price" />
                    </Details>
                  </Rule>
                </Rule>
              </Rule>
            </Rule>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="Set Valve Group" ConditionExpression="=ValveYN" Enabled="false">
          <vars>
            <var name="ValveGroup">
              <value>=LookupMatrix("MultipleValveGroups")</value>
            </var>
            <var name="Valve.OptionListGroup">
              <value>=If(Search(",",ValveGroup) &gt; 0, GroupUnion(split(ValveGroup,",")),ValveGroup)</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Screen" Caption="Select Valve" ConditionExpression="" Enabled="true">
            <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
              <ScreenOption Sequence="1" Name="Valve" Caption="=&quot;Select Valve Option&quot;" AutoContinue="=2" SelectOptionsImageLinkFieldExpression="ImageLink" />
            </Screen>
          </Rule>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="AG Products" ChildType="false" RuleTypeName="Variable" Caption="Set Valve Group" ConditionExpression="" Enabled="true">
        <vars>
          <var name="ValveGroup">
            <value>=LookupMatrix("MultipleValveGroups")</value>
          </var>
          <var name="Valve.OptionListGroup">
            <value>=If(Search(",",ValveGroup) &gt; 0, GroupUnion(split(ValveGroup,",")),ValveGroup)</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Screen" Caption="Select Valve" ConditionExpression="" Enabled="true">
          <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
            <ScreenOption Sequence="1" Name="Valve" Caption="=&quot;Select Valve Option&quot;" AutoContinue="=2" SelectOptionsImageLinkFieldExpression="ImageLink" />
          </Screen>
          <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="Set Valve Part Number and Price" ConditionExpression="" Enabled="true">
            <vars>
              <var name="Valve_PartNumber">
                <value>=LookupMatrix("Valve_Feature")</value>
              </var>
              <var name="Valve_Price">
                <value>=LookupMatrix("Pricing_Valve")</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Condition" Caption="If Customer Code has Value" ConditionExpression="=CustCode &lt;&gt; &quot;&quot;" Enabled="true">
              <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="If Markup is True" ConditionExpression="=Markup = TRUE" Enabled="true">
                <vars>
                  <var name="Total_Price5">
                    <value>=Valve_Price * (1+(FirstPrice/100))</value>
                  </var>
                  <var name="ConfiguredPrice">
                    <value>=ConfiguredPrice + Total_Price5</value>
                  </var>
                </vars>
                <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Detail" Caption="Display Valve Prices" ConditionExpression="" Enabled="false">
                  <Details>
                    <Detail Category="PriceDetails" PrintCode="" PrintSequence="0" Description="Valve Cost" Visible="=True" Value="=Valve_Price" />
                  </Details>
                </Rule>
              </Rule>
              <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="If Markup is False" ConditionExpression="=Markup = FALSE" Enabled="true">
                <vars>
                  <var name="Total_Price5">
                    <value>=Valve_Price  * ((100 - abs(FirstPrice)) / 100)</value>
                  </var>
                  <var name="ConfiguredPrice">
                    <value>=ConfiguredPrice + Total_Price5</value>
                  </var>
                </vars>
                <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Detail" Caption="Display Valve Prices" ConditionExpression="" Enabled="false">
                  <Details>
                    <Detail Category="PriceDetails" PrintCode="" PrintSequence="0" Description="Valve Cost" Visible="=True" Value="=Valve_Price" />
                  </Details>
                </Rule>
              </Rule>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Condition" Caption="Auxillary Hose - Specific Path" ConditionExpression="=Product &lt;&gt; &quot;660&quot; and (Valve IN {&quot;3HF&quot;,&quot;3HFDB&quot;,&quot;3BCL&quot;} And XRM = FALSE) or Valve = &quot;4BCL&quot;" Enabled="true">
      <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Screen" Caption="Aux Hose Question" ConditionExpression="" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
          <ScreenOption Sequence="1" Name="ACC_Hose" Caption="=&quot;Do you want auxillary hoses?&quot;" AutoContinue="=2" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="Set Aux_Filter" ConditionExpression="" Enabled="true">
          <vars>
            <var name="Aux_Filter">
              <value>=IF((Valve="3HF" And XRM=FALSE),"3HFO",IF((Valve ="4BCL" And XRM=FALSE),"4BCLO",IF((Valve="4BCL" And XRM=TRUE),"4BCL1","")))</value>
            </var>
          </vars>
        </Rule>
        <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Screen" Caption="Choose Auxillary Hoses" ConditionExpression="=ACC_Hose = TRUE" Enabled="true">
          <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
            <ScreenOption Sequence="1" Name="Aux_Hoses" Caption="=&quot;Choose Auxillary Hoses&quot;" AutoContinue="=2" SelectOptionsImageLinkFieldExpression="ImageLink" />
          </Screen>
          <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="Set Aux Hose Part Number and Price" ConditionExpression="" Enabled="true">
            <vars>
              <var name="Aux_PartNumber">
                <value>=Aux_Hoses</value>
              </var>
              <var name="Aux_Price">
                <value>=LookupMatrix("Pricing_Aux")</value>
              </var>
            </vars>
          </Rule>
          <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Condition" Caption="If Customer Code has Value" ConditionExpression="=CustCode &lt;&gt; &quot;&quot;" Enabled="true">
            <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="If Markup is True" ConditionExpression="=Markup = TRUE" Enabled="true">
              <vars>
                <var name="Total_Price6">
                  <value>=Aux_Price * (1+(FirstPrice/100))</value>
                </var>
                <var name="ConfiguredPrice">
                  <value>=ConfiguredPrice + Total_Price6</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Detail" Caption="Display Aux Hose Prices" ConditionExpression="" Enabled="false">
                <Details>
                  <Detail Category="PriceDetails" PrintCode="" PrintSequence="0" Description="Aux Hose Cost" Visible="=True" Value="=Aux_Price" />
                </Details>
              </Rule>
            </Rule>
            <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="If Markup is False" ConditionExpression="=Markup = FALSE" Enabled="true">
              <vars>
                <var name="Total_Price6">
                  <value>=Aux_Price  * ((100 - abs(FirstPrice)) / 100)</value>
                </var>
                <var name="ConfiguredPrice">
                  <value>=ConfiguredPrice + Total_Price6</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Detail" Caption="Display Aux Hose Prices" ConditionExpression="" Enabled="false">
                <Details>
                  <Detail Category="PriceDetails" PrintCode="" PrintSequence="0" Description="Aux Hose Cost" Visible="=True" Value="=Aux_Price" />
                </Details>
              </Rule>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="Set Option List Group for Control" ConditionExpression="=(Product = &quot;660&quot; and ValveYN) or Product &lt;&gt; &quot;660&quot;" Enabled="true">
      <vars>
        <var name="Control.OptionListGroup">
          <value>=If(Product = "660","1","0")</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Screen" Caption="Select Control Option" ConditionExpression="" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
          <ScreenOption Sequence="1" Name="Control" Caption="=&quot;Select Control Option&quot;" AutoContinue="=2" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="Set Control Part Number and Price" ConditionExpression="" Enabled="true">
          <vars>
            <var name="Control_PartNumber">
              <value>=LookupMatrix("Control_Misc")</value>
            </var>
            <var name="Control_Price">
              <value>=LookupMatrix("Pricing_Control")</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Condition" Caption="If Customer Code has Value" ConditionExpression="=CustCode &lt;&gt; &quot;&quot;" Enabled="true">
            <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="If Markup is True" ConditionExpression="=Markup = TRUE" Enabled="true">
              <vars>
                <var name="Total_Price7">
                  <value>=Control_Price * (1+(FirstPrice/100))</value>
                </var>
                <var name="ConfiguredPrice">
                  <value>=ConfiguredPrice + Total_Price7</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Detail" Caption="Display Control Price" ConditionExpression="" Enabled="false">
                <Details>
                  <Detail Category="PriceDetails" PrintCode="" PrintSequence="0" Description="Control Cost" Visible="=True" Value="=Control_Price" />
                </Details>
              </Rule>
            </Rule>
            <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="If Markup is False" ConditionExpression="=Markup = FALSE" Enabled="true">
              <vars>
                <var name="Total_Price7">
                  <value>=Control_Price  * ((100 - abs(FirstPrice)) / 100)</value>
                </var>
                <var name="ConfiguredPrice">
                  <value>=ConfiguredPrice + Total_Price7</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Detail" Caption="Display Control Price" ConditionExpression="" Enabled="false">
                <Details>
                  <Detail Category="PriceDetails" PrintCode="" PrintSequence="0" Description="Control Cost" Visible="=True" Value="=Control_Price" />
                </Details>
              </Rule>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="Set Harness List" ConditionExpression="=Product &lt;&gt; &quot;660&quot;" Enabled="true">
      <vars>
        <var name="Harness">
          <value>=LookupMatrix("HarnessMatrix")</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Screen" Caption="Display Harness Default" ConditionExpression="" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="">
          <ScreenOption Sequence="1" Name="Harness" Caption="Harness Default" AutoContinue="=2" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="Set Harness Part Number and Price" ConditionExpression="" Enabled="true">
          <vars>
            <var name="Harness_PartNumber">
              <value>=Harness</value>
            </var>
            <var name="Harness_Price">
              <value>=LookupMatrix("Pricing_Harness")</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Condition" Caption="If Customer Code has Value" ConditionExpression="=CustCode &lt;&gt; &quot;&quot;" Enabled="true">
            <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="If Markup is True" ConditionExpression="=Markup = TRUE" Enabled="true">
              <vars>
                <var name="Total_Price8">
                  <value>=Harness_Price * (1+(FirstPrice/100))</value>
                </var>
                <var name="ConfiguredPrice">
                  <value>=ConfiguredPrice + Total_Price8</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Detail" Caption="Display Harness Price" ConditionExpression="" Enabled="false">
                <Details>
                  <Detail Category="PriceDetails" PrintCode="" PrintSequence="0" Description="Harness Cost" Visible="=True" Value="=Harness_Price" />
                </Details>
              </Rule>
            </Rule>
            <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Variable" Caption="If Markup is False" ConditionExpression="=Markup = FALSE" Enabled="true">
              <vars>
                <var name="Total_Price8">
                  <value>=Harness_Price  * ((100 - abs(FirstPrice)) / 100)</value>
                </var>
                <var name="ConfiguredPrice">
                  <value>=ConfiguredPrice + Total_Price8</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="AG Products" ChildType="true" RuleTypeName="Detail" Caption="Display Harness Price" ConditionExpression="" Enabled="false">
                <Details>
                  <Detail Category="PriceDetails" PrintCode="" PrintSequence="0" Description="Harness Cost" Visible="=True" Value="=Harness_Price" />
                </Details>
              </Rule>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
  </RuleTree>
</IncludeRuleset>