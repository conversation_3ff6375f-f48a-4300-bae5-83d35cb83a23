<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adapter Field Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .result {
            background: #e8f5e8;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .error {
            background: #ffe8e8;
            color: #d00;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <h1>Adapter Field Test</h1>
    
    <div class="test-section">
        <h2>Test Configuration</h2>
        <p><strong>Application:</strong> Agriculture</p>
        <p><strong>Less AG Mini Pack:</strong> No (Unchecked)</p>
        <p><strong>Make:</strong> Ford</p>
        <p><strong>Year:</strong> 2024</p>
        <p><strong>Vehicle Type:</strong> Truck</p>
        <p><strong>Engine:</strong> Ford 6.7L Diesel</p>
        <p><strong>Pump Size:</strong> 9 Gal Pump</p>
        <p><strong>Kit:</strong> 700612 Single Alt/1 Sided Belt</p>
        <p><strong>Clutch:</strong> 8 Groove Ogura "A"</p>
        <p><strong>Expected Adapter:</strong> 03-51261; S=16HB-16MB90; P=10MJ-12MB45</p>
    </div>

    <div class="test-section">
        <h2>Matrix Lookup Test</h2>
        <button onclick="testAdapterLookup()">Test Adapter Lookup</button>
        <div id="test-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>Debug Information</h2>
        <div id="debug-info" class="result" style="display: none;"></div>
    </div>

    <script type="module">
        import { evaluateMatrixValue } from './matrix-loader.js';
        import { fields } from './truck-hydraulic-system-fields.js';

        window.testAdapterLookup = async function() {
            const resultDiv = document.getElementById('test-result');
            const debugDiv = document.getElementById('debug-info');
            
            resultDiv.style.display = 'block';
            debugDiv.style.display = 'block';
            
            try {
                // Test form state matching your inputs
                const testFormState = {
                    Application: 'AG',
                    AGKitLessMiniPack: false,
                    Make: 'FD',
                    Year: '2024',
                    Vehicle_Type: 'TRUCK',
                    Engine: 'FD67D',
                    Pump: '9GL',
                    Kit: '700612',
                    Clutch: '8GRVA'
                };

                debugDiv.innerHTML = '<h3>Test Form State:</h3>' + JSON.stringify(testFormState, null, 2);

                // Test the adapter field's getValue function
                const adapterField = fields.Adapter;
                if (adapterField && adapterField.getValue) {
                    console.log('Testing adapter field with state:', testFormState);
                    
                    const adapterValue = await adapterField.getValue(testFormState);
                    
                    resultDiv.innerHTML = `
                        <h3>Adapter Result:</h3>
                        <strong>Calculated Value:</strong> ${adapterValue}<br>
                        <strong>Expected Value:</strong> 03-51261; S=16HB-16MB90; P=10MJ-12MB45<br>
                        <strong>Match:</strong> ${adapterValue === '03-51261' ? '✅ Partial Match (adapter code)' : '❌ No Match'}
                    `;
                    
                    // Also test the matrix lookup directly
                    const kitLast4 = testFormState.Kit.slice(-4);
                    const directLookup = await evaluateMatrixValue('MultipleAdapterGroups', {
                        'Kit (Skip first 4)': kitLast4,
                        'Pump': testFormState.Pump,
                        'AG': 'B'
                    }, { debug: true });
                    
                    debugDiv.innerHTML += `
                        <h3>Direct Matrix Lookup:</h3>
                        <strong>Kit Last 4:</strong> ${kitLast4}<br>
                        <strong>Pump:</strong> ${testFormState.Pump}<br>
                        <strong>AG:</strong> B<br>
                        <strong>Matrix Result:</strong> ${directLookup}
                    `;
                    
                } else {
                    resultDiv.innerHTML = '<span class="error">❌ Adapter field or getValue function not found</span>';
                }
                
            } catch (error) {
                console.error('Test error:', error);
                resultDiv.innerHTML = `<span class="error">❌ Error: ${error.message}</span>`;
                debugDiv.innerHTML += `<h3>Error Details:</h3>${error.stack}`;
            }
        };
    </script>
</body>
</html>
