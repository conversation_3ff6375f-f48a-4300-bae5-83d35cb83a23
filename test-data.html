<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configurator Test Data</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        pre {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .controls {
            margin-bottom: 20px;
        }
        button {
            margin: 5px;
            padding: 8px 15px;
        }
    </style>
</head>
<body>
    <h1>Configurator Test Data Viewer</h1>
    
    <div class="controls">
        <button onclick="switchConfigurator('hydraulic')">Show Hydraulic System Data</button>
        <button onclick="switchConfigurator('butler')">Show Butler Manufacturing Data</button>
        <button onclick="toggleFormat('json')">Show as JSON</button>
        <button onclick="toggleFormat('sql')">Show as SQL</button>
    </div>

    <pre id="output">Loading...</pre>

    <script type="module">
        import { generateTestData } from './test-data-generator.js';

        window.currentFormat = 'json';
        window.currentType = 'hydraulic';
        
        window.switchConfigurator = (type) => {
            window.currentType = type;
            updateDisplay();
        };
        
        window.toggleFormat = (format) => {
            window.currentFormat = format;
            updateDisplay();
        };
        
        function updateDisplay() {
            const testData = generateTestData(window.currentType, true);
            const output = document.getElementById('output');
            
            if (window.currentFormat === 'json') {
                output.textContent = testData.toJSON();
            } else {
                output.textContent = testData.toSQL();
            }
        }
        
        // Initial display
        updateDisplay();
    </script>
</body>
</html>
