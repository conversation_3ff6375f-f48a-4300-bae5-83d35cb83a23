# Butler Manufacturing Configurator (SYS<PERSON>Butler) - Entity Relationship Diagram

## Overview

This document describes the data relationships and entity structure for the Butler Manufacturing Configurator (SYS.Butler), a specialized configurator for Butler truck beds and accessories. This system focuses on truck bed configuration, toolbox selection, hydraulic power units, and various truck accessories.

## ERD Core Entities

```mermaid
erDiagram
    %% Core Entities
    TRUCK {
        string truck_id_PK
        string make
        string year
        string axle_type
    }

    BUTLER_CONFIGURATION {
        string config_id_PK
        string truck_id_FK
        decimal total_price
        string status
    }

    BED {
        string bed_id_PK
        string bed_model
        decimal base_price
    }

    TOOLBOX {
        string toolbox_id_PK
        string toolbox_type
        decimal base_price
    }

    HYDRAULIC_POWER_UNIT {
        string unit_id_PK
        string unit_type
        decimal base_price
    }

    %% Configuration Line Items
    BUTLER_CONFIG_LINE_ITEM {
        string line_item_id_PK
        string config_id_FK
        string component_type
        string part_number
        decimal line_total
    }

    %% Simplified Relationships
    TRUCK ||--o{ BUTLER_CONFIGURATION : "has"
    BUTLER_CONFIGURATION ||--o{ BUTLER_CONFIG_LINE_ITEM : "contains"

    %% Main Components
    BED ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"
    TOOLBOX ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"
    HYDRAULIC_POWER_UNIT ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"

    %% Bed-Toolbox Compatibility (Many-to-Many)
    BED }o--o{ TOOLBOX : "supports"
```

## ERD Diagram

```mermaid
%% Butler Manufacturing Configurator (SYS.Butler) - Data Relationships
erDiagram
    %% Core Truck Information
    TRUCK {
        string truck_id_PK
        string make
        string year
        string axle_type
        string cab_axle_config
        boolean is_ford
        boolean know_bed_preference
    }

    %% Butler Configuration
    BUTLER_CONFIGURATION {
        string butler_config_id_PK
        string truck_id_FK
        string customer_code
        decimal total_price
        datetime created_date
        string status
        boolean wireless_control
        boolean power_unit_required
    }

    %% Bed and Bed Features
    BED {
        string bed_id_PK
        string bed_model
        string bed_type
        string bed_feature
        string description
        decimal base_price
    }

    BED_MOUNT {
        string bed_mount_id_PK
        string bed_model_FK
        string make
        string year
        string cab_axle
        string axle_type
        string mount_part_number
    }

    %% Toolbox Components
    TOOLBOX {
        string toolbox_id_PK
        string toolbox_part_number
        string toolbox_type
        string size_description
        decimal base_price
    }

    TOOLBOX_COMPATIBILITY {
        string compatibility_id_PK
        string bed_id_FK
        string toolbox_id_FK
        string position_type
        string left_part_number
        string right_part_number
    }

    %% Hydraulic Components
    HYDRAULIC_POWER_UNIT {
        string hyd_unit_id_PK
        string unit_part_number
        string unit_type
        string description
        decimal base_price
    }

    %% Accessories and Features
    HEADACHE_RACK {
        string headache_id_PK
        string rack_part_number
        string rack_type
        decimal base_price
    }

    WORK_LIGHTS {
        string lights_id_PK
        string lights_part_number
        string light_type
        decimal base_price
    }

    RECEIVER_HITCH {
        string hitch_id_PK
        string hitch_part_number
        string hitch_type
        decimal base_price
    }

    FIFTH_WHEEL_BALL {
        string ball_id_PK
        string ball_part_number
        string ball_type
        decimal base_price
    }

    %% Wire Harness and Adapters
    WIRE_HARNESS {
        string harness_id_PK
        string harness_part_number
        string make_compatibility
        string year_compatibility
        decimal base_price
    }

    BUTLER_ADAPTER {
        string adapter_id_PK
        string adapter_part_number
        string adapter_description
        string make_compatibility
        string year_compatibility
        string cab_axle_compatibility
        string bed_type_compatibility
        decimal base_price
    }

    %% Mount Kits
    MOUNT_KIT {
        string mount_kit_id_PK
        string kit_part_number
        string make
        string year
        string axle_type
        string cab_axle
        string bed_type
        decimal base_price
    }

    %% Configuration Line Items (BOM)
    BUTLER_CONFIG_LINE_ITEM {
        string line_item_id_PK
        string butler_config_id_FK
        string component_type
        string part_number
        integer quantity
        decimal unit_price
        decimal line_total
    }

    %% Compatibility Matrices
    TRUCK_OPTIONS_MATRIX {
        string truck_opt_id_PK
        string make
        string year
        string axle
        string cab_axle
        string bed_type
        string compatible_options
    }

    BUTLER_MOUNT_KIT_MATRIX {
        string butler_mk_id_PK
        string make
        string year
        string axle
        string cab_axle
        string bed_type
        string mount_kit_part_number
    }

    BUTLER_ADAPTER_HARNESS_MATRIX {
        string adapter_harness_id_PK
        string make
        string year
        string axle
        string cab_axle
        string bed_type
        string adapter_harness_parts
    }

    DEWEZE_BED_MOUNT_MATRIX {
        string deweze_mount_id_PK
        string cab_axle
        string axle
        string bed_model
        string make
        string year
        string mount_part_number
    }

    BUTLER_SKIRTED_MATRIX {
        string skirted_id_PK
        string bed_id_FK
        string lh37_part
        string lh40_part
        string lh42_part
        string lh55_part
        string lh56_part
        string lh57_part
        string lh60_part
    }

    %% Relationships
    TRUCK ||--o{ BUTLER_CONFIGURATION : "has"
    BUTLER_CONFIGURATION ||--o{ BUTLER_CONFIG_LINE_ITEM : "contains"

    %% Bed relationships
    BED ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"
    BED ||--o{ BED_MOUNT : "requires"
    BED ||--o{ TOOLBOX_COMPATIBILITY : "supports"
    BED ||--o{ BUTLER_SKIRTED_MATRIX : "has_skirted_options"

    %% Component relationships to configuration
    TOOLBOX ||--o{ TOOLBOX_COMPATIBILITY : "available_for"
    TOOLBOX ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"
    HYDRAULIC_POWER_UNIT ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"
    HEADACHE_RACK ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"
    WORK_LIGHTS ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"
    RECEIVER_HITCH ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"
    FIFTH_WHEEL_BALL ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"
    WIRE_HARNESS ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"
    BUTLER_ADAPTER ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"
    MOUNT_KIT ||--o{ BUTLER_CONFIG_LINE_ITEM : "included_in"

    %% Matrix relationships for compatibility
    TRUCK ||--o{ TRUCK_OPTIONS_MATRIX : "determines_options"
    TRUCK ||--o{ BUTLER_MOUNT_KIT_MATRIX : "determines_mount_kit"
    TRUCK ||--o{ BUTLER_ADAPTER_HARNESS_MATRIX : "determines_adapter"
    TRUCK ||--o{ DEWEZE_BED_MOUNT_MATRIX : "determines_bed_mount"

    %% Many-to-many relationships
    BUTLER_CONFIGURATION }o--o{ TOOLBOX : "left_hand_toolbox"
    BUTLER_CONFIGURATION }o--o{ TOOLBOX : "right_hand_toolbox"
    BUTLER_CONFIGURATION }o--o{ TOOLBOX : "across_bed_toolbox"
```

## Entity Descriptions

### Core Entities

#### TRUCK

Stores the fundamental truck specifications for Butler bed configuration.

- **truck_id_PK**: Unique identifier for each truck
- **make**: Vehicle manufacturer (Ford, Chevrolet, Ram, etc.)
- **year**: Vehicle year
- **axle_type**: Axle configuration (Single, Dual)
- **cab_axle_config**: Cab and chassis or bed length configuration
- **is_ford**: Boolean flag for Ford-specific handling
- **know_bed_preference**: Whether customer knows their bed preference

#### BUTLER_CONFIGURATION

Main configuration record for Butler bed and accessory selections.

- **butler_config_id_PK**: Unique configuration identifier
- **truck_id_FK**: Reference to the truck being configured
- **customer_code**: Customer-specific pricing code
- **total_price**: Final calculated price including all components
- **created_date**: Configuration creation timestamp
- **status**: Configuration status (draft, submitted, approved, etc.)
- **wireless_control**: Whether wireless control is selected
- **power_unit_required**: Whether hydraulic power unit is required

### Bed and Mounting Components

#### BED

Central entity representing Butler truck bed models and configurations.

- **bed_id_PK**: Unique bed identifier
- **bed_model**: Specific bed model number
- **bed_type**: Type of bed (Standard, Heavy Duty)
- **bed_feature**: Bed feature (Straight, Tapered, Troughed, Skirted)
- **description**: Human-readable bed description
- **base_price**: Base price for the bed

#### BED_MOUNT

Mounting hardware and compatibility for specific bed models.

- **bed_mount_id_PK**: Unique bed mount identifier
- **bed_model_FK**: Reference to the bed model
- **make**: Compatible vehicle make
- **year**: Compatible vehicle year
- **cab_axle**: Compatible cab/axle configuration
- **axle_type**: Compatible axle type
- **mount_part_number**: Specific mounting hardware part number

### Toolbox Components

#### TOOLBOX

Toolbox components available for Butler bed configurations.

- **toolbox_id_PK**: Unique toolbox identifier
- **toolbox_part_number**: Manufacturing part number
- **toolbox_type**: Type of toolbox (Front, Rear, Across Bed)
- **size_description**: Size and dimension description
- **base_price**: Base price for the toolbox

#### TOOLBOX_COMPATIBILITY

Compatibility matrix for toolboxes with specific bed models.

- **compatibility_id_PK**: Unique compatibility identifier
- **bed_id_FK**: Reference to compatible bed
- **toolbox_id_FK**: Reference to compatible toolbox
- **position_type**: Position where toolbox can be mounted
- **left_part_number**: Part number for left-hand installation
- **right_part_number**: Part number for right-hand installation

### Hydraulic and Power Components

#### HYDRAULIC_POWER_UNIT

Hydraulic power units for Butler bed operation.

- **hyd_unit_id_PK**: Unique hydraulic unit identifier
- **unit_part_number**: Manufacturing part number
- **unit_type**: Type of hydraulic unit (SP, DP)
- **description**: Unit description and specifications
- **base_price**: Base price for the hydraulic unit

### Accessories and Features

#### HEADACHE_RACK

Headache rack components for truck bed protection.

- **headache_id_PK**: Unique headache rack identifier
- **rack_part_number**: Manufacturing part number
- **rack_type**: Type of headache rack (Standard, Deluxe)
- **base_price**: Base price for the headache rack

#### WORK_LIGHTS

Work light components for enhanced visibility.

- **lights_id_PK**: Unique work lights identifier
- **lights_part_number**: Manufacturing part number
- **light_type**: Type of work lights
- **base_price**: Base price for the work lights

#### RECEIVER_HITCH

Receiver hitch components for towing capability.

- **hitch_id_PK**: Unique receiver hitch identifier
- **hitch_part_number**: Manufacturing part number
- **hitch_type**: Type of receiver hitch
- **base_price**: Base price for the receiver hitch

#### FIFTH_WHEEL_BALL

Fifth wheel ball components for heavy-duty towing.

- **ball_id_PK**: Unique fifth wheel ball identifier
- **ball_part_number**: Manufacturing part number
- **ball_type**: Type of fifth wheel ball (Gooseneck, 5th Wheel)
- **base_price**: Base price for the fifth wheel ball

### Electrical and Mounting Components

#### WIRE_HARNESS

Wire harness components for electrical connections.

- **harness_id_PK**: Unique wire harness identifier
- **harness_part_number**: Manufacturing part number
- **make_compatibility**: Compatible vehicle makes
- **year_compatibility**: Compatible vehicle years
- **base_price**: Base price for the wire harness

#### BUTLER_ADAPTER

Butler-specific adapter components for vehicle integration.

- **adapter_id_PK**: Unique Butler adapter identifier
- **adapter_part_number**: Manufacturing part number (e.g., 232162, 232164)
- **adapter_description**: Adapter description and compatibility
- **make_compatibility**: Compatible vehicle makes
- **year_compatibility**: Compatible vehicle years
- **cab_axle_compatibility**: Compatible cab/axle configurations
- **bed_type_compatibility**: Compatible bed types
- **base_price**: Base price for the adapter

#### MOUNT_KIT

Mount kit components for bed installation.

- **mount_kit_id_PK**: Unique mount kit identifier
- **kit_part_number**: Manufacturing part number
- **make**: Compatible vehicle make
- **year**: Compatible vehicle year
- **axle_type**: Compatible axle type
- **cab_axle**: Compatible cab/axle configuration
- **bed_type**: Compatible bed type
- **base_price**: Base price for the mount kit

### Configuration Management

#### BUTLER_CONFIG_LINE_ITEM

Bill of Materials (BOM) line items for Butler configurations.

- **line_item_id_PK**: Unique line item identifier
- **butler_config_id_FK**: Reference to parent Butler configuration
- **component_type**: Type of component (Bed, Toolbox, Hydraulic, etc.)
- **part_number**: Manufacturing part number
- **quantity**: Quantity of this component
- **unit_price**: Price per unit
- **line_total**: Extended line total (quantity × unit_price)

### Compatibility Matrix Tables

#### TRUCK_OPTIONS_MATRIX

Determines compatible options based on truck specifications.

- **truck_opt_id_PK**: Unique truck options identifier
- **make**: Vehicle make
- **year**: Vehicle year
- **axle**: Axle configuration
- **cab_axle**: Cab/axle configuration
- **bed_type**: Bed type
- **compatible_options**: Available compatible options

#### BUTLER_MOUNT_KIT_MATRIX

Determines compatible Butler mount kits based on truck specifications.

- **butler_mk_id_PK**: Unique Butler mount kit identifier
- **make**: Vehicle make
- **year**: Vehicle year
- **axle**: Axle configuration
- **cab_axle**: Cab/axle configuration
- **bed_type**: Bed type
- **mount_kit_part_number**: Compatible mount kit part number

#### BUTLER_ADAPTER_HARNESS_MATRIX

Determines compatible Butler adapters and harnesses.

- **adapter_harness_id_PK**: Unique adapter/harness identifier
- **make**: Vehicle make
- **year**: Vehicle year
- **axle**: Axle configuration
- **cab_axle**: Cab/axle configuration
- **bed_type**: Bed type
- **adapter_harness_parts**: Compatible adapter and harness part numbers

#### DEWEZE_BED_MOUNT_MATRIX

Determines compatible Deweze bed mounting options.

- **deweze_mount_id_PK**: Unique Deweze mount identifier
- **cab_axle**: Cab/axle configuration
- **axle**: Axle configuration
- **bed_model**: Bed model
- **make**: Vehicle make
- **year**: Vehicle year
- **mount_part_number**: Compatible mount part number

#### BUTLER_SKIRTED_MATRIX

Determines skirted bed options and part numbers.

- **skirted_id_PK**: Unique skirted options identifier
- **bed_id_FK**: Reference to bed model
- **lh37_part**: Left-hand 37" part number
- **lh40_part**: Left-hand 40" part number
- **lh42_part**: Left-hand 42" part number
- **lh55_part**: Left-hand 55" part number
- **lh56_part**: Left-hand 56" part number
- **lh57_part**: Left-hand 57" part number
- **lh60_part**: Left-hand 60" part number

## Key Relationships

### Primary Relationships

1. **TRUCK → BUTLER_CONFIGURATION** (1:Many): Each truck can have multiple Butler configurations
2. **BUTLER_CONFIGURATION → BUTLER_CONFIG_LINE_ITEM** (1:Many): Each configuration contains multiple line items
3. **Components → BUTLER_CONFIG_LINE_ITEM** (1:Many): Each component can appear in multiple configurations

### Bed-Centric Relationships

- **BED → BED_MOUNT** (1:Many): Each bed model has specific mounting requirements
- **BED → TOOLBOX_COMPATIBILITY** (1:Many): Each bed supports specific toolbox configurations
- **BED → BUTLER_SKIRTED_MATRIX** (1:1): Each bed has skirted options matrix

### Toolbox Relationships

- **TOOLBOX → TOOLBOX_COMPATIBILITY** (1:Many): Each toolbox is compatible with multiple beds
- **BUTLER_CONFIGURATION → TOOLBOX** (Many:Many): Configurations can have multiple toolboxes in different positions

### Matrix Filtering Relationships

- **TRUCK → Matrix Tables** (1:Many): Truck specifications drive compatibility filtering
- Matrix tables determine which components are compatible with specific truck configurations

## Business Rules

### Configuration Flow

1. User selects truck specifications (make, year, axle type, cab/axle configuration)
2. User indicates if they know their bed preference
3. System uses matrix tables to filter available beds and components
4. User selects bed model and features
5. User selects toolboxes for different positions (left-hand, right-hand, across-bed)
6. User selects accessories (hydraulic power unit, headache rack, work lights, etc.)
7. System generates BOM with pricing and creates configuration record

### Special Handling

- **Ford Vehicles**: Special handling for mounting tabs and Ford-specific features
- **Wireless Control**: Optional wireless control for hydraulic operations
- **Power Unit Requirements**: Conditional hydraulic power unit selection
- **Toolbox Positioning**: Multiple toolbox positions with specific part numbers

### Bed Configuration Rules

- **Skirted Beds**: Have specific part numbers for different lengths (37", 40", 42", 55", 56", 57", 60")
- **Mount Kits**: Determined by truck specifications and bed type
- **Adapters**: Vehicle-specific adapters for electrical integration

## Component Integration

### Toolbox System

- **Left-Hand Toolbox**: Mounted on driver side of bed
- **Right-Hand Toolbox**: Mounted on passenger side of bed
- **Across-Bed Toolbox**: Mounted across the width of the bed
- Each position has specific part numbers and compatibility requirements

### Hydraulic System

- **Power Units**: SP (Single Port) or DP (Dual Port) configurations
- **Wireless Control**: Optional remote control capability
- **Integration**: Connects with bed hydraulic systems for operation

### Electrical System

- **Wire Harness**: Vehicle-specific electrical connections
- **Butler Adapters**: Specialized adapters for different vehicle makes and years
- **Work Lights**: Optional lighting for enhanced visibility

## Manufacturing Integration

The Butler configurator integrates with manufacturing systems by:

1. Generating detailed BOM with all selected components
2. Providing specific part numbers for each component
3. Supporting customer-specific pricing
4. Creating manufacturing work orders for bed assembly

## Database Schema Considerations

### Indexing Strategy

- Primary keys on all entities for fast lookups
- Foreign key indexes for relationship queries
- Composite indexes on matrix tables for filtering operations

### Data Integrity

- Foreign key constraints ensure referential integrity
- Check constraints on boolean fields
- Validation rules for part number formats

### Performance Optimization

- Matrix tables optimized for fast compatibility lookups
- Denormalized pricing data for quick calculations
- Cached compatibility results for common configurations

This ERD provides the foundation for a comprehensive Butler truck bed configuration system that supports complex compatibility rules, multiple toolbox positions, and extensive accessory options while maintaining manufacturing integration capabilities.
