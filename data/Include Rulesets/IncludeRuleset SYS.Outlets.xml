<IncludeRuleset Namespace="SYS" Name="Outlets" EntityId="e5967642-51d3-4cbe-915c-8ee367ac595c">
  <Tags />
  <ComponentAttributes />
  <RuleTree Type="IncludeRuleset">
    <Rule Namespace="SYS" Ruleset="Outlets" ChildType="true" RuleTypeName="Screen" Caption="Ask Outlet" ConditionExpression="" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="Outlets" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
      <Rule Namespace="SYS" Ruleset="Outlets" ChildType="true" RuleTypeName="Variable" Caption="W/O Outlets" ConditionExpression="=Outlets=False" Enabled="true">
        <vars>
          <var name="BigMatrix">
            <value>=lookupmatrix("GroupIndex")</value>
          </var>
          <var name="Outlets_sel">
            <value>=BigMatrix["OH"]</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Outlets" ChildType="true" RuleTypeName="Detail" Caption="&lt; New Detail Rule &gt;" ConditionExpression="" Enabled="true">
          <Details>
            <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Hydraulic Plumbing" Visible="=True" Value="=Outlets_sel" />
          </Details>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Outlets" ChildType="true" RuleTypeName="Variable" Caption="With Outlets" ConditionExpression="=Outlets=True" Enabled="true">
        <vars>
          <var name="BigMatrix">
            <value>=lookupmatrix("GroupIndex")</value>
          </var>
          <var name="Outlets_sel">
            <value>=BigMatrix["NH"]</value>
          </var>
          <var name="Adder">
            <value>=Outlets_Price</value>
          </var>
          <var name="Runtotal">
            <value>=Runtotal+Adder</value>
          </var>
          <var name="ConfiguredPrice">
            <value>=Runtotal</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Outlets" ChildType="true" RuleTypeName="Detail" Caption="&lt; New Detail Rule &gt;" ConditionExpression="" Enabled="true">
          <Details>
            <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Hydraulic Plumbing W/ Outlets" Visible="=True" Value="=Outlets_sel" />
            <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="B) Rear Hydr. Outlet Upgrade" Visible="=True" Value="=Adder" />
          </Details>
        </Rule>
      </Rule>
    </Rule>
  </RuleTree>
</IncludeRuleset>