# SYS-700CPK Configurator Documentation

## Overview

This configurator dynamically builds a form for the SYS-700CPK truck hydraulic system, supporting both Agriculture (AG) and Commercial (COM) applications. The form fields, dependencies, and option logic are defined in `truck-hydraulic-system-fields.js` and rendered by `sys-700cpk.js`.

## Main Files

- **truck-hydraulic-system-fields.js**: Field definitions, dynamic option logic, and groupings.
- **sys-700cpk.js**: Form rendering, state management, and event handling.
- **sys-700cpk.html**: The HTML container for the form.

## Field Groups

- **Truck Selections**: Always shown. Includes Application, Make, Year, Engine, Pump, Kit, Clutch, Adapter, etc.
- **AG Questions**: Shown when Application is 'AG'. Includes Product, Valve, Cab, Chassis, Control, Harness, etc.
- **Commercial Questions**: Shown when Application is 'COM'. Includes Product, Cab, Chassis, Control, Harness.

## Dynamic Logic

- Field options are loaded from XML files and filtered by previous selections and matrix lookups.
- Display fields (e.g., Adapter) are calculated based on form state.
- Changing a field resets dependent fields to ensure valid combinations.

## Adding/Editing Fields

- Add new fields to the `fields` object in `truck-hydraulic-system-fields.js`.
- To change field order or grouping, edit `getFieldGroups()`.
- To add new option logic, implement an `async getOptions(formState)` function for the field.

## Example Scenarios

See the top of this file or the project README for example data entry scenarios and expected results.

## Developer Notes

- Use the browser console for debug output.
- Fallback options are provided for offline/demo use.
- Matrices and XML option lists are in the `data/` directory.

---

For more details, see inline comments in the code and the project README.
