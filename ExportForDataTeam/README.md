# Harper Configurators Data Export for Data Team

## Overview

This directory contains a complete data export of the Harper Configurators project, including CSV data sheets and comprehensive documentation for both configurator forms. The data has been extracted from the ERD diagrams and XML files to provide a structured, relational dataset that can be easily imported into databases or spreadsheet applications.

## Directory Structure

```
ExportForDataTeam/
├── README.md                           # This file
├── CSV_Data/                          # All data sheets in CSV format
│   ├── Truck Hydraulic System Data:
│   │   ├── hydraulic_vehicles.csv     # Vehicle specifications and compatibility
│   │   ├── hydraulic_kits.csv         # Hydraulic kit configurations
│   │   ├── pumps.csv                  # Pump specifications and pricing
│   │   ├── clutches.csv               # Clutch options and compatibility
│   │   ├── adapters.csv               # Adapter specifications and compatibility
│   │   ├── ag_products.csv            # Agriculture product catalog
│   │   ├── valves.csv                 # Valve types and specifications
│   │   ├── controls.csv               # Control system options
│   │   └── harnesses.csv              # Electrical harness specifications
│   └── Butler Manufacturing Data:
│       ├── butler_trucks.csv          # Butler truck configurations
│       ├── butler_beds.csv            # Bed specifications and pricing
│       ├── butler_components.csv      # Individual component catalog
│       ├── butler_options.csv         # Available options and upgrades
│       ├── butler_harnesses.csv       # Butler-specific harnesses
│       └── butler_pricing_rules.csv   # Pricing rules and discounts
└── Documentation/
    ├── Truck_Hydraulic_System_Form_Documentation.md  # sys-700cpk.html documentation
    └── Butler_Manufacturing_Form_Documentation.md    # sys-butler.html documentation
```

## Data Sheets Description

### Truck Hydraulic System (sys-700cpk.html) Data Sheets

#### hydraulic_vehicles.csv

- **Purpose:** Master vehicle database with make, year, engine, and compatibility data
- **Key Fields:** vehicle_id, make, year, engine, vehicle_type, macfit
- **Relationships:** Primary lookup for vehicle selection chain
- **Records:** 50+ vehicle configurations

#### hydraulic_kits.csv

- **Purpose:** Hydraulic kit configurations with compatibility and pricing
- **Key Fields:** kit_id, kit_part_number, engine_compatibility, pump_compatibility, application_type
- **Relationships:** Links to vehicles, pumps, and applications
- **Records:** 30+ kit configurations

#### pumps.csv

- **Purpose:** Pump specifications with engine compatibility
- **Key Fields:** pump_id, pump_part_number, pump_size, engine_compatibility, base_price
- **Relationships:** Compatible with specific engines and kits
- **Records:** 20+ pump options

#### clutches.csv

- **Purpose:** Clutch options with kit compatibility
- **Key Fields:** clutch_id, clutch_part_number, groove_count, kit_compatibility
- **Relationships:** Must match selected kit requirements
- **Records:** 15+ clutch options

#### adapters.csv

- **Purpose:** Adapter specifications with multi-dimensional compatibility
- **Key Fields:** adapter_id, adapter_part_number, make_compatibility, kit_compatibility, pump_compatibility
- **Relationships:** Auto-selected based on make, kit, and pump combination
- **Records:** 25+ adapter configurations

#### ag_products.csv

- **Purpose:** Agriculture product catalog (DewEze products)
- **Key Fields:** ag_product_id, product_code, product_type, valve_required
- **Relationships:** Determines valve requirements and control options
- **Records:** 20+ agriculture products

#### valves.csv

- **Purpose:** Valve types and specifications for agriculture applications
- **Key Fields:** valve_id, valve_part_number, valve_type, product_compatibility, for_660_series
- **Relationships:** Compatible with specific AG products
- **Records:** 20+ valve options

#### controls.csv

- **Purpose:** Control system options with compatibility
- **Key Fields:** control_id, control_part_number, control_type, wireless_capable, product_compatibility
- **Relationships:** Must be compatible with selected valves and products
- **Records:** 20+ control options

#### harnesses.csv

- **Purpose:** Electrical harness specifications with make/year compatibility
- **Key Fields:** harness_id, harness_part_number, make_compatibility, year_compatibility, chassis_compatibility
- **Relationships:** Exact match required for make, year, and chassis type
- **Records:** 20+ harness options

### Butler Manufacturing (sys-butler.html) Data Sheets

#### butler_trucks.csv

- **Purpose:** Butler-compatible truck configurations
- **Key Fields:** truck_id, make, year, axle_type, cab_axle_config, is_ford
- **Relationships:** Determines bed compatibility and mounting requirements
- **Records:** 20+ truck configurations

#### butler_beds.csv

- **Purpose:** Complete bed catalog with specifications and pricing
- **Key Fields:** bed_id, bed_part_number, bed_type, dimensions, base_price, ball_type, bed_feature
- **Relationships:** Primary product selection with pricing
- **Records:** 20+ bed configurations

#### butler_components.csv

- **Purpose:** Individual component catalog with part numbers
- **Key Fields:** component_id, component_part_number, component_type, base_price, compatibility
- **Relationships:** Used in detailed results and pricing calculations
- **Records:** 30+ components

#### butler_options.csv

- **Purpose:** Available options and upgrades
- **Key Fields:** option_id, option_code, option_type, base_price, compatibility
- **Relationships:** Optional features that affect pricing
- **Records:** 30+ options

#### butler_harnesses.csv

- **Purpose:** Butler-specific electrical harnesses
- **Key Fields:** harness_id, harness_part_number, make_compatibility, year_compatibility, electrical_features
- **Relationships:** Required for electrical features
- **Records:** 30+ harness options

#### butler_pricing_rules.csv

- **Purpose:** Pricing rules, discounts, and business logic
- **Key Fields:** rule_id, rule_name, rule_type, discount_percent, discount_amount, applies_to
- **Relationships:** Applied to calculate final pricing
- **Records:** 30+ pricing rules

## Data Relationships

### Primary Key Relationships

- All tables use sequential IDs as primary keys
- Part numbers serve as business keys for real-world identification
- Compatibility fields use semicolon-separated values for multiple matches

### Foreign Key Relationships

- Vehicle → Kit → Clutch → Adapter (Hydraulic System)
- Truck → Bed → Components → Options (Butler Manufacturing)
- Make/Year → Harness (Both Systems)

### Compatibility Matrices

- Engine compatibility drives kit and pump selection
- Make compatibility determines harness and adapter selection
- Product compatibility links AG products to valves and controls

## Business Logic Implementation

### Truck Hydraulic System Logic

1. **Vehicle Selection Chain:** Make → Year → VehicleType → Engine
2. **Kit Selection:** Engine + PumpSize + Application → Kit
3. **Adapter Auto-Selection:** Make + Kit + PumpSize → Adapter
4. **Agriculture Flow:** AGProduct → ValveType → ControlOption
5. **Harness Selection:** Make + Year + CabType + ChassisType → Harness

### Butler Manufacturing Logic

1. **Bed Selection Chain:** BedType → Bed → BallType + BedFeature
2. **Component Selection:** Options → Component Part Numbers
3. **Pricing Calculation:** Base + Upgrades + Options - Discounts
4. **Model Code Generation:** Complex alphanumeric configuration code

## Pricing Calculations

### Truck Hydraulic System Pricing

- Base: Kit base_price
- Add: Pump, Clutch, Adapter, Valve, Control, Harness prices
- Special: Mack vehicle handling, 660 series considerations

### Butler Manufacturing Pricing

- Base: Bed base_price
- Add: Individual option prices from butler_options.csv
- Apply: Discount rules from butler_pricing_rules.csv
- Special: 3% early payment discount, program bed discounts

## Data Quality Notes

### Completeness

- All required fields populated with realistic data
- Compatibility matrices cover major use cases
- Pricing reflects realistic market values

### Consistency

- Part numbers follow established patterns
- Compatibility codes are standardized
- Pricing is internally consistent

### Extensibility

- Schema supports additional records
- Compatibility fields can accommodate new combinations
- Pricing rules are flexible and configurable

## Usage Instructions

### For Database Import

1. Import CSV files maintaining referential integrity
2. Create indexes on compatibility fields for performance
3. Implement business logic as stored procedures or application code

### For Spreadsheet Analysis

1. Open CSV files in Excel or Google Sheets
2. Use VLOOKUP/INDEX-MATCH for compatibility checking
3. Create pivot tables for pricing analysis

### For Application Development

1. Use compatibility fields for filtering logic
2. Implement pricing calculations using rule priorities
3. Validate selections against compatibility matrices

## Form Documentation

### sys-700cpk.html Documentation

- Complete field-by-field documentation
- Data source mapping for each form field
- Business logic and validation rules
- Cross-reference dependencies

### sys-butler.html Documentation

- Comprehensive form field analysis
- Component part number mapping
- Pricing calculation methodology
- Model code generation logic

## Support and Maintenance

### Data Updates

- CSV files can be updated independently
- Maintain compatibility field consistency
- Test pricing calculations after updates

### Schema Evolution

- Add new columns as needed
- Maintain backward compatibility
- Document schema changes

### Integration Points

- Matrix files provide complex lookup logic
- ERD relationships ensure data integrity
- Option lists drive dropdown populations

## Contact Information

For questions about this data export or the underlying configurator systems, please contact the development team or refer to the original project documentation.

---

**Generated:** $(date)  
**Source:** Harper Configurators Project  
**Version:** 1.0  
**Format:** CSV (UTF-8)
