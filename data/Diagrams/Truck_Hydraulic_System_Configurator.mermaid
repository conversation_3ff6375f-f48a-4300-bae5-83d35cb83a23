%% Truck Hydraulic System Configurator (SYS.700CPK) - Data Relationships
erDiagram
    %% Core Vehicle Information
    VEHICLE {
        string vehicle_id_PK
        string make
        string year
        string vehicle_type
        string engine
        string application
        boolean macfit
    }
    %% Configuration Entity
    CONFIGURATION {
        string config_id_PK
        string vehicle_id_FK
        string customer_code
        decimal first_price
        decimal total_price
        datetime created_date
        string status
    }
    %% Component Entities
    KIT {
        string kit_id_PK
        string kit_part_number
        string description
        decimal base_price
        string kit_group
    }
    PUMP {
        string pump_id_PK
        string pump_part_number
        string pump_size
        string pump_type
        decimal base_price
        string pump_group
    }
    CLUTCH {
        string clutch_id_PK
        string clutch_part_number
        string clutch_type
        decimal base_price
        string clutch_group
    }
    ADAPTER {
        string adapter_id_PK
        string adapter_part_number
        string adapter_type
        decimal base_price
        string adapter_group
    }
    %% AG-Specific Components (Agricultural Application)
    AG_PRODUCT {
        string ag_product_id_PK
        string product_type
        string product_series
        boolean valve_required
        string description
    }
    VALVE {
        string valve_id_PK
        string valve_part_number
        string valve_type
        decimal base_price
        boolean for_660_series
    }
    CONTROL {
        string control_id_PK
        string control_part_number
        string control_type
        decimal base_price
    }
    HOSE {
        string hose_id_PK
        string hose_part_number
        string hose_type
        decimal base_price
        string application_type
    }
    HARNESS {
        string harness_id_PK
        string harness_part_number
        string make_compatibility
        string year_compatibility
        string chassis_compatibility
        decimal base_price
    }
    RESERVOIR {
        string reservoir_id_PK
        string reservoir_part_number
        string capacity
        decimal base_price
        boolean for_660_series
    }
    %% Configuration Line Items (BOM)
    CONFIG_LINE_ITEM {
        string line_item_id_PK
        string config_id_FK
        string component_type
        string part_number
        integer quantity
        decimal unit_price
        decimal line_total
    }
    %% Matrix/Filter Tables
    VT_FILTER_MATRIX {
        string filter_id_PK
        string make
        string year
        string vehicle_type_result
    }
    ENGINE_GROUP_MATRIX {
        string engine_group_id_PK
        string make
        string vehicle_type
        string year
        string engine_groups
    }
    PUMP_GROUP_MATRIX {
        string pump_group_id_PK
        string engine
        string application_type
        string vehicle_type
        string year
        string pump_groups
    }
    KIT_GROUP_MATRIX {
        string kit_group_id_PK
        string engine
        string year
        string pump
        string vehicle_type
        string application
        string kit_groups
    }
    ADAPTER_GROUP_MATRIX {
        string adapter_group_id_PK
        string kit_substring
        string pump
        string ag_flag
        string adapter_groups
    }
    %% Relationships
    VEHICLE ||--o{ CONFIGURATION : "has"
    CONFIGURATION ||--o{ CONFIG_LINE_ITEM : "contains"
    %% Component relationships to configuration
    KIT ||--o{ CONFIG_LINE_ITEM : "included_in"
    PUMP ||--o{ CONFIG_LINE_ITEM : "included_in"
    CLUTCH ||--o{ CONFIG_LINE_ITEM : "included_in"
    ADAPTER ||--o{ CONFIG_LINE_ITEM : "included_in"
    VALVE ||--o{ CONFIG_LINE_ITEM : "included_in"
    CONTROL ||--o{ CONFIG_LINE_ITEM : "included_in"
    HOSE ||--o{ CONFIG_LINE_ITEM : "included_in"
    HARNESS ||--o{ CONFIG_LINE_ITEM : "included_in"
    RESERVOIR ||--o{ CONFIG_LINE_ITEM : "included_in"
    %% AG Product relationships
    AG_PRODUCT ||--o{ VALVE : "may_require"
    AG_PRODUCT ||--o{ CONTROL : "may_include"
    AG_PRODUCT ||--o{ HOSE : "may_include"
    AG_PRODUCT ||--o{ RESERVOIR : "may_include"
    %% Matrix relationships for filtering
    VEHICLE ||--o{ VT_FILTER_MATRIX : "filters_by"
    VEHICLE ||--o{ ENGINE_GROUP_MATRIX : "determines"
    VEHICLE ||--o{ PUMP_GROUP_MATRIX : "determines"
    VEHICLE ||--o{ KIT_GROUP_MATRIX : "determines"
    VEHICLE ||--o{ ADAPTER_GROUP_MATRIX : "determines"