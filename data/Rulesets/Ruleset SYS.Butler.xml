<Ruleset Namespace="SYS" Name="Butler" EntityId="8df0d81b-72d7-4965-add4-2c0cceeb13fa">
  <Tags />
  <ComponentAttributes>
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="Arms" DataType="String" DefaultValue="AL" Caption="Arm Length:" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="Arm_Length" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="Axle" DataType="String" Caption="Single or Dual Axle:" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="Axle" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="BallType" DataType="String" DefaultValue="R" Caption="5th Wheel Ball:" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="Ball" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="Bed" DataType="String" Caption="Choose a bed:" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="Beds" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="BedFeature" DataType="String" Caption="Straight/Tapered/Troughed/Skirted:" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="Bed_Feature" DisplayFormat="" DisplayPrecision="" DisplayType="DropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="BedType" DataType="String" Caption="Bed Type:" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="Bed_Type" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="CabAxle" DataType="String" Caption="Cab and Chassis or Bed Length:" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="Cab_Axle" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="ControlType" DataType="Boolean" DefaultValue="False" Caption="Do you want a wireless control?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="Headache" DataType="String" Caption="Headache Rack:" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="Headache_Rack" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="HydPowerFlat" DataType="Boolean" Caption="Do you want a power unit?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="HydPwrUnit" DataType="String" DefaultValue="SP" Caption="Hydraulic Power Unit:" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="Hydraulic_Unit" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="ISFord" DataType="Boolean" Caption="Do you have a Ford?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="LHToolbox" DataType="String" Caption="Left Hand Toolbox:" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="Toolboxes" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="Make" DataType="String" Caption="Select Make of Vehicle:" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="Make" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="MntTabs" DataType="Boolean" Caption="Do you want mounting tabs (Ford Only)?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="MountKit" DataType="Boolean" Caption="Do you want a bed mounting kit?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="Outlets" DataType="Boolean" DefaultValue="False" Caption="Do you want outlets?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="RHToolbox" DataType="String" Caption="Right Hand Toolbox:" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="FrontRightLong" OptionListID="Toolboxes" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="RubRails" DataType="Boolean" DefaultValue="False" Caption="Do you want rub rails?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="SideRails" DataType="Boolean" Caption="Do you want side rails?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="Skirted" DataType="Boolean" DefaultValue="False" Caption="Do you want a skirted bed?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="Spikes" DataType="String" DefaultValue="BR" Caption="Spikes:" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="Spikes" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="SpinLength" DataType="String" DefaultValue="SS" Caption="Spinners:" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="Spinners" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="Tailboard" DataType="Boolean" DefaultValue="True" Caption="Do you want a receiver hitch?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="TruckConfig" DataType="Boolean" Caption="Do you know what bed you want?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="Wireharness" DataType="Boolean" Caption="Do you want a wire harness?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="WorkLights" DataType="Boolean" DefaultValue="False" Caption="Do you want work lights?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="XBed" DataType="String" DefaultValue="NN" Caption="Across the Bed Toolbox:" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="XB" OptionListID="Toolboxes" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="Years" DataType="String" Caption="Truck Year:" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="Years" DisplayFormat="" DisplayPrecision="" DisplayType="DropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="ButlerAdapter" DataType="String" Caption="Select Your Adapter" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="ButlerAdapter" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="XBed2" DataType="Boolean" DefaultValue="False" Caption="Do you want an across the bed toolbox (2 doors)?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="XB" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="WeldTab" DataType="Boolean" Caption="Do you want your tabs welded on?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="Assembly_Comp" DataType="Boolean" DefaultValue="False" Caption="Do you want the toolboxes and E/H system installed?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="AdapeterHarness" DataType="Boolean" Caption="Do you want an adapter harness?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="DropForks" DataType="Boolean" DefaultValue="False" Caption="Do you want drop forks?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="FrontToolbox" DataType="String" Caption="Front Toolbox:" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="Toolboxes" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="RearToolbox" DataType="Boolean" DefaultValue="False" Caption="Do you want rear toolboxes?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="2" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="Mudflaps" DataType="Boolean" Caption="Do you want mudflaps?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="TBDodge" DataType="Boolean" Caption="Is this a Dodge?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="2" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="Arm_Spikes" DataType="Boolean" Caption="Do you want spikes included in crosstube?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="Arm_Spikes_Bracket" DataType="Boolean" Caption="Do you want brackets for spikes included with order?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="SkirtCA" DataType="String" Caption="What is your Cab/Axlle Lenght?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="ButlerSkirtCA" DisplayFormat="" DisplayPrecision="" DisplayType="TypeableDropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="CustomerOrder" DataType="Boolean" Caption="Is this a Customer order (No is Harper Stock)?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="CustomPrice" DataType="Number" DefaultValue="0" Caption="What is the custom price adder?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="false" DefaultGroup="" OptionListID="" DisplayFormat="Decimal" DisplayPrecision="2" DisplayType="NumericTextBox" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="CustomOrder" DataType="Boolean" Caption="Does this order have customization with added price?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="true" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="MountKit_sel" DataType="String" Caption="Select Mount Kit:" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="false" DefaultGroup="" OptionListID="ButlerMountKits" DisplayFormat="" DisplayPrecision="" DisplayType="DropDown" PrintCode="" />
    <ComponentAttribute Namespace="SYS" Ruleset="Butler" Name="RTBrackets" DataType="Boolean" DefaultValue="False" Caption="Do you want rear toolbox brackets?" Description="" IsLocked="false" IsVisible="true" IsPersistToOutput="true" Tooltip="" InformationMessage="" InfoLink="" AppLink="" IsHighlight="false" AutoContinue="1" IsRequired="false" DefaultGroup="" OptionListID="" DisplayFormat="" DisplayPrecision="" DisplayType="RadioButtonHorizontal" PrintCode="" />
  </ComponentAttributes>
  <RuleTree Type="Interactive">
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Select Database" ConditionExpression="" Enabled="true">
      <vars>
        <var name="ApDB">
          <value>=IF(sys.Application IN {"SalesPortal", "SYTELINE_PROD"},"HARPER_APP","PILOT_APP")</value>
        </var>
        <var name="SL_BILLTO_ID">
          <value>=IF(sys.Application &lt;&gt; "SALES BUYDESIGN","SL_BILLTO","SYS_BILLTO_ID")</value>
        </var>
      </vars>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Global Default Variables" ConditionExpression="" Enabled="true">
      <vars>
        <var name="ConfiguredPrice">
          <value>=0</value>
        </var>
      </vars>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Customer or Stock Order" ConditionExpression="" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="CustomerOrder" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Set Default Variables" ConditionExpression="=sys.Application in {&quot;Syteline&quot;,&quot;SytelineProd&quot;,&quot;SalesPortal&quot;}" Enabled="false">
      <vars>
        <var name="HeaderID">
          <value>=Left(sys.headerid,Length(sys.headerid) - 2)</value>
        </var>
      </vars>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Customer Order" ConditionExpression="=CustomerOrder=True" Enabled="true">
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Set Default Variables" ConditionExpression="=sys.Application in {&quot;Syteline&quot;,&quot;SytelineProd&quot;,&quot;SalesPortal&quot;}" Enabled="true">
        <vars>
          <var name="HeaderID">
            <value>=Left(sys.headerid,Length(sys.headerid) - 2)</value>
          </var>
          <var name="CustCode">
            <value>=usr.GetCustCode</value>
          </var>
          <var name="FirstPrice">
            <value>=usr.GetFirstPrice</value>
          </var>
          <var name="Markup">
            <value>=IF(FirstPrice &gt; 0,TRUE,FALSE)</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="false" RuleTypeName="Variable" Caption="Set Default Variables" ConditionExpression="" Enabled="true">
          <vars>
            <var name="FirstPrice">
              <value>=0</value>
            </var>
            <var name="CustCode">
              <value>=usr.GetCustCodeDev</value>
            </var>
            <var name="Markup">
              <value>=FALSE</value>
            </var>
          </vars>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Stock Order" ConditionExpression="=CustomerOrder=False" Enabled="false">
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Set Default Variables" ConditionExpression="=sys.Application in {&quot;Syteline&quot;,&quot;SytelineProd&quot;,&quot;SalesPortal&quot;}" Enabled="true">
        <vars>
          <var name="HeaderID">
            <value>=Left(sys.headerid,Length(sys.headerid) - 2)</value>
          </var>
          <var name="FirstPrice">
            <value>=usr.ButlerGetFirstPrice</value>
          </var>
          <var name="Markup">
            <value>=IF(FirstPrice &gt; 0,TRUE,FALSE)</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="false" RuleTypeName="Variable" Caption="Set Default Variables" ConditionExpression="" Enabled="true">
          <vars>
            <var name="FirstPrice">
              <value>=0</value>
            </var>
            <var name="Markup">
              <value>=FALSE</value>
            </var>
          </vars>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Pricing Library" ConditionExpression="" Enabled="true">
      <Comment>Used to store pricing for non optionlist selections  Option List with Price Beds; Ball; Headache_Rack; Spikes;Toolboxes(includesXB).
Finish Group Discount (shipping group parts) is box of parts like power unit, hardware, mudflap brackets, etc... Arm 205250 Spike 205251 Reg 205252
100000=issue must be resolved
200000=Now in bed Price
300000=Now in Headache rack price
400000=Discontinued offering</Comment>
      <vars>
        <var name="Skirt_Price">
          <value>=862.00</value>
        </var>
        <var name="Trough_Price">
          <value>=445.00</value>
        </var>
        <var name="RubRail_Price">
          <value>=335.00</value>
        </var>
        <var name="ABSpike_Price">
          <value>=334.00</value>
        </var>
        <var name="AB_Brackets_Price">
          <value>=67.00</value>
        </var>
        <var name="Outlets_Price">
          <value>=307.00</value>
        </var>
        <var name="Wireless_Price">
          <value>=273.00</value>
        </var>
        <var name="DropForks_Price">
          <value>=355.00</value>
        </var>
        <var name="SpikePwrUnitUpgrade_Price">
          <value>=605.33</value>
        </var>
        <var name="SideRails_Price">
          <value>=216.00</value>
        </var>
        <var name="RegularPwrUnitStd_Price_OBSOLETE">
          <value>=400000</value>
        </var>
        <var name="RegularPwrUnitHigh_Price_OBSOLETE">
          <value>=400000</value>
        </var>
        <var name="FB36_Pricing">
          <value>=82.00</value>
        </var>
        <var name="RearTB_Price">
          <value>=710.00</value>
        </var>
        <var name="WorkLights_Price">
          <value>=118.00</value>
        </var>
        <var name="AdapterHarness_Price">
          <value>=34.00</value>
        </var>
        <var name="BedHarness_Price">
          <value>=200000</value>
        </var>
        <var name="STTHarness_Price">
          <value>=300000</value>
        </var>
        <var name="MountKit_Price">
          <value>=220.00</value>
        </var>
        <var name="FinishAsm_Price">
          <value>=400000</value>
        </var>
        <var name="RearTBMNT_Price">
          <value>=36.00</value>
        </var>
        <var name="Mudflaps_Price">
          <value>=30.00</value>
        </var>
        <var name="MudflapMountRT_Price">
          <value>=28.00</value>
        </var>
        <var name="MountTab_Price">
          <value>=40.00</value>
        </var>
        <var name="Arm_Finish_Group_Disc">
          <value>=1363.45</value>
        </var>
        <var name="Spike_Finish_Group_Disc">
          <value>=758.12</value>
        </var>
        <var name="Reg_Finish_Group_Disc">
          <value>=20.51</value>
        </var>
      </vars>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Bed Type" ConditionExpression="" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="BedType" OptionListID="Bed_Type" OptionListGroup="Butler" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask If Bed is Known" ConditionExpression="" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="TruckConfig" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Include Ruleset" Caption="Truck Configuration" ConditionExpression="=TruckConfig=False" Enabled="true">
      <RuleSet namespace="SYS" type="SR" rulesetid="Truck" />
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Bed Choice (Truck Info)" ConditionExpression="=TruckConfig=False" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="Bed" OptionListID="Beds" OptionListGroup="=GroupUnion(Split(LookUpMatrix(&quot;TruckOpt&quot;,Make, Years, Axle, CabAxle, BedType),&quot;-&quot;))" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Bed Choice (Bed is known)" ConditionExpression="=TruckConfig=True" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="Bed" OptionListGroup="=BedType.Value" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail BOM Title" ConditionExpression="" Enabled="false">
      <Details>
        <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="BOM" Visible="=True" Value="" />
      </Details>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Base Price" ConditionExpression="" Enabled="true">
      <vars>
        <var name="Adder">
          <value>=0</value>
        </var>
        <var name="Runtotal">
          <value>=0</value>
        </var>
        <var name="Adder">
          <value>=Bed.Value.Price</value>
        </var>
        <var name="Runtotal">
          <value>=Runtotal+Adder</value>
        </var>
        <var name="ConfiguredPrice">
          <value>=Runtotal</value>
        </var>
        <var name="LooseOptPrice">
          <value>=0</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
        <Details>
          <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Bed" Visible="=True" Value="=Bed" />
          <Detail Category="OrderDetails" PrintCode="" PrintSequence="11" Description="---------------------------------------------------------------------------" Visible="=True" Value="-----------------------------------------------------------------------" />
          <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="A) Base Price" Visible="=True" Value="=Adder" />
        </Details>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Bed Feature" ConditionExpression="" Enabled="true">
      <vars>
        <var name="BigMatrix">
          <value>=lookupmatrix("GroupIndex")</value>
        </var>
        <var name="BedWidth">
          <value>=BigMatrix["BW"]</value>
        </var>
        <var name="BedLength">
          <value>=BigMatrix["BL"]</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Ball" ConditionExpression="" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
          <ScreenOption Sequence="1" Name="BallType" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Standard 5th wheel bed feature options" ConditionExpression="=BallType=&quot;R&quot;" Enabled="true">
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Length &gt;100 &amp; &lt;103" ConditionExpression="=BedLength&gt;100 &amp; BedLength&lt;103" Enabled="true">
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Arm Bed" ConditionExpression="=BedType=&quot;A&quot;" Enabled="true">
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Bed Feature" ConditionExpression="=BedWidth &lt;= 84" Enabled="true">
                <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
                  <ScreenOption Sequence="1" Name="BedFeature" OptionListID="Bed_Feature" OptionListGroup="Arm Narrow 101" SelectOptionsImageLinkFieldExpression="ImageLink" />
                </Screen>
                <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Bed Feature Narrow" ConditionExpression="" Enabled="true">
                  <vars>
                    <var name="BigMatrix">
                      <value>=lookupmatrix("GroupIndex")</value>
                    </var>
                    <var name="BedFeature_sel">
                      <value>=BigMatrix[BedFeature]</value>
                    </var>
                  </vars>
                  <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                    <Details>
                      <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Deck" Visible="=True" Value="=BedFeature_sel" />
                    </Details>
                  </Rule>
                </Rule>
              </Rule>
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Bed Feature" ConditionExpression="=BedWidth &gt; 84" Enabled="true">
                <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
                  <ScreenOption Sequence="1" Name="BedFeature" OptionListID="Bed_Feature" OptionListGroup="Arm Wide 101" SelectOptionsImageLinkFieldExpression="ImageLink" />
                </Screen>
                <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Bed Feature Wide" ConditionExpression="" Enabled="true">
                  <vars>
                    <var name="BigMatrix">
                      <value>=lookupmatrix("GroupIndex")</value>
                    </var>
                    <var name="BedFeature_sel">
                      <value>=BigMatrix[BedFeature]</value>
                    </var>
                  </vars>
                  <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                    <Details>
                      <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Deck" Visible="=True" Value="=BedFeature_sel" />
                    </Details>
                  </Rule>
                </Rule>
              </Rule>
            </Rule>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Spike Regular Bed" ConditionExpression="=BedType=&quot;R&quot; or BedType=&quot;S&quot;" Enabled="true">
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Bed Feature" ConditionExpression="=BedWidth &lt;= 84" Enabled="true">
                <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
                  <ScreenOption Sequence="1" Name="BedFeature" OptionListID="Bed_Feature" OptionListGroup="RS Narrow 101" SelectOptionsImageLinkFieldExpression="ImageLink" />
                </Screen>
                <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Bed Feature Narrow" ConditionExpression="" Enabled="true">
                  <vars>
                    <var name="BigMatrix">
                      <value>=lookupmatrix("GroupIndex")</value>
                    </var>
                    <var name="BedFeature_sel">
                      <value>=BigMatrix[BedFeature]</value>
                    </var>
                  </vars>
                  <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                    <Details>
                      <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Deck" Visible="=True" Value="=BedFeature_sel" />
                    </Details>
                  </Rule>
                </Rule>
              </Rule>
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Bed Feature" ConditionExpression="=BedWidth &gt; 84" Enabled="true">
                <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
                  <ScreenOption Sequence="1" Name="BedFeature" OptionListID="Bed_Feature" OptionListGroup="RS Wide 101" SelectOptionsImageLinkFieldExpression="ImageLink" />
                </Screen>
                <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Bed Feature Wide" ConditionExpression="" Enabled="true">
                  <vars>
                    <var name="BigMatrix">
                      <value>=lookupmatrix("GroupIndex")</value>
                    </var>
                    <var name="BedFeature_sel">
                      <value>=BigMatrix[BedFeature]</value>
                    </var>
                  </vars>
                  <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                    <Details>
                      <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Deck" Visible="=True" Value="=BedFeature_sel" />
                    </Details>
                  </Rule>
                </Rule>
              </Rule>
            </Rule>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Length &lt;100 or &gt;103" ConditionExpression="=BedLength&lt;100 or BedLength&gt;103" Enabled="true">
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Arm Bed" ConditionExpression="=BedType=&quot;A&quot;" Enabled="true">
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Bed Feature" ConditionExpression="=BedWidth &lt;= 84" Enabled="true">
                <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
                  <ScreenOption Sequence="1" Name="BedFeature" OptionListID="Bed_Feature" OptionListGroup="Arm Narrow" SelectOptionsImageLinkFieldExpression="ImageLink" />
                </Screen>
                <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Bed Feature Narrow" ConditionExpression="" Enabled="true">
                  <vars>
                    <var name="BigMatrix">
                      <value>=lookupmatrix("GroupIndex")</value>
                    </var>
                    <var name="BedFeature_sel">
                      <value>=BigMatrix[BedFeature]</value>
                    </var>
                  </vars>
                  <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                    <Details>
                      <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Deck" Visible="=True" Value="=BedFeature_sel" />
                    </Details>
                  </Rule>
                </Rule>
              </Rule>
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Bed Feature" ConditionExpression="=BedWidth &gt; 84" Enabled="true">
                <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
                  <ScreenOption Sequence="1" Name="BedFeature" OptionListID="Bed_Feature" OptionListGroup="Arm Wide" SelectOptionsImageLinkFieldExpression="ImageLink" />
                </Screen>
                <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Bed Feature Wide" ConditionExpression="" Enabled="true">
                  <vars>
                    <var name="BigMatrix">
                      <value>=lookupmatrix("GroupIndex")</value>
                    </var>
                    <var name="BedFeature_sel">
                      <value>=BigMatrix[BedFeature]</value>
                    </var>
                  </vars>
                  <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                    <Details>
                      <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Deck" Visible="=True" Value="=BedFeature_sel" />
                    </Details>
                  </Rule>
                </Rule>
              </Rule>
            </Rule>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Spike Regular Bed" ConditionExpression="=BedType=&quot;R&quot; or BedType=&quot;S&quot;" Enabled="true">
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Bed Feature" ConditionExpression="=BedWidth &lt;= 84" Enabled="true">
                <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
                  <ScreenOption Sequence="1" Name="BedFeature" OptionListID="Bed_Feature" OptionListGroup="RS Narrow" SelectOptionsImageLinkFieldExpression="ImageLink" />
                </Screen>
                <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Bed Feature Narrow" ConditionExpression="" Enabled="true">
                  <vars>
                    <var name="BigMatrix">
                      <value>=lookupmatrix("GroupIndex")</value>
                    </var>
                    <var name="BedFeature_sel">
                      <value>=BigMatrix[BedFeature]</value>
                    </var>
                  </vars>
                  <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                    <Details>
                      <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Deck" Visible="=True" Value="=BedFeature_sel" />
                    </Details>
                  </Rule>
                </Rule>
              </Rule>
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Bed Feature" ConditionExpression="=BedWidth &gt; 84" Enabled="true">
                <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
                  <ScreenOption Sequence="1" Name="BedFeature" OptionListID="Bed_Feature" OptionListGroup="RS Wide" SelectOptionsImageLinkFieldExpression="ImageLink" />
                </Screen>
                <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Bed Feature Wide" ConditionExpression="" Enabled="true">
                  <vars>
                    <var name="BigMatrix">
                      <value>=lookupmatrix("GroupIndex")</value>
                    </var>
                    <var name="BedFeature_sel">
                      <value>=BigMatrix[BedFeature]</value>
                    </var>
                  </vars>
                  <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                    <Details>
                      <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Deck" Visible="=True" Value="=BedFeature_sel" />
                    </Details>
                  </Rule>
                </Rule>
              </Rule>
            </Rule>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="B&amp;W 5th wheel bed feature options" ConditionExpression="=BallType=&quot;P&quot;" Enabled="true">
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Bed Feature" ConditionExpression="=BedWidth &lt;= 84" Enabled="true">
            <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
              <ScreenOption Sequence="1" Name="BedFeature" OptionListID="Bed_Feature" OptionListGroup="Narrow BW" SelectOptionsImageLinkFieldExpression="ImageLink" />
            </Screen>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Bed Feature Narrow" ConditionExpression="" Enabled="true">
              <vars>
                <var name="BigMatrix">
                  <value>=lookupmatrix("GroupIndex")</value>
                </var>
                <var name="BedFeature_sel">
                  <value>=BigMatrix[BedFeature]</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                <Details>
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Deck" Visible="=True" Value="=BedFeature_sel" />
                </Details>
              </Rule>
            </Rule>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Bed Feature" ConditionExpression="=BedWidth &gt; 84" Enabled="true">
            <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
              <ScreenOption Sequence="1" Name="BedFeature" OptionListID="Bed_Feature" OptionListGroup="Wide BW" SelectOptionsImageLinkFieldExpression="ImageLink" />
            </Screen>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Bed Feature Wide" ConditionExpression="" Enabled="true">
              <vars>
                <var name="BigMatrix">
                  <value>=lookupmatrix("GroupIndex")</value>
                </var>
                <var name="BedFeature_sel">
                  <value>=BigMatrix[BedFeature]</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                <Details>
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Deck" Visible="=True" Value="=BedFeature_sel" />
                </Details>
              </Rule>
            </Rule>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="B&amp;W" ConditionExpression="=BallType=&quot;P&quot;" Enabled="true">
          <vars>
            <var name="Adder">
              <value>=BallType.Value.Price</value>
            </var>
            <var name="Runtotal">
              <value>=Runtotal+Adder</value>
            </var>
            <var name="ConfiguredPrice">
              <value>=Runtotal</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="=BallType=&quot;P&quot;" Enabled="true">
            <Details>
              <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="B) B&amp;W 5th Wheel, Upgrade" Visible="=True" Value="=BallType.Value.Price" />
            </Details>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Ball" ConditionExpression="=BedFeature!=&quot;TT&quot; &amp; BedFeature!=&quot;TS&quot; &amp; BedFeature!=&quot;FF&quot; &amp; BedFeature!=&quot;SF&quot; &amp; BedFeature!=&quot;US&quot;" Enabled="false">
          <vars>
            <var name="BigMatrix">
              <value>=lookupmatrix("GroupIndex")</value>
            </var>
            <var name="BallType_sel">
              <value>=BigMatrix[BallType]</value>
            </var>
            <var name="Adder">
              <value>=BallType.Value.Price</value>
            </var>
            <var name="Runtotal">
              <value>=Runtotal+Adder</value>
            </var>
            <var name="ConfiguredPrice">
              <value>=Runtotal</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="=BallType=&quot;R&quot;" Enabled="true">
            <Details>
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="5th Wheel Hitch" Visible="=True" Value="=BallType_sel" />
            </Details>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="=BallType=&quot;P&quot;" Enabled="true">
            <Details>
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="5th Wheel Hitch" Visible="=True" Value="=BallType_sel" />
              <Detail Category="PriceDetails" PrintCode="E" PrintSequence="10" Description="B&amp;W 5th Wheel, Opt" Visible="=True" Value="=Adder" />
            </Details>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display OLD" ConditionExpression="" Enabled="false">
            <Details>
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="5th Wheel Hitch" Visible="=True" Value="=BallType_sel" />
              <Detail Category="PriceDetails" PrintCode="" PrintSequence="10" Description="=if(BallType.Value.Price&gt;0,&quot;Ball Price&quot;,null)" Visible="=True" Value="=if(BallType.Value.Price&gt;0,Adder,null)" />
            </Details>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="41&quot; Feeder Balls" ConditionExpression="=BedFeature=&quot;FF&quot; or BedFeature=&quot;SF&quot;" Enabled="false">
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Ball 41 Feeder" ConditionExpression="=BallType=&quot;R&quot;" Enabled="true">
            <Comment>Changes regular ball to 41" feeder ball weldment</Comment>
            <vars>
              <var name="BigMatrix">
                <value>=lookupmatrix("GroupIndex")</value>
              </var>
              <var name="BallType_sel">
                <value>=BigMatrix["F"]</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="5th Wheel Hitch" Visible="=True" Value="=BallType_sel" />
              </Details>
            </Rule>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display OLD" ConditionExpression="" Enabled="false">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="5th Wheel Hitch" Visible="=True" Value="=BallType_sel" />
                <Detail Category="PriceDetails" PrintCode="" PrintSequence="10" Description="=if(BallType.Value.Price&gt;0,&quot;Ball Price&quot;,null)" Visible="=True" Value="=if(BallType.Value.Price&gt;0,Adder,null)" />
              </Details>
            </Rule>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Ball 41 Feeder" ConditionExpression="=BallType=&quot;P&quot;" Enabled="true">
            <Comment>Changes regular ball to 41" feeder ball weldment</Comment>
            <vars>
              <var name="BigMatrix">
                <value>=lookupmatrix("GroupIndex")</value>
              </var>
              <var name="BallType_sel">
                <value>=BigMatrix[BallType]</value>
              </var>
              <var name="Adder">
                <value>=BallType.Value.Price</value>
              </var>
              <var name="Runtotal">
                <value>=Runtotal+Adder</value>
              </var>
              <var name="ConfiguredPrice">
                <value>=Runtotal</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="=BallType=&quot;P&quot;" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="5th Wheel Hitch" Visible="=True" Value="=BallType_sel" />
                <Detail Category="PriceDetails" PrintCode="" PrintSequence="10" Description="B&amp;W 5th Wheel, Opt" Visible="=True" Value="=Adder" />
              </Details>
            </Rule>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display OLD" ConditionExpression="" Enabled="false">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="5th Wheel Hitch" Visible="=True" Value="=BallType_sel" />
                <Detail Category="PriceDetails" PrintCode="" PrintSequence="10" Description="=if(BallType.Value.Price&gt;0,&quot;Ball Price&quot;,null)" Visible="=True" Value="=if(BallType.Value.Price&gt;0,Adder,null)" />
              </Details>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="SKIRT" ConditionExpression="=BedFeature=&quot;SK&quot; or BedFeature=&quot;SF&quot; or BedFeature=&quot;SW&quot; or BedFeature=&quot;US&quot;" Enabled="true">
      <vars>
        <var name="Adder">
          <value>=Skirt_Price</value>
        </var>
        <var name="Runtotal">
          <value>=Runtotal+Adder</value>
        </var>
        <var name="ConfiguredPrice">
          <value>=Runtotal</value>
        </var>
        <var name="Skirted">
          <value>=True</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="false" RuleTypeName="Variable" Caption="SKIRT" ConditionExpression="" Enabled="true">
        <vars>
          <var name="Skirted">
            <value>=False</value>
          </var>
        </vars>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
        <Details>
          <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="B) Skirting, Upgrade" Visible="=True" Value="=Adder" />
        </Details>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Skirts" ConditionExpression="=Skirted=True" Enabled="true">
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Skirted CA Selection Short BTO" ConditionExpression="=BedLength&lt;=86" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
          <ScreenOption Sequence="1" Name="SkirtCA" OptionListID="ButlerSkirtCA" OptionListGroup="SHORT BTO" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="&lt; New Variable Rule &gt;" ConditionExpression="" Enabled="true">
          <vars>
            <var name="SkirtMatrix">
              <value>=lookupmatrix("ButlerSkirted")</value>
            </var>
            <var name="LHSkirt">
              <value>=SkirtMatrix["LH"+SkirtCA]</value>
            </var>
            <var name="RHSkirt">
              <value>=SkirtMatrix["RH"+SkirtCA]</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="&lt; New Detail Rule &gt;" ConditionExpression="" Enabled="true">
            <Details>
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="LH Skirt" Visible="=True" Value="=LHSkirt" />
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="RH Skirt" Visible="=True" Value="=RHSkirt" />
            </Details>
          </Rule>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Skirted CA Selection Long BTO" ConditionExpression="=BedLength &lt;= 102 &amp; BedLength &gt; 86" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
          <ScreenOption Sequence="1" Name="SkirtCA" OptionListID="ButlerSkirtCA" OptionListGroup="LONG BTO" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="&lt; New Variable Rule &gt;" ConditionExpression="" Enabled="true">
          <vars>
            <var name="SkirtMatrix">
              <value>=lookupmatrix("ButlerSkirted")</value>
            </var>
            <var name="LHSkirt">
              <value>=SkirtMatrix["LH"+SkirtCA]</value>
            </var>
            <var name="RHSkirt">
              <value>=SkirtMatrix["RH"+SkirtCA]</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="&lt; New Detail Rule &gt;" ConditionExpression="" Enabled="true">
            <Details>
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="LH Skirt" Visible="=True" Value="=LHSkirt" />
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="RH Skirt" Visible="=True" Value="=RHSkirt" />
            </Details>
          </Rule>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Skirted CA Selection CC" ConditionExpression="=BedLength &gt; 102" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
          <ScreenOption Sequence="1" Name="SkirtCA" OptionListID="ButlerSkirtCA" OptionListGroup="CC" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="&lt; New Variable Rule &gt;" ConditionExpression="" Enabled="true">
          <vars>
            <var name="SkirtMatrix">
              <value>=lookupmatrix("ButlerSkirted")</value>
            </var>
            <var name="LHSkirt">
              <value>=SkirtMatrix["LH"+SkirtCA]</value>
            </var>
            <var name="RHSkirt">
              <value>=SkirtMatrix["RH"+SkirtCA]</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="&lt; New Detail Rule &gt;" ConditionExpression="" Enabled="true">
            <Details>
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="LH Skirt" Visible="=True" Value="=LHSkirt" />
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="RH Skirt" Visible="=True" Value="=RHSkirt" />
            </Details>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Dodge 7' toolbox" ConditionExpression="=BedLength&lt;=86" Enabled="true">
      <Comment>Needed to add in toolbox mount</Comment>
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="TBDodge" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Not Skirted" ConditionExpression="=Skirted=False" Enabled="true">
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="toolbox mount dodge 7'" ConditionExpression="=TBDodge=true" Enabled="true">
          <vars>
            <var name="TBDodge_sel">
              <value>280766</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
            <Details>
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Dodge Toolbox Mount" Visible="=True" Value="=TBDodge_sel" />
            </Details>
          </Rule>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Skirted" ConditionExpression="=Skirted=True" Enabled="true">
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Spike &amp; Regular" ConditionExpression="=BedType=&quot;S&quot; or BedType=&quot;R&quot;" Enabled="true">
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="toolbox mount dodge 7'" ConditionExpression="=TBDodge=true" Enabled="true">
            <vars>
              <var name="TBDodge_sel">
                <value>280766</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Dodge Toolbox Mount" Visible="=True" Value="=TBDodge_sel" />
              </Details>
            </Rule>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Arm" ConditionExpression="=BedType=&quot;A&quot;" Enabled="true">
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="toolbox mount dodge 7'" ConditionExpression="=TBDodge=true" Enabled="true">
            <vars>
              <var name="TBDodge_sel">
                <value>281021</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Dodge Toolbox Mount" Visible="=True" Value="=TBDodge_sel" />
              </Details>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Trough" ConditionExpression="=BedFeature=&quot;TT&quot; or BedFeature=&quot;TS&quot; or BedFeature=&quot;US&quot;" Enabled="true">
      <vars>
        <var name="Adder">
          <value>=Trough_Price</value>
        </var>
        <var name="Runtotal">
          <value>=Runtotal+Adder</value>
        </var>
        <var name="ConfiguredPrice">
          <value>=Runtotal</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
        <Details>
          <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="B) Trough Bed w/ Ball, Upgrade" Visible="=True" Value="=Adder" />
        </Details>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Headache Rack" ConditionExpression="" Enabled="true">
      <vars>
        <var name="BigMatrix">
          <value>=lookupmatrix("GroupIndex")</value>
        </var>
        <var name="BedLength">
          <value>=BigMatrix["BL"]</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Greater than 80&quot; Wide" ConditionExpression="=BedWidth &gt; 80" Enabled="true">
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Headache Rack Box Removal" ConditionExpression="=Bedlength &lt;=102" Enabled="true">
          <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
            <ScreenOption Sequence="1" Name="Headache" OptionListID="Headache_Rack" OptionListGroup="BoxRemoval" SelectOptionsImageLinkFieldExpression="ImageLink" />
          </Screen>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Headache Rack" ConditionExpression="" Enabled="true">
            <vars>
              <var name="BigMatrix">
                <value>=lookupmatrix("GroupIndex")</value>
              </var>
              <var name="Headache_sel">
                <value>=BigMatrix[Headache]</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description=" Headache Rack" Visible="=True" Value="=Headache_sel" />
              </Details>
            </Rule>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Headache Rack Lights" ConditionExpression="=Headache=&quot;SL&quot; or Headache=&quot;ML&quot; or Headache=&quot;TL&quot; or Headache=&quot;XL&quot;" Enabled="true">
            <vars>
              <var name="BigMatrix">
                <value>=lookupmatrix("GroupIndex")</value>
              </var>
              <var name="HeadacheLights_sel">
                <value>=BigMatrix["XX"]</value>
              </var>
              <var name="Adder">
                <value>=Headache.Value.Price</value>
              </var>
              <var name="Runtotal">
                <value>=Runtotal+Adder</value>
              </var>
              <var name="ConfiguredPrice">
                <value>=Runtotal</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Headache Rack Lights" Visible="=True" Value="=HeadacheLights_sel" />
                <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="B) STT H/A Rack Lights, Upgrade" Visible="=True" Value="=Adder" />
              </Details>
            </Rule>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Headache Rack Cab Chassis" ConditionExpression="=Bedlength &gt; 102" Enabled="true">
          <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
            <ScreenOption Sequence="1" Name="Headache" OptionListID="Headache_Rack" OptionListGroup="CabChassis" SelectOptionsImageLinkFieldExpression="ImageLink" />
          </Screen>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Headache Rack" ConditionExpression="" Enabled="true">
            <vars>
              <var name="BigMatrix">
                <value>=lookupmatrix("GroupIndex")</value>
              </var>
              <var name="Headache_sel">
                <value>=BigMatrix[Headache]</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Headache Rack" Visible="=True" Value="=Headache_sel" />
              </Details>
            </Rule>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Headache Rack Lights" ConditionExpression="=Headache=&quot;SL&quot; or Headache=&quot;ML&quot; or Headache=&quot;TL&quot; or Headache=&quot;XL&quot;" Enabled="true">
            <vars>
              <var name="BigMatrix">
                <value>=lookupmatrix("GroupIndex")</value>
              </var>
              <var name="HeadacheLights_sel">
                <value>=BigMatrix["XX"]</value>
              </var>
              <var name="Adder">
                <value>=Headache.Value.Price</value>
              </var>
              <var name="Runtotal">
                <value>=Runtotal+Adder</value>
              </var>
              <var name="ConfiguredPrice">
                <value>=Runtotal</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Headache Rack Lights" Visible="=True" Value="=HeadacheLights_sel" />
                <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="B) STT H/A Rack Lights, Upgrade" Visible="=True" Value="=Adder" />
              </Details>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="80&quot; Wide" ConditionExpression="=BedWidth = 80" Enabled="true">
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Headache Rack Box Removal" ConditionExpression="=Bedlength &lt;=102" Enabled="true">
          <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
            <ScreenOption Sequence="1" Name="Headache" OptionListID="Headache_Rack" OptionListGroup="BoxRemoval80" SelectOptionsImageLinkFieldExpression="ImageLink" />
          </Screen>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Headache Rack" ConditionExpression="" Enabled="true">
            <vars>
              <var name="BigMatrix">
                <value>=lookupmatrix("GroupIndex")</value>
              </var>
              <var name="Headache_sel">
                <value>=BigMatrix[Headache]</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description=" Headache Rack" Visible="=True" Value="=Headache_sel" />
              </Details>
            </Rule>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Headache Rack Lights" ConditionExpression="=Headache=&quot;SL&quot; or Headache=&quot;ML&quot; or Headache=&quot;TL&quot; or Headache=&quot;XL&quot;" Enabled="true">
            <vars>
              <var name="BigMatrix">
                <value>=lookupmatrix("GroupIndex")</value>
              </var>
              <var name="HeadacheLights_sel">
                <value>=BigMatrix["XX"]</value>
              </var>
              <var name="Adder">
                <value>=Headache.Value.Price</value>
              </var>
              <var name="Runtotal">
                <value>=Runtotal+Adder</value>
              </var>
              <var name="ConfiguredPrice">
                <value>=Runtotal</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Headache Rack Lights" Visible="=True" Value="=HeadacheLights_sel" />
                <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="B) STT H/A Rack Lights, Upgrade" Visible="=True" Value="=Adder" />
              </Details>
            </Rule>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Headache Rack Cab Chassis" ConditionExpression="=Bedlength &gt; 102" Enabled="true">
          <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
            <ScreenOption Sequence="1" Name="Headache" OptionListID="Headache_Rack" OptionListGroup="CabChassis80" SelectOptionsImageLinkFieldExpression="ImageLink" />
          </Screen>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Headache Rack" ConditionExpression="" Enabled="true">
            <vars>
              <var name="BigMatrix">
                <value>=lookupmatrix("GroupIndex")</value>
              </var>
              <var name="Headache_sel">
                <value>=BigMatrix[Headache]</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Headache Rack" Visible="=True" Value="=Headache_sel" />
              </Details>
            </Rule>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Headache Rack Lights" ConditionExpression="=Headache=&quot;SL&quot; or Headache=&quot;ML&quot; or Headache=&quot;TL&quot; or Headache=&quot;XL&quot;" Enabled="true">
            <vars>
              <var name="BigMatrix">
                <value>=lookupmatrix("GroupIndex")</value>
              </var>
              <var name="HeadacheLights_sel">
                <value>=BigMatrix["XX"]</value>
              </var>
              <var name="Adder">
                <value>=Headache.Value.Price</value>
              </var>
              <var name="Runtotal">
                <value>=Runtotal+Adder</value>
              </var>
              <var name="ConfiguredPrice">
                <value>=Runtotal</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Headache Rack Lights" Visible="=True" Value="=HeadacheLights_sel" />
                <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="B) STT H/A Rack Lights, Upgrade" Visible="=True" Value="=Adder" />
              </Details>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Receiver Spike and Regular" ConditionExpression="=BedType=&quot;R&quot; or BedType=&quot;S&quot;" Enabled="true">
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Receiver" ConditionExpression="=BedFeature!=&quot;TT&quot; or BedFeature!=&quot;TS&quot; or BedFeature!=&quot;US&quot;" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
          <ScreenOption Sequence="1" Name="Tailboard" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Not skirted or trough" ConditionExpression="=BedFeature=&quot;BT&quot; or BedFeature=&quot;BS&quot; or BedFeature=&quot;TB&quot; or BedFeature=&quot;SB&quot; or BedFeature=&quot;FF&quot;" Enabled="true">
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Receiver Hitch" ConditionExpression="=Tailboard=True" Enabled="true">
            <vars>
              <var name="BigMatrix">
                <value>=lookupmatrix("GroupIndex")</value>
              </var>
              <var name="Tailboard_sel">
                <value>=BigMatrix["TR"]</value>
              </var>
              <var name="Tailboard_model">
                <value>TR</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Tailboard" Visible="=True" Value="=Tailboard_sel" />
              </Details>
            </Rule>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Plate" ConditionExpression="=Tailboard=False" Enabled="true">
            <vars>
              <var name="BigMatrix">
                <value>=lookupmatrix("GroupIndex")</value>
              </var>
              <var name="Tailboard_sel">
                <value>=BigMatrix["TP"]</value>
              </var>
              <var name="Tailboard_model">
                <value>TP</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Tailboard" Visible="=True" Value="=Tailboard_sel" />
              </Details>
            </Rule>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Skirted" ConditionExpression="=Skirted=True" Enabled="true">
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Not Trough skirted" ConditionExpression="=BedFeature!=&quot;US&quot;" Enabled="true">
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Skirted Receiver Hitch" ConditionExpression="=Tailboard=True" Enabled="true">
              <vars>
                <var name="BigMatrix">
                  <value>=lookupmatrix("GroupIndex")</value>
                </var>
                <var name="Tailboard_sel">
                  <value>=BigMatrix["ST"]</value>
                </var>
                <var name="Tailboard_model">
                  <value>ST</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                <Details>
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Tailboard" Visible="=True" Value="=Tailboard_sel" />
                </Details>
              </Rule>
            </Rule>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Skirted Plate" ConditionExpression="=Tailboard=False" Enabled="true">
              <vars>
                <var name="BigMatrix">
                  <value>=lookupmatrix("GroupIndex")</value>
                </var>
                <var name="Tailboard_sel">
                  <value>=BigMatrix["SP"]</value>
                </var>
                <var name="Tailboard_model">
                  <value>SP</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                <Details>
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Tailboard" Visible="=True" Value="=Tailboard_sel" />
                </Details>
              </Rule>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Trough Receiver Hitch" ConditionExpression="=BedFeature=&quot;TT&quot; or BedFeature=&quot;TS&quot;" Enabled="true">
      <vars>
        <var name="BigMatrix">
          <value>=lookupmatrix("GroupIndex")</value>
        </var>
        <var name="Tailboard_sel">
          <value>=BigMatrix["TU"]</value>
        </var>
        <var name="Tailboard_model">
          <value>TU</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
        <Details>
          <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Tailboard" Visible="=True" Value="=Tailboard_sel" />
        </Details>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Trough Skirted Receiver Hitch" ConditionExpression="=BedFeature=&quot;US&quot;" Enabled="true">
      <vars>
        <var name="BigMatrix">
          <value>=lookupmatrix("GroupIndex")</value>
        </var>
        <var name="Tailboard_sel">
          <value>=BigMatrix["SU"]</value>
        </var>
        <var name="Tailboard_model">
          <value>SU</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
        <Details>
          <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Tailboard" Visible="=True" Value="=Tailboard_sel" />
        </Details>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Standard Ball" ConditionExpression="=BallType=&quot;R&quot;" Enabled="true">
      <vars>
        <var name="BigMatrix">
          <value>=lookupmatrix("GroupIndex")</value>
        </var>
        <var name="Ball">
          <value>=BigMatrix["X"]</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Ball" ConditionExpression="" Enabled="true">
        <Details>
          <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Standard Ball" Visible="=True" Value="=Ball" />
        </Details>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Rub Rails" ConditionExpression="" Enabled="false">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="RubRails" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Rub Rails" ConditionExpression="=RubRails=True" Enabled="true">
        <vars>
          <var name="BigMatrix">
            <value>=lookupmatrix("GroupIndex")</value>
          </var>
          <var name="RubRails_sel">
            <value>=BigMatrix["RR"]</value>
          </var>
          <var name="Adder">
            <value>=RubRail_Price</value>
          </var>
          <var name="Runtotal">
            <value>=Runtotal+Adder</value>
          </var>
          <var name="ConfiguredPrice">
            <value>=Runtotal</value>
          </var>
          <var name="RubRails_model">
            <value>RR</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
          <Details>
            <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Rub Rails" Visible="=True" Value="=RubRails_sel" />
            <Detail Category="PriceDetails" PrintCode="E" PrintSequence="10" Description="Rub Rails, Pair, Opt" Visible="=True" Value="=Adder" />
          </Details>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="No Rub Rails" ConditionExpression="=RubRails=False" Enabled="true">
        <vars>
          <var name="RubRails_model">
            <value>NN</value>
          </var>
        </vars>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Skirted" ConditionExpression="" Enabled="false">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="Skirted" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Skirted For Model Code" ConditionExpression="" Enabled="true">
      <Comment>Currently only used as place holder for model code</Comment>
      <vars>
        <var name="Skirted_model">
          <value>N</value>
        </var>
      </vars>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Arm Beds" ConditionExpression="=BedType=&quot;A&quot;" Enabled="true">
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Arm Length on tapered" ConditionExpression="=BedFeature=&quot;BT&quot; or BedFeature=&quot;TT&quot; or BedFeature=&quot;TB&quot; or BedFeature=&quot;SK&quot; or BedFeature=&quot;SF&quot; or BedFeature=&quot;SW&quot;" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
          <ScreenOption Sequence="1" Name="Arms" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Arm Length" ConditionExpression="" Enabled="true">
          <vars>
            <var name="BigMatrix">
              <value>=lookupmatrix("GroupIndex")</value>
            </var>
            <var name="Arms_sel">
              <value>=BigMatrix[Arms]</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
            <Details>
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Arm Length" Visible="=True" Value="=Arms_sel" />
            </Details>
          </Rule>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Straight Beds" ConditionExpression="=BedFeature=&quot;BS&quot; or BedFeature=&quot;TS&quot; or BedFeature=&quot;SB&quot;" Enabled="true">
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Arm Length on straight" ConditionExpression="=BedWidth &lt;= 84" Enabled="true">
          <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
            <ScreenOption Sequence="1" Name="Arms" SelectOptionsImageLinkFieldExpression="ImageLink" />
          </Screen>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Arm Length &lt;=84 wide bed" ConditionExpression="" Enabled="true">
            <vars>
              <var name="BigMatrix">
                <value>=lookupmatrix("GroupIndex")</value>
              </var>
              <var name="Arms_sel">
                <value>=BigMatrix[Arms]</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Arm Length" Visible="=True" Value="=Arms_sel" />
              </Details>
            </Rule>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Arm Length &gt;84 wide bed" ConditionExpression="=BedWidth &gt; 84" Enabled="true">
          <vars>
            <var name="BigMatrix">
              <value>=lookupmatrix("GroupIndex")</value>
            </var>
            <var name="Arms_sel">
              <value>=BigMatrix["AF"]</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
            <Details>
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Arm Length" Visible="=True" Value="=Arms_sel" />
            </Details>
          </Rule>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Arm Length 41&quot; Feeder" ConditionExpression="=BedFeature=&quot;FF&quot; or BedFeature=&quot;SF&quot;" Enabled="true">
        <vars>
          <var name="BigMatrix">
            <value>=lookupmatrix("GroupIndex")</value>
          </var>
          <var name="Arms_sel">
            <value>=BigMatrix["FL"]</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
          <Details>
            <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Arm Length" Visible="=True" Value="=Arms_sel" />
          </Details>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Spinners" ConditionExpression="" Enabled="false">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
          <ScreenOption Sequence="1" Name="SpinLength" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Spinner Length" ConditionExpression="" Enabled="true">
        <vars>
          <var name="BigMatrix">
            <value>=lookupmatrix("GroupIndex")</value>
          </var>
          <var name="SpinLength_sel">
            <value>=BigMatrix["SS"]</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
          <Details>
            <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Spinners" Visible="=True" Value="=SpinLength_sel" />
          </Details>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Spike holder in crosstube" ConditionExpression="" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
          <ScreenOption Sequence="1" Name="Arm_Spikes" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Crosstube W/O Spikes" ConditionExpression="=Arm_Spikes=false" Enabled="true">
          <vars>
            <var name="BigMatrix">
              <value>=lookupmatrix("GroupIndex")</value>
            </var>
            <var name="Crosstube_sel">
              <value>=BigMatrix["CT"]</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
            <Details>
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Crosstube" Visible="=True" Value="=Crosstube_sel" />
            </Details>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Crosstube W/ Spikes" ConditionExpression="=Arm_Spikes=True" Enabled="true">
          <vars>
            <var name="BigMatrix">
              <value>=lookupmatrix("GroupIndex")</value>
            </var>
            <var name="Crosstube_sel">
              <value>=BigMatrix["CS"]</value>
            </var>
            <var name="Arm_Spikes_sel">
              <value>205326</value>
            </var>
            <var name="LooseOptPrice">
              <value>=LooseOptPrice + ABSpike_Price</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
            <Details>
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Crosstube" Visible="=True" Value="=Crosstube_sel" />
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Spikes" Visible="=True" Value="=Arm_Spikes_sel" />
              <Detail Category="PriceDetails" PrintCode="" PrintSequence="11" Description="B) Arm Bed Spikes, Upgrade" Visible="=True" Value="=ABSpike_Price" />
            </Details>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask if brackets included with spikes" ConditionExpression="" Enabled="true">
            <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
              <ScreenOption Sequence="1" Name="Arm_Spikes_Bracket" SelectOptionsImageLinkFieldExpression="ImageLink" />
            </Screen>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Include brackets" ConditionExpression="=Arm_Spikes_Bracket=True" Enabled="true">
              <vars>
                <var name="BigMatrix">
                  <value>=lookupmatrix("GroupIndex")</value>
                </var>
                <var name="Arm_Spikes_Brackets_sel">
                  <value>205369</value>
                </var>
                <var name="LooseOptPrice">
                  <value>=LooseOptPrice+AB_Brackets_Price</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                <Details>
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Spike Holder Brackets" Visible="=True" Value="=Arm_Spikes_Brackets_sel" />
                  <Detail Category="PriceDetails" PrintCode="" PrintSequence="11" Description="C) Arm Bed Spike Holder, Option" Visible="=True" Value="=AB_Brackets_Price" />
                </Details>
              </Rule>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Include Ruleset" Caption="Outlets" ConditionExpression="" Enabled="true">
        <RuleSet namespace="SYS" type="SR" rulesetid="Outlets" />
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Ship completeing group (Power Unit, Control wired, bolt kit,mudflap bracketm power cables" ConditionExpression="" Enabled="true">
        <Comment>Power Unit, control handle, bolt kit mud flap plate, power cables</Comment>
        <vars>
          <var name="BigMatrix">
            <value>=lookupmatrix("GroupIndex")</value>
          </var>
          <var name="ShipCompleteingGroup">
            <value>=BigMatrix["HFG"]</value>
          </var>
          <var name="CompleteGroupType">
            <value>HFG</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
          <Details>
            <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Shipping Completeing Group" Visible="=True" Value="=ShipCompleteingGroup" />
          </Details>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Include Ruleset" Caption="Wired and Wireless Controll" ConditionExpression="=CustomerOrder=True" Enabled="true">
        <RuleSet namespace="SYS" type="SR" rulesetid="Controls" />
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Spike Beds" ConditionExpression="=BedType=&quot;S&quot;" Enabled="true">
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Spikes" ConditionExpression="" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
          <ScreenOption Sequence="1" Name="Spikes" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Spikes" ConditionExpression="" Enabled="true">
          <vars>
            <var name="BigMatrix">
              <value>=lookupmatrix("GroupIndex")</value>
            </var>
            <var name="Spikes_sel">
              <value>=BigMatrix[Spikes]</value>
            </var>
            <var name="Adder">
              <value>=Spikes.Value.Price</value>
            </var>
            <var name="Runtotal">
              <value>=Runtotal+Adder</value>
            </var>
            <var name="ConfiguredPrice">
              <value>=Runtotal</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="=Spikes=&quot;BR&quot;" Enabled="true">
            <Details>
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Spikes" Visible="=True" Value="=Spikes_sel" />
            </Details>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="=Spikes=&quot;RF&quot;" Enabled="true">
            <Details>
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Spikes" Visible="=True" Value="=Spikes_sel" />
              <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="B) Red Spikes, Upgrade" Visible="=True" Value="=Adder" />
            </Details>
          </Rule>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Drop Forks" ConditionExpression="=CustomerOrder=True" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
          <ScreenOption Sequence="1" Name="DropForks" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Drop Forks" ConditionExpression="=DropForks=True" Enabled="true">
          <vars>
            <var name="BigMatrix">
              <value>=lookupmatrix("GroupIndex")</value>
            </var>
            <var name="DropForks_sel">
              <value>=BigMatrix["DF"]</value>
            </var>
            <var name="LooseOptPrice">
              <value>=LooseOptPrice + DropForks_Price</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
            <Details>
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Drop Forks" Visible="=True" Value="=DropForks_sel" />
              <Detail Category="PriceDetails" PrintCode="" PrintSequence="11" Description="C) Drop Forks, Option" Visible="=True" Value="=DropForks_Price" />
            </Details>
          </Rule>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Include Ruleset" Caption="Outlets" ConditionExpression="=BedFeature!=&quot;TT&quot; &amp; BedFeature!=&quot;TS&quot; &amp; BedFeature!=&quot;US&quot;" Enabled="true">
        <RuleSet namespace="SYS" type="SR" rulesetid="Outlets" />
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Power Unit" ConditionExpression="" Enabled="false">
        <Comment>making this be a upgrad only if you get outlets</Comment>
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
          <ScreenOption Sequence="1" Name="HydPwrUnit" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Power Unit" ConditionExpression="" Enabled="false">
          <vars>
            <var name="BigMatrix">
              <value>=lookupmatrix("GroupIndex")</value>
            </var>
            <var name="HydPwrUnit_sel">
              <value>=BigMatrix[HydPwrUnit]</value>
            </var>
            <var name="Adder">
              <value>=if(HydPwrUnit="HP",649.98,0)</value>
            </var>
            <var name="Runtotal">
              <value>=Runtotal+Adder</value>
            </var>
            <var name="ConfiguredPrice">
              <value>=Runtotal</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
            <Details>
              <Detail Category="OrderDetails" PrintCode="E" PrintSequence="10" Description="Hydraulic Power Unit" Visible="=True" Value="=HydPwrUnit_sel" />
              <Detail Category="PriceDetails" PrintCode="E" PrintSequence="10" Description="=if(HydPwrUnit=&quot;HP&quot;,&quot;Pump Price&quot;,null)" Visible="=True" Value="=if(HydPwrUnit=&quot;HP&quot;,649.98,null)" />
            </Details>
          </Rule>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Ship completeing group (Power Unit, Control wired, bolt kit,mudflap bracketm power cables" ConditionExpression="=Outlets=True" Enabled="true">
        <Comment>Power Unit, control handle, bolt kit mud flap plate, power cables
need below if allowing to select power unit
=HydPwrUnit="HP"</Comment>
        <vars>
          <var name="BigMatrix">
            <value>=lookupmatrix("GroupIndex")</value>
          </var>
          <var name="ShipCompleteingGroup">
            <value>=BigMatrix["HFG"]</value>
          </var>
          <var name="Adder">
            <value>=SpikePwrUnitUpgrade_Price</value>
          </var>
          <var name="Runtotal">
            <value>=Runtotal+Adder</value>
          </var>
          <var name="ConfiguredPrice">
            <value>=Runtotal</value>
          </var>
          <var name="CompleteGroupType">
            <value>HFG</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
          <Details>
            <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Shipping Completeing Group" Visible="=True" Value="=ShipCompleteingGroup" />
            <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="C) Spike w/ Arm pwr unit, Option" Visible="=True" Value="=Adder" />
          </Details>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Ship completeing group (Power Unit, Control wired, bolt kit,mudflap bracketm power cables" ConditionExpression="=Outlets=False" Enabled="true">
        <Comment>Power Unit, control handle, bolt kit mud flap plate, power cables
need below if allowing to select power unit
=HydPwrUnit="SP"</Comment>
        <vars>
          <var name="BigMatrix">
            <value>=lookupmatrix("GroupIndex")</value>
          </var>
          <var name="ShipCompleteingGroup">
            <value>=BigMatrix["SFG"]</value>
          </var>
          <var name="CompleteGroupType">
            <value>SFG</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
          <Details>
            <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Shipping Completeing Group" Visible="=True" Value="=ShipCompleteingGroup" />
          </Details>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Trough Spike Bed Hydraulics" ConditionExpression="=BedFeature=&quot;TT&quot; or BedFeature=&quot;TS&quot; or BedFeature=&quot;US&quot;" Enabled="true">
        <vars>
          <var name="BigMatrix">
            <value>=lookupmatrix("GroupIndex")</value>
          </var>
          <var name="Outlets_sel">
            <value>=BigMatrix["SH"]</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="&lt; New Detail Rule &gt;" ConditionExpression="" Enabled="true">
          <Details>
            <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Hydraulic Plumbing" Visible="=True" Value="=Outlets_sel" />
          </Details>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Include Ruleset" Caption="Wired and Wireless Controll" ConditionExpression="=CustomerOrder=True" Enabled="true">
        <RuleSet namespace="SYS" type="SR" rulesetid="Controls" />
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Side Rails" ConditionExpression="" Enabled="true">
        <vars>
          <var name="BigMatrix">
            <value>=lookupmatrix("GroupIndex")</value>
          </var>
          <var name="SideRails_sel">
            <value>=BigMatrix["AL"]</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
          <Details>
            <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Side Rails" Visible="=True" Value="=SideRails_sel" />
          </Details>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Regular Bed" ConditionExpression="=BedType=&quot;R&quot;" Enabled="true">
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Side Rails" ConditionExpression="" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
          <ScreenOption Sequence="1" Name="SideRails" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Side rails true" ConditionExpression="=SideRails=true" Enabled="true">
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Side Rails" ConditionExpression="" Enabled="true">
            <vars>
              <var name="BigMatrix">
                <value>=lookupmatrix("GroupIndex")</value>
              </var>
              <var name="SideRails_sel">
                <value>=BigMatrix["AL"]</value>
              </var>
              <var name="Adder">
                <value>=SideRails_Price</value>
              </var>
              <var name="Runtotal">
                <value>=Runtotal+Adder</value>
              </var>
              <var name="ConfiguredPrice">
                <value>=Runtotal</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Side Rails" Visible="=True" Value="=SideRails_sel" />
                <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="B) Side Rails, Upgrade" Visible="=True" Value="=Adder" />
              </Details>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Power Unit Yes or No" ConditionExpression="" Enabled="false">
        <Comment>Decided to discontinue offering power units on regular beds as option.</Comment>
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
          <ScreenOption Sequence="1" Name="HydPowerFlat" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Power Unit" ConditionExpression="=HydPowerFlat=True" Enabled="true">
          <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
            <ScreenOption Sequence="1" Name="HydPwrUnit" SelectOptionsImageLinkFieldExpression="ImageLink" />
          </Screen>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Power Unit" ConditionExpression="" Enabled="false">
            <vars>
              <var name="BigMatrix">
                <value>=lookupmatrix("GroupIndex")</value>
              </var>
              <var name="HydPwrUnit_sel">
                <value>=BigMatrix[HydPwrUnit]</value>
              </var>
              <var name="Adder">
                <value>=if(HydPwrUnit="SP",632.10,if(HydPwrUnit="HP",1332.45,0))</value>
              </var>
              <var name="Runtotal">
                <value>=Runtotal+Adder</value>
              </var>
              <var name="ConfiguredPrice">
                <value>=Runtotal</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Hydraulic Power Unit" Visible="=True" Value="=HydPwrUnit_sel" />
                <Detail Category="PriceDetails" PrintCode="" PrintSequence="10" Description="=if(HydPwrUnit=&quot;SP&quot;,&quot;Pump Price&quot;,if(HydPwrUnit=&quot;HP&quot;,&quot;Pump Price&quot;,null))" Visible="=True" Value="=if(HydPwrUnit=&quot;SP&quot;,632.10,if(HydPwrUnit=&quot;HP&quot;,1332.45,null))" />
              </Details>
            </Rule>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Ship completeing group (Power Unit, Control wired, bolt kit,mudflap bracketm power cables" ConditionExpression="=HydPwrUnit=&quot;SP&quot; or HydPwrUnit=&quot;HP&quot;" Enabled="true">
            <Comment>Power Unit, control handle, bolt kit mud flap plate, power cables</Comment>
            <vars>
              <var name="BigMatrix">
                <value>=lookupmatrix("GroupIndex")</value>
              </var>
              <var name="ShipCompleteingGroup">
                <value>=if(HydPwrUnit="SP",BigMatrix["SFG"],if(HydPwrUnit="HP",BigMatrix["HFG"],0))</value>
              </var>
              <var name="Adder">
                <value>=if(HydPwrUnit="SP",RegularPwrUnitStd_Price,if(HydPwrUnit="HP",RegularPwrUnitHigh_Price,0))</value>
              </var>
              <var name="Runtotal">
                <value>=Runtotal+Adder</value>
              </var>
              <var name="ConfiguredPrice">
                <value>=Runtotal</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Shipping Completeing Group" Visible="=True" Value="=ShipCompleteingGroup" />
                <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="=if(HydPwrUnit=&quot;SP&quot;,&quot;Flat Bed, Spike PWR Unit Option&quot;,if(HydPwrUnit=&quot;HP&quot;,&quot;Flat Bed, Arm PWR Unit Option&quot;,null))" Visible="=True" Value="=Adder" />
              </Details>
            </Rule>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Include Ruleset" Caption="Outlets" ConditionExpression="" Enabled="true">
            <RuleSet namespace="SYS" type="SR" rulesetid="Outlets" />
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Include Ruleset" Caption="Wired and Wireless Control" ConditionExpression="=CustomerOrder=True" Enabled="true">
            <RuleSet namespace="SYS" type="SR" rulesetid="Controls" />
          </Rule>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Ship completeing group" ConditionExpression="" Enabled="true">
        <Comment>need below if we alow power units on flat
=HydPowerFlat=False</Comment>
        <vars>
          <var name="BigMatrix">
            <value>=lookupmatrix("GroupIndex")</value>
          </var>
          <var name="ShipCompleteingGroup">
            <value>=BigMatrix["NFG"]</value>
          </var>
          <var name="CompleteGroupType">
            <value>NFG</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
          <Details>
            <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Shipping Completeing Group" Visible="=True" Value="=ShipCompleteingGroup" />
          </Details>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Front Toolbox Bed Size &quot;Non Skirted&quot;" ConditionExpression="=Skirted=False" Enabled="true">
      <vars>
        <var name="BigMatrix">
          <value>=lookupmatrix("GroupIndex")</value>
        </var>
        <var name="BedLength">
          <value>=BigMatrix["BL"]</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Toolbox for Arm and Spike" ConditionExpression="=BedType=&quot;A&quot; or BedType=&quot;S&quot;" Enabled="true">
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Front Toolbox (short beds) Arm &amp; Spike" ConditionExpression="=BedLength &lt;= 86" Enabled="true">
          <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
            <ScreenOption Sequence="1" Name="FrontToolbox" OptionListGroup="FrontShort" SelectOptionsImageLinkFieldExpression="ImageLink" />
          </Screen>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Front Toolbox" ConditionExpression="" Enabled="true">
            <vars>
              <var name="BigMatrix">
                <value>=lookupmatrix("GroupIndex")</value>
              </var>
              <var name="FrontLHToolbox">
                <value>=if(FrontToolbox="FB18","LFB18","LFB20")</value>
              </var>
              <var name="FrontLHToolbox_sel">
                <value>=BigMatrix[FrontLHToolbox]</value>
              </var>
              <var name="FrontRHToolbox">
                <value>=if(FrontToolbox="FB18","RFB18","RFB20")</value>
              </var>
              <var name="FrontRHToolbox_sel">
                <value>=BigMatrix[FrontRHToolbox]</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="LH Front Toolbox" Visible="=True" Value="=FrontLHToolbox_sel" />
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="RH Front Toolbox" Visible="=True" Value="=FrontRHToolbox_sel" />
              </Details>
            </Rule>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Front Toolbox (long beds) Arm&amp;Spike" ConditionExpression="=BedLength &gt; 86" Enabled="true">
          <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
            <ScreenOption Sequence="1" Name="FrontToolbox" OptionListGroup="FrontLong" SelectOptionsImageLinkFieldExpression="ImageLink" />
          </Screen>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Front Toolbox" ConditionExpression="=BedType!=&quot;R&quot;" Enabled="true">
            <vars>
              <var name="BigMatrix">
                <value>=lookupmatrix("GroupIndex")</value>
              </var>
              <var name="FrontLHToolbox">
                <value>=if(FrontToolbox="FB30","LFB30","LFB36")</value>
              </var>
              <var name="FrontLHToolbox_sel">
                <value>=BigMatrix[FrontLHToolbox]</value>
              </var>
              <var name="FrontRHToolbox">
                <value>=if(FrontToolbox="FB30","RFB30","RFB36")</value>
              </var>
              <var name="FrontRHToolbox_sel">
                <value>=BigMatrix[FrontRHToolbox]</value>
              </var>
              <var name="Adder">
                <value>=if(FrontToolbox="FB36",FB36_Pricing,0)</value>
              </var>
              <var name="Runtotal">
                <value>=Runtotal+Adder</value>
              </var>
              <var name="ConfiguredPrice">
                <value>=Runtotal</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="LH Front Toolbox" Visible="=True" Value="=FrontLHToolbox_sel" />
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="RH Front Toolbox" Visible="=True" Value="=FrontRHToolbox_sel" />
              </Details>
            </Rule>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="=FrontToolbox=&quot;FB36&quot;" Enabled="true">
              <Details>
                <Detail Category="PriceDetails" PrintCode="E " PrintSequence="11" Description="C) Upgrade 36in Toolbox, Option" Visible="=True" Value="=FB36_Pricing" />
              </Details>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Toolbox for Regular" ConditionExpression="=BedType=&quot;R&quot;" Enabled="true">
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Front Toolbox (short beds) Regular" ConditionExpression="=BedLength &lt;= 86" Enabled="true">
          <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
            <ScreenOption Sequence="1" Name="FrontToolbox" OptionListGroup="FrontShortReg" SelectOptionsImageLinkFieldExpression="ImageLink" />
          </Screen>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Front Toolbox" ConditionExpression="=FrontToolbox != &quot;NNNN&quot;" Enabled="true">
            <vars>
              <var name="BigMatrix">
                <value>=lookupmatrix("GroupIndex")</value>
              </var>
              <var name="FrontLHToolbox">
                <value>=if(FrontToolbox="FB18","LFB18","LFB20")</value>
              </var>
              <var name="FrontLHToolbox_sel">
                <value>=BigMatrix[FrontLHToolbox]</value>
              </var>
              <var name="FrontRHToolbox">
                <value>=if(FrontToolbox="FB18","RFB18","RFB20")</value>
              </var>
              <var name="FrontRHToolbox_sel">
                <value>=BigMatrix[FrontRHToolbox]</value>
              </var>
              <var name="Adder">
                <value>=FrontToolbox.Value.PriceFlatBed</value>
              </var>
              <var name="LooseOptPrice">
                <value>=LooseOptPrice+adder</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="LH Front Toolbox" Visible="=True" Value="=FrontLHToolbox_sel" />
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="RH Front Toolbox" Visible="=True" Value="=FrontRHToolbox_sel" />
                <Detail Category="PriceDetails" PrintCode="" PrintSequence="11" Description="C) Flat Bed, 18-24in Toolbox, Option" Visible="=True" Value="=Adder" />
              </Details>
            </Rule>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Front Toolbox (long beds) Regular" ConditionExpression="=BedLength &gt; 86" Enabled="true">
          <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
            <ScreenOption Sequence="1" Name="FrontToolbox" OptionListGroup="FrontLongReg" SelectOptionsImageLinkFieldExpression="ImageLink" />
          </Screen>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Front Toolbox" ConditionExpression="=FrontToolbox != &quot;NNNN&quot;" Enabled="true">
            <vars>
              <var name="BigMatrix">
                <value>=lookupmatrix("GroupIndex")</value>
              </var>
              <var name="FrontLHToolbox">
                <value>=if(FrontToolbox="FB30","LFB30","LFB36")</value>
              </var>
              <var name="FrontLHToolbox_sel">
                <value>=BigMatrix[FrontLHToolbox]</value>
              </var>
              <var name="FrontRHToolbox">
                <value>=if(FrontToolbox="FB30","RFB30","RFB36")</value>
              </var>
              <var name="FrontRHToolbox_sel">
                <value>=BigMatrix[FrontRHToolbox]</value>
              </var>
              <var name="Adder">
                <value>=FrontToolbox.Value.PriceFlatBed</value>
              </var>
              <var name="LooseOptPrice">
                <value>=LooseOptPrice+adder</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="LH Front Toolbox" Visible="=True" Value="=FrontLHToolbox_sel" />
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="RH Front Toolbox" Visible="=True" Value="=FrontRHToolbox_sel" />
                <Detail Category="PriceDetails" PrintCode="" PrintSequence="11" Description="=If(FrontToolbox=&quot;FB36&quot;,&quot;C) BBR, 36in Toolbox, Opt&quot;,If(FrontToolbox=&quot;FB30&quot;,&quot;C) BBR, 30in Toolbox, Opt&quot;,null))" Visible="=True" Value="=Adder" />
              </Details>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Front Toolbox Skirted" ConditionExpression="=Skirted=True" Enabled="true">
      <vars>
        <var name="BigMatrix">
          <value>=lookupmatrix("GroupIndex")</value>
        </var>
        <var name="BedLength">
          <value>=BigMatrix["BL"]</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Front Toolbox (short beds)" ConditionExpression="=BedLength &lt;= 86" Enabled="true">
        <vars>
          <var name="BigMatrix">
            <value>=lookupmatrix("GroupIndex")</value>
          </var>
          <var name="FrontToolbox_sel">
            <value>=BigMatrix["FB18"]</value>
          </var>
          <var name="FrontToolbox">
            <value>="FB18"</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="=BedType!=&quot;R&quot;" Enabled="true">
          <Details>
            <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Front Toolbox" Visible="=True" Value="=FrontToolbox_sel" />
          </Details>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Regular (Flat) Bed Toolbox Pricing" ConditionExpression="=BedType=&quot;R&quot;" Enabled="true">
          <vars>
            <var name="Adder">
              <value>=FrontToolbox.Value.PriceFlatBed</value>
            </var>
            <var name="Runtotal">
              <value>=Runtotal+Adder</value>
            </var>
            <var name="ConfiguredPrice">
              <value>=Runtotal</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
            <Details>
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Front Toolbox" Visible="=True" Value="=FrontToolbox_sel" />
              <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="B) Front Toolbox Skirt, Upgrade" Visible="=True" Value="=Adder" />
            </Details>
          </Rule>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Front Toolbox (long beds)" ConditionExpression="=BedLength &gt; 86" Enabled="true">
        <vars>
          <var name="BigMatrix">
            <value>=lookupmatrix("GroupIndex")</value>
          </var>
          <var name="FrontToolbox_sel">
            <value>=BigMatrix["FB30"]</value>
          </var>
          <var name="FrontToolbox">
            <value>="FB30"</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="=BedType!=&quot;R&quot;" Enabled="true">
          <Details>
            <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Front Toolbox" Visible="=True" Value="=FrontToolbox_sel" />
          </Details>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Regular (Flat) Bed Toolbox Pricing" ConditionExpression="=BedType=&quot;R&quot;" Enabled="true">
          <vars>
            <var name="Adder">
              <value>=FrontToolbox.Value.PriceFlatBed</value>
            </var>
            <var name="Runtotal">
              <value>=Runtotal+Adder</value>
            </var>
            <var name="ConfiguredPrice">
              <value>=Runtotal</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
            <Details>
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Front Toolbox" Visible="=True" Value="=FrontToolbox_sel" />
              <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="B) Front Toolbox Skirt, Upgrade" Visible="=True" Value="=Adder" />
            </Details>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="REAR TOOLBOX SKIRT" ConditionExpression="=Skirted=True" Enabled="true">
      <Comment>Get Front Toolboxes because hardware is different</Comment>
      <vars>
        <var name="BigMatrix">
          <value>=lookupmatrix("GroupIndex")</value>
        </var>
        <var name="RearToolbox_sel">
          <value>=BigMatrix["RT"]</value>
        </var>
        <var name="MudflapMount_RT">
          <value>=BigMatrix["MM"]</value>
        </var>
        <var name="Adder">
          <value>=RearTB_Price</value>
        </var>
        <var name="Runtotal">
          <value>=Runtotal+Adder</value>
        </var>
        <var name="ConfiguredPrice">
          <value>=Runtotal</value>
        </var>
        <var name="LooseOptPrice">
          <value>=LooseOptPrice + MudflapMountRT_Price</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
        <Details>
          <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Rear Toolbox" Visible="=True" Value="=RearToolbox_sel" />
          <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="B) Rear Toolbox Skirt, Upgrade" Visible="=True" Value="=Adder" />
          <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Mudflap Mount" Visible="=True" Value="=MudflapMount_RT" />
          <Detail Category="PriceDetails" PrintCode="" PrintSequence="11" Description="C) Mudflap Mount, Option" Visible="=True" Value="=MudflapMountRT_Price" />
        </Details>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Arm Rear Toolboxes" ConditionExpression="=BedType=&quot;A&quot;" Enabled="true">
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Rear Toolbox" ConditionExpression="=Skirted=False" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
          <ScreenOption Sequence="1" Name="RearToolbox" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="REAR TOOLBOX" ConditionExpression="=RearToolbox=True" Enabled="true">
          <vars>
            <var name="BigMatrix">
              <value>=lookupmatrix("GroupIndex")</value>
            </var>
            <var name="RearLHToolbox_sel">
              <value>=BigMatrix["LRT"]</value>
            </var>
            <var name="RearRHToolbox_sel">
              <value>=BigMatrix["RRT"]</value>
            </var>
            <var name="RearToolboxHardware">
              <value>=BigMatrix["RTM"]</value>
            </var>
            <var name="LooseOptPrice">
              <value>=LooseOptPrice +RearTB_Price</value>
            </var>
            <var name="RearTBMount">
              <value>280615</value>
            </var>
            <var name="MudflapMount_RT">
              <value>=BigMatrix["MM"]</value>
            </var>
            <var name="LooseOptPrice">
              <value>=LooseOptPrice + MudflapMountRT_Price</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
            <Details>
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Rear LH Toolbox" Visible="=True" Value="=RearLHToolbox_sel" />
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Rear RH Toolbox" Visible="=True" Value="=RearRHToolbox_sel" />
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Rear Toolbox Hardware" Visible="=True" Value="=RearToolboxHardware" />
              <Detail Category="PriceDetails" PrintCode="" PrintSequence="11" Description="C) Rear Toolbox, Option" Visible="=True" Value="=RearTB_Price" />
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Rear Toolbox Mounts" Visible="=True" Value="=RearTBMount" />
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Mudflap Mount" Visible="=True" Value="=MudflapMount_RT" />
              <Detail Category="PriceDetails" PrintCode="" PrintSequence="11" Description="C) Mudflap Mount, Option" Visible="=True" Value="=MudflapMountRT_Price" />
            </Details>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Tailboard No Rear Toolboxes" ConditionExpression="=RearToolbox=False" Enabled="true">
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Receiver" ConditionExpression="" Enabled="true">
            <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
              <ScreenOption Sequence="1" Name="Tailboard" SelectOptionsImageLinkFieldExpression="ImageLink" />
            </Screen>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Receiver Hitch" ConditionExpression="=Tailboard=True" Enabled="true">
              <vars>
                <var name="BigMatrix">
                  <value>=lookupmatrix("GroupIndex")</value>
                </var>
                <var name="Tailboard_sel">
                  <value>=BigMatrix["TR"]</value>
                </var>
                <var name="Tailboard_model">
                  <value>TR</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                <Details>
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Tailboard" Visible="=True" Value="=Tailboard_sel" />
                </Details>
              </Rule>
            </Rule>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Plate" ConditionExpression="=Tailboard=False" Enabled="true">
              <vars>
                <var name="BigMatrix">
                  <value>=lookupmatrix("GroupIndex")</value>
                </var>
                <var name="Tailboard_sel">
                  <value>=BigMatrix["TP"]</value>
                </var>
                <var name="Tailboard_model">
                  <value>TP</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                <Details>
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Tailboard" Visible="=True" Value="=Tailboard_sel" />
                </Details>
              </Rule>
            </Rule>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Tailboard  Rear Toolboxes" ConditionExpression="=RearToolbox=True" Enabled="true">
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Receiver" ConditionExpression="" Enabled="true">
            <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
              <ScreenOption Sequence="1" Name="Tailboard" SelectOptionsImageLinkFieldExpression="ImageLink" />
            </Screen>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Receiver Hitch" ConditionExpression="=Tailboard=True" Enabled="true">
              <vars>
                <var name="BigMatrix">
                  <value>=lookupmatrix("GroupIndex")</value>
                </var>
                <var name="Tailboard_sel">
                  <value>=BigMatrix["TX"]</value>
                </var>
                <var name="Tailboard_model">
                  <value>TX</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                <Details>
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Tailboard" Visible="=True" Value="=Tailboard_sel" />
                </Details>
              </Rule>
            </Rule>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Plate" ConditionExpression="=Tailboard=False" Enabled="true">
              <vars>
                <var name="BigMatrix">
                  <value>=lookupmatrix("GroupIndex")</value>
                </var>
                <var name="Tailboard_sel">
                  <value>=BigMatrix["TY"]</value>
                </var>
                <var name="Tailboard_model">
                  <value>TY</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                <Details>
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Tailboard" Visible="=True" Value="=Tailboard_sel" />
                </Details>
              </Rule>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Spike and Regular Rear Toolboxes" ConditionExpression="=BedType=&quot;S&quot; or BedType=&quot;R&quot;" Enabled="true">
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Not Skirted" ConditionExpression="=Skirted=False" Enabled="true">
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Rear Toolbox Brackets" ConditionExpression="" Enabled="true">
          <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
            <ScreenOption Sequence="1" Name="RTBrackets" SelectOptionsImageLinkFieldExpression="ImageLink" />
          </Screen>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="RT_Mounts" ConditionExpression="=RTBrackets=True" Enabled="true">
            <Comment>Mounts</Comment>
            <vars>
              <var name="RearTBMount">
                <value>280407</value>
              </var>
            </vars>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Rear Toolbox" ConditionExpression="=RTBrackets=True" Enabled="true">
            <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
              <ScreenOption Sequence="1" Name="RearToolbox" SelectOptionsImageLinkFieldExpression="ImageLink" />
            </Screen>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="REAR TOOLBOX" ConditionExpression="=RearToolbox=True" Enabled="true">
              <vars>
                <var name="BigMatrix">
                  <value>=lookupmatrix("GroupIndex")</value>
                </var>
                <var name="RearLHToolbox_sel">
                  <value>=BigMatrix["LRT"]</value>
                </var>
                <var name="RearRHToolbox_sel">
                  <value>=BigMatrix["RRT"]</value>
                </var>
                <var name="RearToolboxHardware">
                  <value>=BigMatrix["RTM"]</value>
                </var>
                <var name="LooseOptPrice">
                  <value>=LooseOptPrice +RearTB_Price</value>
                </var>
                <var name="MudflapMount_RT">
                  <value>=BigMatrix["MM"]</value>
                </var>
                <var name="LooseOptPrice">
                  <value>=LooseOptPrice + MudflapMountRT_Price</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                <Details>
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Rear LH Toolbox" Visible="=True" Value="=RearLHToolbox_sel" />
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Rear RH Toolbox" Visible="=True" Value="=RearRHToolbox_sel" />
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Rear Toolbox Hardware" Visible="=True" Value="=RearToolboxHardware" />
                  <Detail Category="PriceDetails" PrintCode="" PrintSequence="11" Description="C) Rear Toolbox, Option" Visible="=True" Value="=RearTB_Price" />
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Rear Toolbox Mounts" Visible="=True" Value="=RearTBMount" />
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Mudflap Mount" Visible="=True" Value="=MudflapMount_RT" />
                  <Detail Category="PriceDetails" PrintCode="" PrintSequence="11" Description="C) Mudflap Mount, Option" Visible="=True" Value="=MudflapMountRT_Price" />
                </Details>
              </Rule>
            </Rule>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="REAR TOOLBOX" ConditionExpression="=RearToolbox=False" Enabled="true">
              <vars>
                <var name="Adder">
                  <value>=RearTBMNT_Price</value>
                </var>
                <var name="Runtotal">
                  <value>=Runtotal+Adder</value>
                </var>
                <var name="ConfiguredPrice">
                  <value>=Runtotal</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                <Details>
                  <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="B) Rear Toolbox Mount, Upgrade" Visible="=True" Value="=Adder" />
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Rear Toolbox Mounts" Visible="=True" Value="=RearTBMount" />
                </Details>
              </Rule>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Rub rail for Spike or Regular" ConditionExpression="=BedType=&quot;S&quot; or BedType=&quot;R&quot;" Enabled="true">
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="BTO" ConditionExpression="=BedLength&lt;103" Enabled="true">
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Rub Rails" ConditionExpression="" Enabled="true">
          <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
            <ScreenOption Sequence="1" Name="RubRails" SelectOptionsImageLinkFieldExpression="ImageLink" />
          </Screen>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Rub Rails" ConditionExpression="=RubRails=True" Enabled="true">
            <vars>
              <var name="BigMatrix">
                <value>=lookupmatrix("GroupIndex")</value>
              </var>
              <var name="RubRails_sel">
                <value>=BigMatrix["RR"]</value>
              </var>
              <var name="Adder">
                <value>=RubRail_Price</value>
              </var>
              <var name="Runtotal">
                <value>=Runtotal+Adder</value>
              </var>
              <var name="ConfiguredPrice">
                <value>=Runtotal</value>
              </var>
              <var name="RubRails_model">
                <value>RR</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Rub Rails" Visible="=True" Value="=RubRails_sel" />
                <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="B) Rub Rails, Upgrade" Visible="=True" Value="=Adder" />
              </Details>
            </Rule>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="No Rub Rails" ConditionExpression="=RubRails=False" Enabled="true">
            <vars>
              <var name="RubRails_model">
                <value>NN</value>
              </var>
            </vars>
          </Rule>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Cab and Chassis" ConditionExpression="=BedLength&gt;103" Enabled="true">
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Without skirt or rear toolbox" ConditionExpression="=Skirted=False &amp; RearToolbox=False" Enabled="true">
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Rub Rails" ConditionExpression="" Enabled="true">
            <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
              <ScreenOption Sequence="1" Name="RubRails" SelectOptionsImageLinkFieldExpression="ImageLink" />
            </Screen>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Rub Rails" ConditionExpression="=RubRails=True" Enabled="true">
              <vars>
                <var name="BigMatrix">
                  <value>=lookupmatrix("GroupIndex")</value>
                </var>
                <var name="RubRails_sel">
                  <value>=BigMatrix["RR"]</value>
                </var>
                <var name="Adder">
                  <value>=RubRail_Price</value>
                </var>
                <var name="Runtotal">
                  <value>=Runtotal+Adder</value>
                </var>
                <var name="ConfiguredPrice">
                  <value>=Runtotal</value>
                </var>
                <var name="RubRails_model">
                  <value>RR</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                <Details>
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Rub Rails" Visible="=True" Value="=RubRails_sel" />
                  <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="B) Rub Rails, Upgrade" Visible="=True" Value="=Adder" />
                </Details>
              </Rule>
            </Rule>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="No Rub Rails" ConditionExpression="=RubRails=False" Enabled="true">
              <vars>
                <var name="RubRails_model">
                  <value>NN</value>
                </var>
              </vars>
            </Rule>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="With skirt or rear toolbox" ConditionExpression="=Skirted=True or RearToolbox=True" Enabled="true">
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Rub Rails" ConditionExpression="" Enabled="true">
            <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
              <ScreenOption Sequence="1" Name="RubRails" SelectOptionsImageLinkFieldExpression="ImageLink" />
            </Screen>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Rub Rails" ConditionExpression="=RubRails=True" Enabled="true">
              <vars>
                <var name="BigMatrix">
                  <value>=lookupmatrix("GroupIndex")</value>
                </var>
                <var name="RubRails_sel">
                  <value>=BigMatrix["RX"]</value>
                </var>
                <var name="Adder">
                  <value>=RubRail_Price</value>
                </var>
                <var name="Runtotal">
                  <value>=Runtotal+Adder</value>
                </var>
                <var name="ConfiguredPrice">
                  <value>=Runtotal</value>
                </var>
                <var name="RubRails_model">
                  <value>RR</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                <Details>
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Rub Rails" Visible="=True" Value="=RubRails_sel" />
                  <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="B) Rub Rails, Upgrade" Visible="=True" Value="=Adder" />
                </Details>
              </Rule>
            </Rule>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="No Rub Rails" ConditionExpression="=RubRails=False" Enabled="true">
              <vars>
                <var name="RubRails_model">
                  <value>NN</value>
                </var>
              </vars>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Rub rail for Arm" ConditionExpression="=BedType=&quot;A&quot;" Enabled="true">
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="BTO" ConditionExpression="=BedLength&lt;103" Enabled="true">
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Rub Rails Arm Beds" ConditionExpression="=BedType=&quot;A&quot;" Enabled="true">
          <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
            <ScreenOption Sequence="1" Name="RubRails" SelectOptionsImageLinkFieldExpression="ImageLink" />
          </Screen>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Rub Rails" ConditionExpression="=RubRails=True" Enabled="true">
            <vars>
              <var name="BigMatrix">
                <value>=lookupmatrix("GroupIndex")</value>
              </var>
              <var name="RubRails_sel">
                <value>=BigMatrix["RR"]</value>
              </var>
              <var name="Adder">
                <value>=RubRail_Price</value>
              </var>
              <var name="Runtotal">
                <value>=Runtotal+Adder</value>
              </var>
              <var name="ConfiguredPrice">
                <value>=Runtotal</value>
              </var>
              <var name="RubRails_model">
                <value>RR</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Rub Rails" Visible="=True" Value="=RubRails_sel" />
                <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="B) Rub Rails, Upgrade" Visible="=True" Value="=Adder" />
              </Details>
            </Rule>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="No Rub Rails" ConditionExpression="=RubRails=False" Enabled="true">
            <vars>
              <var name="RubRails_model">
                <value>NN</value>
              </var>
            </vars>
          </Rule>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Cab and Chassis" ConditionExpression="=BedLength&gt;103" Enabled="true">
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Without skirt or rear toolbox" ConditionExpression="=Skirted=False &amp; RearToolbox=False" Enabled="true">
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Rub Rails" ConditionExpression="" Enabled="true">
            <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
              <ScreenOption Sequence="1" Name="RubRails" SelectOptionsImageLinkFieldExpression="ImageLink" />
            </Screen>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Rub Rails" ConditionExpression="=RubRails=True" Enabled="true">
              <vars>
                <var name="BigMatrix">
                  <value>=lookupmatrix("GroupIndex")</value>
                </var>
                <var name="RubRails_sel">
                  <value>=BigMatrix["RR"]</value>
                </var>
                <var name="Adder">
                  <value>=RubRail_Price</value>
                </var>
                <var name="Runtotal">
                  <value>=Runtotal+Adder</value>
                </var>
                <var name="ConfiguredPrice">
                  <value>=Runtotal</value>
                </var>
                <var name="RubRails_model">
                  <value>RR</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                <Details>
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Rub Rails" Visible="=True" Value="=RubRails_sel" />
                  <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="B) Rub Rails, Upgrade" Visible="=True" Value="=Adder" />
                </Details>
              </Rule>
            </Rule>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="No Rub Rails" ConditionExpression="=RubRails=False" Enabled="true">
              <vars>
                <var name="RubRails_model">
                  <value>NN</value>
                </var>
              </vars>
            </Rule>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="With skirt or rear toolbox" ConditionExpression="=Skirted=True or RearToolbox=True" Enabled="true">
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Rub Rails" ConditionExpression="" Enabled="true">
            <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
              <ScreenOption Sequence="1" Name="RubRails" SelectOptionsImageLinkFieldExpression="ImageLink" />
            </Screen>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Rub Rails" ConditionExpression="=RubRails=True" Enabled="true">
              <vars>
                <var name="BigMatrix">
                  <value>=lookupmatrix("GroupIndex")</value>
                </var>
                <var name="RubRails_sel">
                  <value>=BigMatrix["RX"]</value>
                </var>
                <var name="Adder">
                  <value>=RubRail_Price</value>
                </var>
                <var name="Runtotal">
                  <value>=Runtotal+Adder</value>
                </var>
                <var name="ConfiguredPrice">
                  <value>=Runtotal</value>
                </var>
                <var name="RubRails_model">
                  <value>RR</value>
                </var>
              </vars>
              <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
                <Details>
                  <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Rub Rails" Visible="=True" Value="=RubRails_sel" />
                  <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="B) Rub Rails, Upgrade" Visible="=True" Value="=Adder" />
                </Details>
              </Rule>
            </Rule>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="No Rub Rails" ConditionExpression="=RubRails=False" Enabled="true">
              <vars>
                <var name="RubRails_model">
                  <value>NN</value>
                </var>
              </vars>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Arm" ConditionExpression="=BedType=&quot;A&quot;" Enabled="true">
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Receiver Skirted ARM" ConditionExpression="=Skirted=True" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
          <ScreenOption Sequence="1" Name="Tailboard" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Receiver Hitch" ConditionExpression="=Tailboard=True" Enabled="true">
          <vars>
            <var name="BigMatrix">
              <value>=lookupmatrix("GroupIndex")</value>
            </var>
            <var name="Tailboard_sel">
              <value>=BigMatrix["ST"]</value>
            </var>
            <var name="Tailboard_model">
              <value>ST</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
            <Details>
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Tailboard" Visible="=True" Value="=Tailboard_sel" />
            </Details>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Plate" ConditionExpression="=Tailboard=False" Enabled="true">
          <vars>
            <var name="BigMatrix">
              <value>=lookupmatrix("GroupIndex")</value>
            </var>
            <var name="Tailboard_sel">
              <value>=BigMatrix["SP"]</value>
            </var>
            <var name="Tailboard_model">
              <value>SP</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
            <Details>
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Tailboard" Visible="=True" Value="=Tailboard_sel" />
            </Details>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Work Lights" ConditionExpression="=CustomerOrder=True" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="WorkLights" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="With Work Lights" ConditionExpression="=WorkLights=True" Enabled="true">
        <vars>
          <var name="BigMatrix">
            <value>=lookupmatrix("GroupIndex")</value>
          </var>
          <var name="WorkLights_sel">
            <value>=BigMatrix["WL"]</value>
          </var>
          <var name="LooseOptPrice">
            <value>=LooseOptPrice+WorkLights_Price</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
          <Details>
            <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Work Lights" Visible="=True" Value="=WorkLights_sel" />
            <Detail Category="PriceDetails" PrintCode="" PrintSequence="11" Description="C) Work Light, Option" Visible="=True" Value="=WorkLights_Price" />
          </Details>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="W/O Work Lights" ConditionExpression="=WorkLights=False" Enabled="true">
        <vars>
          <var name="WorkLights_model">
            <value>NN</value>
          </var>
        </vars>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Completing Group Non Trough" ConditionExpression="=BedFeature!=&quot;TT&quot; &amp; BedFeature!=&quot;TS&quot; &amp; BedFeature!=&quot;US&quot;" Enabled="true">
      <vars>
        <var name="BigMatrix">
          <value>=lookupmatrix("GroupIndex")</value>
        </var>
        <var name="CompletingGroup">
          <value>=BigMatrix["FG"]</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
        <Details>
          <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Completing Group" Visible="=True" Value="=CompletingGroup" />
        </Details>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Completing Group Trough" ConditionExpression="=BedFeature=&quot;TT&quot; or BedFeature=&quot;TS&quot; or BedFeature=&quot;US&quot;" Enabled="true">
      <vars>
        <var name="BigMatrix">
          <value>=lookupmatrix("GroupIndex")</value>
        </var>
        <var name="CompletingGroup">
          <value>=BigMatrix["FT"]</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
        <Details>
          <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Completing Group" Visible="=True" Value="=CompletingGroup" />
        </Details>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask XBed" ConditionExpression="=CustomerOrder=True" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="XBed" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="AcrossToolBox" ConditionExpression="=XBed!=&quot;NN&quot;" Enabled="true">
        <vars>
          <var name="BigMatrix">
            <value>=lookupmatrix("GroupIndex")</value>
          </var>
          <var name="XBed_sel">
            <value>=BigMatrix[XBed]</value>
          </var>
          <var name="XBed_Price">
            <value>=XBed.Value.PriceFlatBed</value>
          </var>
          <var name="LooseOptPrice">
            <value>=LooseOptPrice + XBed_Price</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
          <Details>
            <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Across the Bed Toolbox" Visible="=True" Value="=XBed_sel" />
            <Detail Category="PriceDetails" PrintCode="" PrintSequence="11" Description="C) XTB, Toolbox" Visible="=True" Value="=XBed_Price" />
          </Details>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="No AcrossToolBox" ConditionExpression="=XBed2=False" Enabled="false">
        <Comment>Used for the model code</Comment>
        <vars>
          <var name="XBed2_model">
            <value>NN</value>
          </var>
        </vars>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Mudflaps" ConditionExpression="" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="Mudflaps" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Mudflaps" ConditionExpression="=Mudflaps=true" Enabled="true">
        <vars>
          <var name="BigMatrix">
            <value>=lookupmatrix("GroupIndex")</value>
          </var>
          <var name="Mudflaps_sel">
            <value>=BigMatrix["MF"]</value>
          </var>
          <var name="LooseOptPrice">
            <value>=LooseOptPrice+Mudflaps_Price</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
          <Details>
            <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Mudflaps" Visible="=True" Value="=Mudflaps_sel" />
            <Detail Category="PriceDetails" PrintCode="" PrintSequence="11" Description="C) Mudflaps, Option" Visible="=True" Value="=MudFlaps_Price" />
          </Details>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Wire Harness" ConditionExpression="" Enabled="false">
      <Comment>Moved out Front Harness Rear Harness and Headache Lights Below</Comment>
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="Wireharness" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Pigtails for tailboard and clearance" ConditionExpression="=Wireharness=False" Enabled="false">
        <vars>
          <var name="BigMatrix">
            <value>=lookupmatrix("GroupIndex")</value>
          </var>
          <var name="TC_pigtail_select">
            <value>=BigMatrix["HX"]</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
          <Details>
            <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Tailboard &amp; Clearance Pigtail" Visible="=True" Value="=TC_pigtail_select" />
          </Details>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="No Wire Harness Model Code" ConditionExpression="=Wireharness=false" Enabled="true">
        <vars>
          <var name="Wireharness_model">
            <value>N</value>
          </var>
        </vars>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Front Harness" ConditionExpression="" Enabled="true">
      <Comment>=Wireharness=True It is now standard</Comment>
      <vars>
        <var name="BigMatrix">
          <value>=lookupmatrix("GroupIndex")</value>
        </var>
        <var name="FrontHarness_sel">
          <value>=BigMatrix["HF"]</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
        <Details>
          <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Front Harness" Visible="=True" Value="=FrontHarness_sel" />
        </Details>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Rear Harness" ConditionExpression="" Enabled="true">
      <Comment>=Wireharness=True It is now standard.  Took out BedHarness_Price it is now standard</Comment>
      <vars>
        <var name="BigMatrix">
          <value>=lookupmatrix("GroupIndex")</value>
        </var>
        <var name="RearHarness_sel">
          <value>=BigMatrix["HR"]</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
        <Details>
          <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Rear Harness" Visible="=True" Value="=RearHarness_sel" />
        </Details>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Headache Lights" ConditionExpression="=Headache=&quot;SL&quot; or Headache=&quot;ML&quot; or Headache=&quot;TL&quot; or Headache=&quot;XL&quot;" Enabled="true">
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="STT Harness" ConditionExpression="" Enabled="true">
        <Comment>used to be =Wireharness=true.  Took out STTHarness_Price it is now standard</Comment>
        <vars>
          <var name="BigMatrix">
            <value>=lookupmatrix("GroupIndex")</value>
          </var>
          <var name="STT_sel">
            <value>=BigMatrix["HS"]</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
          <Details>
            <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="STT Harness" Visible="=True" Value="=STT_sel" />
          </Details>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="STT Pigtails" ConditionExpression="=Wireharness=false" Enabled="false">
        <vars>
          <var name="BigMatrix">
            <value>=lookupmatrix("GroupIndex")</value>
          </var>
          <var name="pigtail_sel">
            <value>=BigMatrix["XN"]</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
          <Details>
            <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="STT Pigtail" Visible="=True" Value="=pigtail_sel" />
          </Details>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Wire Harness Model Code" ConditionExpression="" Enabled="true">
      <Comment>used to be =Wireharness=true</Comment>
      <vars>
        <var name="Wireharness_model">
          <value>H</value>
        </var>
      </vars>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Adapter Harness On Order?" ConditionExpression="=CustomerOrder=True" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="AdapeterHarness" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Wire Harness True" ConditionExpression="=AdapeterHarness=true" Enabled="true">
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Include Ruleset" Caption="Truck Configuration" ConditionExpression="=TruckConfig=true" Enabled="true">
          <RuleSet namespace="SYS" type="SR" rulesetid="Truck" />
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Butler Adapter Option" ConditionExpression="=AdapeterHarness=True" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
          <ScreenOption Sequence="1" Name="ButlerAdapter" OptionListID="ButlerAdapter" OptionListGroup="=GroupUnion(Split(LookUpMatrix(&quot;ButlerAdapterHarns&quot;,Make, Years, Axle, CabAxle, BedType),&quot;-&quot;))" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Adapter Harness" ConditionExpression="" Enabled="true">
          <vars />
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Adapter Harness" ConditionExpression="" Enabled="true">
            <Details>
              <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="C) Adapter Harness, Option" Visible="=True" Value="=ButlerAdapter" />
            </Details>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Mount Kit" ConditionExpression="=CustomerOrder=True" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="MountKit" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="MountKit True" ConditionExpression="=MountKit=true" Enabled="true">
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Include Ruleset" Caption="Truck Configuration" ConditionExpression="=TruckConfig=true" Enabled="true">
          <RuleSet namespace="SYS" type="SR" rulesetid="Truck" />
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Butler Mount Kit Option" ConditionExpression="=MountKit=True" Enabled="true">
          <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
            <ScreenOption Sequence="1" Name="MountKit_sel" OptionListID="ButlerMountKits" OptionListGroup="=GroupUnion(Split(LookUpMatrix(&quot;ButlerMKOpt&quot;,Make, Years, Axle, CabAxle, BedType),&quot;-&quot;))" SelectOptionsImageLinkFieldExpression="ImageLink" />
          </Screen>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="MountKit" ConditionExpression="" Enabled="true">
            <vars>
              <var name="MountKit_model">
                <value>MK</value>
              </var>
            </vars>
            <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="MountKit" ConditionExpression="" Enabled="true">
              <Details>
                <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Mount Kit" Visible="=True" Value="=MountKit_sel" />
                <Detail Category="PriceDetails" PrintCode="" PrintSequence="11" Description="C) Mount Kit, Option" Visible="=True" Value="=MountKit_Price" />
              </Details>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Mount Kit False" ConditionExpression="" Enabled="true">
        <Comment>Used for the model code</Comment>
        <vars>
          <var name="MountKit_model">
            <value>NN</value>
          </var>
        </vars>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Ask Mounting Tabs" ConditionExpression="=TruckConfig=True or Make=&quot;FD&quot;" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="MntTabs" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Mounting Tabs" ConditionExpression="=MntTabs=True" Enabled="true">
        <vars>
          <var name="BigMatrix">
            <value>=lookupmatrix("GroupIndex")</value>
          </var>
          <var name="MountTab_sel">
            <value>=BigMatrix["MT"]</value>
          </var>
          <var name="LooseOptPrice">
            <value>=LooseOptPrice+MountTab_Price</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
          <Details>
            <Detail Category="OrderDetails" PrintCode="" PrintSequence="10" Description="Mounting Tabs" Visible="=True" Value="=MountTab_sel" />
            <Detail Category="PriceDetails" PrintCode="" PrintSequence="11" Description="C) Mount Tab, Option" Visible="=True" Value="=MountTab_Price" />
          </Details>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Welded Mount Tabs?" ConditionExpression="=MntTabs=True" Enabled="false">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
          <ScreenOption Sequence="1" Name="WeldTab" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Pump and Toolbox Install" ConditionExpression="" Enabled="false">
      <Comment>Not offering</Comment>
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="Assembly_Comp" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Finish Assembly Cost" ConditionExpression="=Assembly_Comp=True" Enabled="true">
        <vars>
          <var name="Adder">
            <value>=FinishAsm_Price</value>
          </var>
          <var name="Runtotal">
            <value>=Runtotal+Adder</value>
          </var>
          <var name="ConfiguredPrice">
            <value>=Runtotal</value>
          </var>
        </vars>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
          <Details>
            <Detail Category="PriceDetails" PrintCode="E" PrintSequence="10" Description="Toolbox &amp; E/H Kit, INSTL" Visible="=True" Value="=Adder" />
          </Details>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Custom Price Adder?" ConditionExpression="" Enabled="true">
      <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
        <ScreenOption Sequence="1" Name="CustomOrder" SelectOptionsImageLinkFieldExpression="ImageLink" />
      </Screen>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Screen" Caption="Custom Price Adder" ConditionExpression="=CustomOrder=True" Enabled="true">
        <Screen Title="" ShowTitle="=False" ShowLine="=False" ShowDescription="=False" ShowExpandCollapse="=False" IsVisible="=True" Description="" PageBreakAfter="=False" PageCaption="" AppLink="" InfoLink="" ImageLink="" Pause="=False" PauseMessage="" PauseMessageStyle="" PauseTitle="" ScreenStyle="">
          <ScreenOption Sequence="1" Name="CustomPrice" SelectOptionsImageLinkFieldExpression="ImageLink" />
        </Screen>
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Custom Price Adder" ConditionExpression="" Enabled="true">
          <vars>
            <var name="Adder">
              <value>=CustomPrice</value>
            </var>
            <var name="Runtotal">
              <value>=Runtotal+Adder</value>
            </var>
            <var name="ConfiguredPrice">
              <value>=Runtotal</value>
            </var>
          </vars>
          <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
            <Details>
              <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="B) Custom Price Addition" Visible="=True" Value="=Adder" />
            </Details>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Surcharge Price Adder" ConditionExpression="" Enabled="false">
      <vars>
        <var name="Adder">
          <value>=if(bedtype="A",485,385)</value>
        </var>
        <var name="Runtotal">
          <value>=Runtotal+Adder</value>
        </var>
        <var name="ConfiguredPrice">
          <value>=Runtotal</value>
        </var>
      </vars>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Detail Display" ConditionExpression="" Enabled="true">
        <Details>
          <Detail Category="PriceDetails" PrintCode="E" PrintSequence="10" Description="Surcharge" Visible="=True" Value="=Adder" />
        </Details>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Arm Bed Discounts" ConditionExpression="=BedType=&quot;A&quot;" Enabled="true">
      <Comment>if(Skirted=true,RearTB_Price,0) This likely is incorrecect and needs deleted
-if(FrontToolbox="FB36",FB36_Pricing,0)</Comment>
      <vars>
        <var name="Adjustment">
          <value>=-1*(Arm_Finish_Group_Disc+if(Skirted=False,FrontToolbox.Value.PriceFlatBed,0))</value>
        </var>
        <var name="Runtotal">
          <value>=Runtotal+Adjustment</value>
        </var>
        <var name="ConfiguredPrice">
          <value>=Runtotal</value>
        </var>
      </vars>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Spike Bed Discounts" ConditionExpression="=BedType=&quot;S&quot;" Enabled="true">
      <Comment>if(Skirted=True,RearTB_Price,0)  likely needs deleted
-if(FrontToolbox="FB36",FB36_Pricing,0)</Comment>
      <vars>
        <var name="Adjustment">
          <value>=-1*(Spike_Finish_Group_Disc+if(Skirted=False,FrontToolbox.Value.PriceFlatBed,0)+if(CompleteGroupType="HFG",SpikePwrUnitUpgrade_Price,0))</value>
        </var>
        <var name="Runtotal">
          <value>=Runtotal+Adjustment</value>
        </var>
        <var name="ConfiguredPrice">
          <value>=Runtotal</value>
        </var>
      </vars>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Flat Bed Discounts" ConditionExpression="=BedType=&quot;R&quot;" Enabled="true">
      <Comment>if(Skirted=True,FrontToolbox.Value.PriceFlatBed,0)+if(Skirted=True,RearTB_Price,0)  liley needs deleted</Comment>
      <vars>
        <var name="Adjustment">
          <value>=-1*(Reg_Finish_Group_Disc)</value>
        </var>
        <var name="Runtotal">
          <value>=Runtotal+Adjustment</value>
        </var>
        <var name="ConfiguredPrice">
          <value>=Runtotal</value>
        </var>
      </vars>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Configured Pice" ConditionExpression="" Enabled="true">
      <Comment>+if(Skirted=True,FrontToolbox.Value.PriceFlatBed,0)+if(Skirted=True,RearTB_Price,0)  think i can delete was added to bed as built price
removed from shipped parts -if(BedType!="R",if(FrontToolbox="FB36",FB36_Pricing,0),0)
-if(Skirted=True,FrontToolbox.Value.PriceFlatBed,0)-if(Skirted=True,RearTB_Price,0) think I can delete was in the shipped parts price</Comment>
      <Details>
        <Detail Category="OrderDetails" PrintCode="" PrintSequence="12" Description="---------------------------------------------------------------------------" Visible="=False" Value="-----------------------------------------------------------------------" />
        <Detail Category="PriceDetails" PrintCode="" PrintSequence="12" Description="Options" Visible="=False" Value="=LooseOptPrice" />
        <Detail Category="PriceDetails" PrintCode="" PrintSequence="12" Description="Bed as Built Price " Visible="=False" Value="=ConfiguredPrice" />
        <Detail Category="PriceDetails" PrintCode="" PrintSequence="12" Description="Shipped Parts Price " Visible="=False" Value="=LooseOptPrice-Adjustment" />
        <Detail Category="PriceDetails" PrintCode="" PrintSequence="11" Description="D) Total Package Price" Visible="=True" Value="=ConfiguredPrice-Adjustment+LooseOptPrice" />
        <Detail Category="PriceDetails" PrintCode="E" PrintSequence="12" Description="D) Base Price Adjustment" Visible="=False" Value="=Adjustment" />
      </Details>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Customer Order" ConditionExpression="=CustomerOrder=True" Enabled="true">
        <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Not Midland 3%" ConditionExpression="=CustCode!=&quot;B82&quot;" Enabled="true">
          <Details>
            <Detail Category="PriceDetails" PrintCode="E" PrintSequence="11" Description="E) 3% Discount If Paid in 10 Days" Visible="=True" Value="=Bed.Value.Price *.03" />
          </Details>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="3% Customer Order" ConditionExpression="=CustomerOrder=True" Enabled="false">
        <Details>
          <Detail Category="PriceDetails" PrintCode="=if(CustCode=&quot;B999999&quot;,&quot;&quot;,&quot;E&quot;)" PrintSequence="11" Description="E) 3% Discount If Paid in 10 Days" Visible="=True" Value="=Bed.Value.Price *.03" />
        </Details>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Include Ruleset" Caption="Butler Manufacturing" ConditionExpression="" Enabled="true">
      <RuleSet namespace="SYS" type="SR" rulesetid="Butler_Manufacturing" />
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Condition" Caption="Model Code" ConditionExpression="" Enabled="true">
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Arm Model Code Rule Skirts" ConditionExpression="=BedFeature!=&quot;SK&quot; &amp; BedFeature!=&quot;SW&quot; &amp; BedFeature!=&quot;SF&quot; &amp; BedFeature!=&quot;US&quot;" Enabled="true">
        <vars>
          <var name="SkirtCA">
            <value>="ZZ"</value>
          </var>
        </vars>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Rear Toolbox" ConditionExpression="" Enabled="true">
        <vars>
          <var name="RearToolbox_MC">
            <value>=if(RearToolbox=true,"Y","N")</value>
          </var>
        </vars>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Wire harness" ConditionExpression="" Enabled="true">
        <vars>
          <var name="Wireharness_MC">
            <value>=if(Wireharness=true,"Y","N")</value>
          </var>
        </vars>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Rub Rails" ConditionExpression="" Enabled="true">
        <vars>
          <var name="RubRails_MC">
            <value>=if(RubRails=true,"Y","N")</value>
          </var>
        </vars>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Outlets" ConditionExpression="" Enabled="true">
        <vars>
          <var name="Outlets_MC">
            <value>=if(Outlets=true,"Y","N")</value>
          </var>
        </vars>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Adapter Harness" ConditionExpression="" Enabled="true">
        <vars>
          <var name="AdapterHarness_MC">
            <value>=if(AdapeterHarness=true,"Y","N")</value>
          </var>
        </vars>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Mount Kits" ConditionExpression="" Enabled="true">
        <vars>
          <var name="MountKit_MC">
            <value>=if(MountKit=true,"Y","N")</value>
          </var>
        </vars>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Work Lights" ConditionExpression="" Enabled="true">
        <vars>
          <var name="WorkLights_MC">
            <value>=if(WorkLights=true,"Y","N")</value>
          </var>
        </vars>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Mudflaps" ConditionExpression="" Enabled="true">
        <vars>
          <var name="Mudflaps_MC">
            <value>=if(Mudflaps=true,"Y","N")</value>
          </var>
        </vars>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Arm Spikes&amp; Brackets" ConditionExpression="" Enabled="true">
        <vars>
          <var name="Arm_Spikes_MC">
            <value>=if(Arm_Spikes=true,"Y","N")</value>
          </var>
          <var name="Arm_Spikes_Bracket_MC">
            <value>=if(Arm_Spikes_Bracket=true,"Y","N")</value>
          </var>
        </vars>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Side Rails" ConditionExpression="" Enabled="true">
        <vars>
          <var name="SideRails_MC">
            <value>=if(SideRails=true,"Y","N")</value>
          </var>
        </vars>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Arm items eliminated from spike and regular beds" ConditionExpression="=BedType!=&quot;A&quot;" Enabled="true">
        <vars>
          <var name="Arm_Spikes_MC">
            <value>="Z"</value>
          </var>
          <var name="Arm_Spikes_Bracket_MC">
            <value>="Z"</value>
          </var>
          <var name="Arms">
            <value>="ZZ"</value>
          </var>
        </vars>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="Spikes items eliminated from arm and regular beds" ConditionExpression="=BedType!=&quot;S&quot;" Enabled="true">
        <vars>
          <var name="Spikes">
            <value>="ZZ"</value>
          </var>
        </vars>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Variable" Caption="regular items eliminated from arm and spike beds" ConditionExpression="=BedType!=&quot;R&quot;" Enabled="true">
        <vars>
          <var name="SideRails_MC">
            <value>="Z"</value>
          </var>
        </vars>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler" ChildType="true" RuleTypeName="Detail" Caption="Model Code" ConditionExpression="" Enabled="true">
        <Details>
          <Detail Category="SummaryDetails" PrintCode="" PrintSequence="10" Description="Model Code" Visible="=True" Value="=Bed+BedFeature+Headache+BallType+Tailboard_model+SkirtCA+FrontToolbox+RearToolbox_MC+Wireharness_MC+RubRails_MC+Outlets_MC+AdapterHarness_MC+MountKit_MC+Worklights_MC+Mudflaps_MC+Arms+Arm_Spikes_MC+Arm_Spikes_MC+Spikes+SideRails_MC" />
          <Detail Category="OrderDetails" PrintCode="E" PrintSequence="10" Description="Model Code" Visible="=True" Value="=Bed+BedFeature+Headache+BallType+Tailboard_model+SkirtCA+FrontToolbox+RearToolbox_MC+Wireharness_MC+RubRails_MC+Outlets_MC+AdapterHarness_MC+MountKit_MC+Worklights_MC+Mudflaps_MC+Arms+Arm_Spikes_MC+Arm_Spikes_MC+Spikes+SideRails_MC" />
        </Details>
      </Rule>
    </Rule>
  </RuleTree>
  <RuleTree Type="PostComponent" />
  <RuleTree Type="PostConfiguration" />
</Ruleset>