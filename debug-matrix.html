<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Matrix Debug</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
        .section { background: #f5f5f5; padding: 15px; margin: 15px 0; border-radius: 5px; }
        pre { background: #fff; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { background: #007cba; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #005a87; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>Matrix Loading Debug</h1>
    
    <div class="section">
        <h2>Matrix Loading Test</h2>
        <button onclick="testMatrixLoading()">Load MultipleAdapterGroups Matrix</button>
        <div id="matrix-result"></div>
    </div>

    <div class="section">
        <h2>Specific Lookup Test</h2>
        <button onclick="testSpecificLookup()">Test Kit 0612, Pump 9GL, AG B</button>
        <div id="lookup-result"></div>
    </div>

    <script type="module">
        import { loadMatrix, evaluateMatrixValue } from './matrix-loader.js';

        window.testMatrixLoading = async function() {
            const resultDiv = document.getElementById('matrix-result');
            resultDiv.innerHTML = '<p>Loading matrix...</p>';
            
            try {
                const matrix = await loadMatrix('MultipleAdapterGroups');
                
                if (matrix) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ Matrix loaded successfully!</div>
                        <h3>Matrix Structure:</h3>
                        <pre>${JSON.stringify({
                            name: matrix.name,
                            dimensions: matrix.dimensions,
                            valueCount: matrix.values.length,
                            sampleValues: matrix.values.slice(0, 3)
                        }, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to load matrix</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                console.error('Matrix loading error:', error);
            }
        };

        window.testSpecificLookup = async function() {
            const resultDiv = document.getElementById('lookup-result');
            resultDiv.innerHTML = '<p>Testing lookup...</p>';
            
            try {
                // Test the exact lookup we need
                const result = await evaluateMatrixValue('MultipleAdapterGroups', {
                    'Kit (Skip first 4)': '0612',
                    'Pump': '9GL',
                    'AG': 'B'
                }, { debug: true });
                
                resultDiv.innerHTML = `
                    <h3>Lookup Result:</h3>
                    <p><strong>Input:</strong> Kit=0612, Pump=9GL, AG=B</p>
                    <p><strong>Result:</strong> ${result || 'null'}</p>
                    <p><strong>Expected:</strong> 03-51261</p>
                    <p><strong>Status:</strong> ${result === '03-51261' ? '<span class="success">✅ Match!</span>' : '<span class="error">❌ No match</span>'}</p>
                `;
                
                // Also test with debug info
                console.log('Lookup test result:', result);
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                console.error('Lookup error:', error);
            }
        };
    </script>
</body>
</html>
