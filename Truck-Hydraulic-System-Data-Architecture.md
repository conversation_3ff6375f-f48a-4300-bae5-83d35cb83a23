# Truck Hydraulic System Configurator - Data Architecture Documentation

## Overview

The Truck Hydraulic System Configurator (SYS-700CPK) is a complex product configuration system that uses a combination of **matrices** and **option lists** to guide users through selecting compatible hydraulic system components for trucks. This document explains the data architecture, file relationships, and lookup mechanisms.

## System Architecture

The configurator operates on a **matrix-driven filtering system** where each user selection progressively narrows down the available options for subsequent fields through database lookups.

### Core Components

1. **Matrices** (`data/Matrices/`) - Logic tables that determine compatibility and filtering
2. **Option Lists** (`data/Option_Lists/`) - Master lists of available components with descriptions
3. **Field Definitions** (`truck-hydraulic-system-fields.js`) - UI field configurations and business logic
4. **Matrix Loader** (`matrix-loader.js`) - Runtime engine for matrix evaluation

## Data Flow Overview

```mermaid
graph TD
    A[User Input] --> B[Field Definition]
    B --> C{Has Matrix?}
    C -->|Yes| D[Matrix Lookup]
    C -->|No| E[Static Options]
    D --> F[Filter Groups]
    F --> G[Option List Lookup]
    G --> H[Filtered Options]
    E --> H
    H --> I[Display to User]
    I --> J[Update Form State]
    J --> K[Trigger Dependent Fields]
    K --> B
```

## File Structure

### Matrices Directory (`data/Matrices/`)

Matrices are XML files that contain lookup tables for component compatibility and filtering:

- **Filtering Matrices**: Determine which options are available

  - `Matrix_MultipleEngineGroups.xml` - Engine compatibility by make/year/vehicle type
  - `Matrix_PumpGroupMatrix.xml` - Pump compatibility by engine/application
  - `Matrix_Kit_Group.xml` - Kit compatibility by engine/pump/year
  - `Matrix_Clutch_Group.xml` - Clutch compatibility
  - `Matrix_VT_Filter.xml` - Vehicle type filtering

- **Calculation Matrices**: Determine specific component values

  - `Matrix_MultipleAdapterGroups.xml` - Adapter selection by kit/pump/application
  - `Matrix_HarnessMatrix.xml` - Harness selection by make/year/chassis

- **Pricing Matrices**: Component pricing
  - `Matrix_Pricing_Kit.xml`, `Matrix_Pricing_Pump.xml`, etc.

### Option Lists Directory (`data/Option_Lists/`)

Option Lists are XML files containing master component catalogs with descriptions:

- **Core Components**:

  - `OptionList_Make.xml` - Vehicle manufacturers
  - `OptionList_Engine.xml` - Engine specifications
  - `OptionList_Pump_1T1.xml` - Pump specifications
  - `OptionList_Kit.xml` - Kit part numbers
  - `OptionList_Clutch.xml` - Clutch specifications
  - `OptionList_Adapter.xml` - Adapter specifications with S=/P= values

- **Application-Specific**:
  - `OptionList_Product.xml` - DewEze products (AG applications)
  - `OptionList_Valve.xml` - Valve types
  - `OptionList_Control.xml` - Control systems

## Matrix Structure

### XML Format

```xml
<Matrix Name="MatrixName" Description="Purpose" DataType="String">
  <Dimension Name="Input1" DataType="String" AccessMethod="=" />
  <Dimension Name="Input2" DataType="String" AccessMethod="=" />
  <DefaultValue>ZZ</DefaultValue>
  <Value>
    <Lookup>InputValue1</Lookup>
    <Lookup>InputValue2</Lookup>
    <Value>OutputValue</Value>
  </Value>
</Matrix>
```

### Key Concepts

- **Dimensions**: Input parameters for the lookup
- **Lookups**: Specific input values to match
- **Value**: Output result when all lookups match
- **DefaultValue**: Fallback when no match is found
- **Wildcards**: `*` matches any input value

## Option List Structure

### XML Format

```xml
<OptionList Name="ListName" Description="Purpose">
  <Property Name="Value" DataType="String" />
  <Property Name="Description" DataType="String" />
  <Group Name="GroupCode1" />
  <Group Name="GroupCode2" />
  <Value>
    <Property Name="Value">ComponentCode</Property>
    <Property Name="Description">Full Description</Property>
    <Group Name="GroupCode1" />
  </Value>
</OptionList>
```

### Key Concepts

- **Groups**: Categories that components belong to (used for filtering)
- **Value**: Component part number or code
- **Description**: Human-readable component description
- **Properties**: Additional metadata (order, visibility, etc.)

## Field Processing Flow

Each form field follows this processing pattern:

1. **Condition Check**: Determine if field should be visible
2. **Matrix Evaluation**: Get filtering groups (if applicable)
3. **Option List Filtering**: Filter components by groups
4. **Display**: Present filtered options to user
5. **State Update**: Update form state when user selects
6. **Dependency Cascade**: Trigger updates to dependent fields

## Detailed Field Relationships

### Field Dependency Chain

```mermaid
graph TD
    A[Application] --> B[Make]
    A --> C[AGKitLessMiniPack]
    B --> D[Year]
    B --> E[Vehicle_Type]
    D --> E
    E --> F[Engine]
    F --> G[Pump]
    G --> H[Kit]
    H --> I[Clutch]
    H --> J[Adapter]
    G --> J
    A --> K[Product]
    K --> L[Valve]
    L --> M[ACC_Hose]
    M --> N[Aux_Hoses]
    A --> O[Control]
    A --> P[Harness]

    style A fill:#e1f5fe
    style J fill:#fff3e0
    style K fill:#f3e5f5
```

### Core Truck Selection Fields

#### 1. Application Field

- **Type**: Static select field
- **Data Source**: Hardcoded options in field definition
- **Options**: Agriculture (AG), Commercial (COM), Other (OTHER), Wrecker (WRK)
- **Dependencies**: Controls visibility of all subsequent fields
- **Matrix Impact**: Converted to AG flag (AG→B, Others→O) for matrix lookups

#### 2. Make Field

- **Type**: Dynamic select field
- **Data Source**: `OptionList_Make.xml`
- **Filtering**: Groups filtered by Application (AG uses group "B", others use group "O")
- **Dependencies**: Depends on Application
- **Matrix Impact**: Used in engine, vehicle type, and harness lookups

#### 3. Year Field

- **Type**: Dynamic select field
- **Data Source**: Generated programmatically (2000 to current year)
- **Dependencies**: Depends on Make
- **Matrix Impact**: Used in all subsequent compatibility matrices

#### 4. Vehicle_Type Field

- **Type**: Dynamic select field
- **Data Source**: Static options (Truck, Van)
- **Matrix**: `Matrix_VT_Filter` determines available types
- **Matrix Inputs**: Make, Year, Application
- **Logic**:
  - Result "NOVAN" or "1" → Truck only
  - Result "3" → Both Truck and Van
  - Default → Truck only

#### 5. Engine Field

- **Type**: Dynamic select field
- **Data Source**: `OptionList_Engine.xml`
- **Matrix**: `Matrix_MultipleEngineGroups`
- **Matrix Inputs**: Make, Vehicle_Type (normalized to OT), Year
- **Filtering Process**:
  1. Matrix returns engine group codes
  2. Option list filtered by matching groups
  3. Fallback to make-specific defaults if no match

#### 6. Pump Field

- **Type**: Dynamic select field
- **Data Source**: `OptionList_Pump_1T1.xml`
- **Matrix**: `Matrix_PumpGroupMatrix`
- **Matrix Inputs**: Engine, Application (normalized), Vehicle_Type, Year
- **Filtering Process**:
  1. Matrix returns pump group codes (comma-separated)
  2. Option list filtered by matching groups
  3. Results sorted numerically by pump size

#### 7. Kit Field

- **Type**: Dynamic select field
- **Data Source**: `OptionList_Kit.xml`
- **Matrix**: `Matrix_Kit_Group`
- **Matrix Inputs**: Engine, Year, Pump, Vehicle_Type (OT), Application (AG→B, Others→C)
- **Filtering Process**:
  1. Matrix returns kit group codes (dash-separated)
  2. Groups normalized (cleaned and padded)
  3. Option list filtered by matching normalized groups
  4. Fallback strategies for no matches

#### 8. Clutch Field

- **Type**: Dynamic select field
- **Data Source**: `OptionList_Clutch.xml`
- **Matrix**: `Matrix_Clutch_Group` with `Matrix_HD_Clutch` override
- **Matrix Inputs**: Current form state
- **Filtering Process**:
  1. Primary matrix lookup for clutch groups
  2. HD_Clutch matrix checked for exceptions
  3. Option list filtered by resulting groups

#### 9. Adapter Field (Display Only)

- **Type**: Calculated display field
- **Data Sources**:
  - `Matrix_MultipleAdapterGroups` (for adapter code)
  - `OptionList_Adapter.xml` (for full description)
- **Matrix Inputs**: Kit (last 4 digits), Pump, AG flag
- **Calculation Process**:
  1. Extract last 4 digits of kit number
  2. Matrix lookup returns adapter code (e.g., "03-51261")
  3. Option list lookup returns full description with S=/P= values
  4. Result: "03-51261; S=16HB-16MB90; P=10MJ-12MB45"

### Agriculture-Specific Fields (AG Questions)

These fields are only visible when Application = "AG":

#### 10. Product Field

- **Type**: Dynamic select field
- **Data Source**: `OptionList_Product.xml`
- **Dependencies**: Application = "AG"
- **No Matrix**: Direct option list display

#### 11. Valve Field

- **Type**: Dynamic select field
- **Data Source**: `OptionList_Valve.xml`
- **Matrix**: `Matrix_MultipleValveGroups`
- **Dependencies**: Application = "AG" AND Product ≠ "660"
- **Matrix Inputs**: Current form state

#### 12. Control Field

- **Type**: Dynamic select field
- **Data Source**: `OptionList_Control.xml`
- **Dependencies**: Application = "AG"
- **Filtering**: Groups filtered by Product type (660 vs others)

#### 13. Harness Field

- **Type**: Dynamic select field
- **Data Source**: Calculated from `Matrix_HarnessMatrix`
- **Matrix**: `Matrix_HarnessMatrix`
- **Matrix Inputs**: Year, Make (normalized), Chassis
- **Dependencies**: Application = "AG"

## Matrix Lookup Process Detail

```mermaid
sequenceDiagram
    participant UI as User Interface
    participant FL as Field Logic
    participant ML as Matrix Loader
    participant MF as Matrix File
    participant OL as Option List

    UI->>FL: User selects value
    FL->>FL: Normalize inputs
    FL->>ML: evaluateMatrixValue(name, inputs)
    ML->>MF: Load Matrix_[name].xml
    MF-->>ML: Return matrix data
    ML->>ML: Find matching lookup
    ML-->>FL: Return group codes
    FL->>OL: Load OptionList_[name].xml
    OL-->>FL: Return all options
    FL->>FL: Filter by group codes
    FL-->>UI: Return filtered options
```

## Data Normalization Rules

The system applies several normalization rules when interfacing between UI values and matrix/option list values:

### Vehicle Type Normalization

- UI: "TRUCK" → Matrix: "OT"
- UI: "VAN" → Matrix: "VN"

### Application Normalization

- UI: "AG" → Matrix AG Flag: "B"
- UI: "COM"/"OTHER"/"WRK" → Matrix AG Flag: "O"

### Make Normalization

- UI: "Ford" → Matrix: "FD"
- UI: "Dodge" → Matrix: "DG"
- UI: "Chevrolet" → Matrix: "GM"

### Kit Processing

- Full Kit: "700612" → Matrix Input: "0612" (last 4 digits)

### Group Code Normalization

- Clean: Remove non-alphanumeric characters
- Pad: Numeric groups padded to 3 digits with leading zeros
- Example: "620" → "620", "62" → "062"

## Matrix-Option List Relationships

```mermaid
graph LR
    subgraph "Matrices (Filtering Logic)"
        M1[MultipleEngineGroups]
        M2[PumpGroupMatrix]
        M3[Kit_Group]
        M4[Clutch_Group]
        M5[MultipleAdapterGroups]
        M6[VT_Filter]
        M7[MultipleValveGroups]
        M8[HarnessMatrix]
    end

    subgraph "Option Lists (Component Catalogs)"
        O1[OptionList_Engine.xml]
        O2[OptionList_Pump_1T1.xml]
        O3[OptionList_Kit.xml]
        O4[OptionList_Clutch.xml]
        O5[OptionList_Adapter.xml]
        O6[OptionList_Make.xml]
        O7[OptionList_Valve.xml]
    end

    M1 -->|Engine Groups| O1
    M2 -->|Pump Groups| O2
    M3 -->|Kit Groups| O3
    M4 -->|Clutch Groups| O4
    M5 -->|Adapter Code| O5
    M7 -->|Valve Groups| O7

    style M5 fill:#fff3e0
    style O5 fill:#fff3e0
```

## Error Handling and Fallbacks

### Matrix Lookup Failures

1. **No Matrix Match**: Returns `defaultValue` or `null`
2. **Matrix File Missing**: Falls back to hardcoded options
3. **XML Parse Error**: Logs error and uses fallback

### Option List Failures

1. **File Missing**: Uses hardcoded fallback options
2. **No Group Matches**: Returns empty array (no options available)
3. **Network Error**: Retries with cached data if available

### Common Fallback Strategies

- **Engine**: Make-specific defaults (FD67D for Ford, etc.)
- **Pump**: Standard sizes (9GL, 12GL)
- **Kit**: Generic kit numbers based on application
- **Adapter**: Make-specific adapter mappings with full S=/P= specifications

## Troubleshooting Guide

### Field Not Showing Options

1. Check matrix lookup inputs for correct normalization
2. Verify matrix file exists and is valid XML
3. Check option list file exists and contains matching groups
4. Review console logs for matrix evaluation details

### Incorrect Filtering

1. Verify input normalization (TRUCK→OT, AG→B, etc.)
2. Check matrix dimensions match input parameter names
3. Validate group codes in option list match matrix output
4. Review group normalization (cleaning and padding)

### Adapter Field Issues

1. Verify kit number extraction (last 4 digits)
2. Check MultipleAdapterGroups matrix for matching entry
3. Confirm OptionList_Adapter.xml contains the adapter code
4. Validate S=/P= values in adapter description

## Performance Considerations

### Caching Strategy

- **Matrix Cache**: Matrices cached in memory after first load
- **Option List Cache**: Not cached (loaded fresh each time)
- **Form State**: Debounced updates to prevent excessive re-rendering

### Optimization Opportunities

- Pre-load common matrices on page load
- Cache option lists in browser storage
- Implement progressive loading for large option lists
- Add service worker for offline capability

## Development Guidelines

### Adding New Fields

1. Define field in `truck-hydraulic-system-fields.js`
2. Create or update relevant matrix file
3. Create or update option list file
4. Add field to appropriate group in `getFieldGroups()`
5. Update dependency mapping in `updateFormState()`

### Modifying Matrices

1. Backup existing matrix file
2. Update XML structure maintaining dimension order
3. Test with various input combinations
4. Validate all dependent fields still function
5. Update documentation

### Testing Checklist

- [ ] All field dependencies work correctly
- [ ] Matrix lookups return expected results
- [ ] Option lists filter properly
- [ ] Fallback scenarios function
- [ ] Error handling works as expected
- [ ] Performance is acceptable
