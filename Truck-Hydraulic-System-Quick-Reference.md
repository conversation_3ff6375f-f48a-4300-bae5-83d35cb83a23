# Truck Hydraulic System Configurator - Quick Reference

## File Locations

### Core Application Files
- `sys-700cpk.html` - Main configurator page
- `sys-700cpk.js` - Form rendering and state management
- `truck-hydraulic-system-fields.js` - Field definitions and business logic
- `matrix-loader.js` - Matrix evaluation engine
- `sys-700cpk.css` - Styling

### Data Files
- `data/Matrices/` - Logic tables for compatibility and filtering
- `data/Option_Lists/` - Component catalogs with descriptions
- `data/Rulesets/` - Business rule definitions (XML)

## Key Matrices

| Matrix Name | Purpose | Key Inputs | Output |
|-------------|---------|------------|--------|
| `MultipleEngineGroups` | Engine compatibility | Make, Vehicle_Type, Year | Engine group codes |
| `PumpGroupMatrix` | Pump compatibility | Engine, Application, Vehicle_Type, Year | Pump group codes |
| `Kit_Group` | Kit compatibility | Engine, Year, Pump, Vehicle_Type, App | Kit group codes |
| `Clutch_Group` | Clutch compatibility | Form state | Clutch group codes |
| `MultipleAdapterGroups` | Adapter selection | Kit (last 4), Pump, AG flag | Adapter code |
| `VT_Filter` | Vehicle type filtering | Make, Year, Application | Vehicle type options |

## Key Option Lists

| Option List | Purpose | Groups Used |
|-------------|---------|-------------|
| `OptionList_Make.xml` | Vehicle manufacturers | B (AG), O (Others) |
| `OptionList_Engine.xml` | Engine specifications | Engine group codes |
| `OptionList_Pump_1T1.xml` | Pump specifications | Pump group codes |
| `OptionList_Kit.xml` | Kit part numbers | Kit group codes |
| `OptionList_Clutch.xml` | Clutch specifications | Clutch group codes |
| `OptionList_Adapter.xml` | Adapter specs with S=/P= | Adapter codes |

## Data Normalization Quick Reference

### UI to Matrix Value Mapping
```
Application:
  AG → B (matrix AG flag)
  COM/OTHER/WRK → O (matrix AG flag)

Vehicle Type:
  TRUCK → OT (matrix value)
  VAN → VN (matrix value)

Make:
  Ford → FD
  Dodge → DG
  Chevrolet → GM

Kit Processing:
  700612 → 0612 (last 4 digits for matrix)
```

### Group Code Processing
```
Clean: Remove non-alphanumeric characters
Pad: Numeric codes padded to 3 digits
Examples:
  "620" → "620"
  "62" → "062"
  "A-1" → "A1"
```

## Field Dependencies

```
Application
├── Make
│   ├── Year
│   └── Vehicle_Type
│       └── Engine
│           └── Pump
│               └── Kit
│                   ├── Clutch
│                   └── Adapter (also needs Pump)
└── AG-Specific Fields (if Application = AG)
    ├── Product
    ├── Valve
    ├── Control
    └── Harness
```

## Common Debugging Commands

### Enable Matrix Debug Logging
```javascript
const result = await evaluateMatrixValue('MatrixName', inputs, {
  debug: true
});
```

### Check Form State
```javascript
console.log('Current form state:', formState);
```

### Test Adapter Calculation
```javascript
const adapterField = fields.Adapter;
const result = await adapterField.getValue(formState);
console.log('Adapter result:', result);
```

### Check Field Visibility
```javascript
const isVisible = isFieldVisible('FieldName', formState);
console.log('Field visible:', isVisible);
```

## Error Messages and Solutions

### "No Engine Kit Available"
- **Cause**: MultipleEngineGroups matrix returns no match
- **Solution**: Check Make/Year/Vehicle_Type combination in matrix
- **Debug**: Verify normalization (TRUCK→OT, etc.)

### "No kit exists. Please contact Harper."
- **Cause**: Kit_Group matrix returns no match
- **Solution**: Check Engine/Year/Pump/Vehicle_Type/App combination
- **Debug**: Verify all inputs are properly normalized

### Adapter shows only code (no S=/P= values)
- **Cause**: OptionList_Adapter.xml lookup failed
- **Solution**: Verify adapter code exists in option list
- **Debug**: Search XML for matching Value property

### Field shows no options
- **Cause**: Matrix returns no groups or option list has no matching groups
- **Solution**: Check matrix output and option list group assignments
- **Debug**: Enable matrix debug logging

## Performance Tips

### Matrix Caching
- Matrices are cached after first load
- Clear cache: `window.matrixCache.clear()`
- Check cache: `window.matrixCache.has('MatrixName')`

### Form State Management
- Updates are debounced (50ms delay)
- Dependent fields are cleared automatically
- Form re-renders after state changes

### Network Optimization
- Option lists loaded on demand
- Matrix files cached in memory
- Consider pre-loading common matrices

## Testing Scenarios

### Valid Configuration Test
```javascript
// Ford Agriculture 2024 - Should work completely
{
  Application: 'AG',
  Make: 'FD',
  Year: '2024',
  Vehicle_Type: 'TRUCK',
  Engine: 'FD67D',
  Pump: '9GL',
  Kit: '700612',
  Clutch: '8GRVA'
}
// Expected Adapter: "03-51261; S=16HB-16MB90; P=10MJ-12MB45"
```

### Edge Case Test
```javascript
// Old year - Should trigger fallbacks
{
  Application: 'AG',
  Make: 'FD',
  Year: '1999',
  Vehicle_Type: 'TRUCK'
}
// Expected: Fallback options used
```

### Commercial Application Test
```javascript
// Commercial Freightliner - Different matrix paths
{
  Application: 'COM',
  Make: 'FL',
  Year: '2024',
  Vehicle_Type: 'TRUCK'
}
// Expected: No AG fields visible
```

## File Structure Summary

```
configurator/
├── sys-700cpk.html              # Main page
├── sys-700cpk.js                # Form logic
├── truck-hydraulic-system-fields.js  # Field definitions
├── matrix-loader.js             # Matrix engine
├── sys-700cpk.css              # Styles
└── data/
    ├── Matrices/               # Logic tables
    │   ├── Matrix_MultipleEngineGroups.xml
    │   ├── Matrix_PumpGroupMatrix.xml
    │   ├── Matrix_Kit_Group.xml
    │   ├── Matrix_Clutch_Group.xml
    │   ├── Matrix_MultipleAdapterGroups.xml
    │   └── ...
    └── Option_Lists/           # Component catalogs
        ├── OptionList_Engine.xml
        ├── OptionList_Pump_1T1.xml
        ├── OptionList_Kit.xml
        ├── OptionList_Clutch.xml
        ├── OptionList_Adapter.xml
        └── ...
```

## Key Functions

### Matrix Evaluation
```javascript
evaluateMatrixValue(matrixName, inputs, options)
```

### Field Visibility Check
```javascript
isFieldVisible(fieldName, formState)
```

### Form State Update
```javascript
updateFormState(fieldName, value)
```

### Display Field Update
```javascript
updateDisplayFields()
```
