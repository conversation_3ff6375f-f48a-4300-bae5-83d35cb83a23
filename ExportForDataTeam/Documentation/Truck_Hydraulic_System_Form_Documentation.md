# Truck Hydraulic System Form Documentation (sys-700cpk.html)

## Overview

This document provides comprehensive documentation for every field in the Truck Hydraulic System configurator form (sys-700cpk.html), including data sources, dependencies, and business logic.

## Form Field Documentation

### 1. Application Selection

**Field Name:** Application  
**Form Label:** "Application"  
**Data Type:** Dropdown  
**Required:** Yes  
**Source Data Sheet:** `ag_products.csv`  
**Options:** Agriculture, Commercial  
**Business Logic:**

- Determines which product categories are available
- Agriculture enables AG product selection and valve requirements
- Commercial focuses on standard hydraulic applications
- Filters available vehicles, kits, and components based on application type

### 2. Less AG Mini Pack

**Field Name:** LessAGMiniPack  
**Form Label:** "Less AG Mini Pack?"  
**Data Type:** Checkbox  
**Required:** No  
**Source Data Sheet:** N/A (Boolean flag)  
**Dependencies:** Only visible when Application = "Agriculture"  
**Business Logic:**

- When checked, excludes mini pack components from kit selection
- Affects pricing calculations for AG applications
- Modifies available kit options in `hydraulic_kits.csv`

### 3. Vehicle Make Selection

**Field Name:** Make  
**Form Label:** "Select Make of Vehicle"  
**Data Type:** Dropdown  
**Required:** Yes  
**Source Data Sheet:** `hydraulic_vehicles.csv` (make column)  
**Options:** Ford, Dodge/Ram, Chevy/GMC, Freightliner, International, Mack, etc.  
**Business Logic:**

- Filters available years, engines, and vehicle types
- Determines compatible kits from `hydraulic_kits.csv`
- Affects adapter selection from `adapters.csv`
- Cross-references with harness compatibility in `harnesses.csv`

### 4. Truck Year

**Field Name:** Year  
**Form Label:** "Truck Year"  
**Data Type:** Dropdown  
**Required:** Yes  
**Source Data Sheet:** `hydraulic_vehicles.csv` (year column)  
**Dependencies:** Filtered by selected Make  
**Business Logic:**

- Determines engine options available for the make/year combination
- Affects harness compatibility (year ranges in `harnesses.csv`)
- Influences kit selection and pricing

### 5. Vehicle Type

**Field Name:** VehicleType  
**Form Label:** "Select Vehicle Type"  
**Data Type:** Dropdown  
**Required:** Yes  
**Source Data Sheet:** `hydraulic_vehicles.csv` (vehicle_type column)  
**Options:** Truck, Van  
**Dependencies:** Filtered by Make and Year  
**Business Logic:**

- Determines available engine options
- Affects pump size limitations (vans typically use smaller pumps)
- Influences kit selection from `hydraulic_kits.csv`

### 6. Engine Selection

**Field Name:** Engine  
**Form Label:** "Select Engine"  
**Data Type:** Dropdown  
**Required:** Yes  
**Source Data Sheet:** `hydraulic_vehicles.csv` (engine column)  
**Dependencies:** Filtered by Make, Year, and VehicleType  
**Business Logic:**

- Determines compatible kits from `hydraulic_kits.csv` (engine_compatibility column)
- Affects pump selection from `pumps.csv` (engine_compatibility column)
- Influences adapter requirements from `adapters.csv`
- Special handling for Mack engines (macfit flag in `hydraulic_vehicles.csv`)

### 7. Pump Size

**Field Name:** PumpSize  
**Form Label:** "Select Pump Size"  
**Data Type:** Dropdown  
**Required:** Yes  
**Source Data Sheet:** `pumps.csv`  
**Dependencies:** Filtered by Engine and Application  
**Business Logic:**

- Must be compatible with selected engine (engine_compatibility in `pumps.csv`)
- Affects kit selection (pump_compatibility in `hydraulic_kits.csv`)
- Influences adapter selection (pump_compatibility in `adapters.csv`)
- Pricing from base_price column in `pumps.csv`

### 8. Kit Selection

**Field Name:** Kit  
**Form Label:** "Select Your Kit"  
**Data Type:** Dropdown  
**Required:** Yes  
**Source Data Sheet:** `hydraulic_kits.csv`  
**Dependencies:** Filtered by Engine, PumpSize, and Application  
**Business Logic:**

- Must match engine_compatibility and pump_compatibility
- Application type must match application_type column
- Determines base pricing from base_price column
- Affects available clutch options

### 9. Clutch Selection

**Field Name:** Clutch  
**Form Label:** "Select Your Clutch"  
**Data Type:** Dropdown  
**Required:** Yes  
**Source Data Sheet:** `clutches.csv`  
**Dependencies:** Filtered by selected Kit  
**Business Logic:**

- Must be compatible with selected kit (kit_compatibility column)
- Groove count affects adapter selection
- Pricing from base_price column
- Clutch group determines available options

### 10. Adapter

**Field Name:** Adapter  
**Form Label:** "Adapter"  
**Data Type:** Display/Calculated  
**Required:** N/A (Auto-calculated)  
**Source Data Sheet:** `adapters.csv`  
**Dependencies:** Make, Kit, PumpSize, Clutch  
**Business Logic:**

- Auto-selected based on make_compatibility, kit_compatibility, and pump_compatibility
- Displays adapter_part_number and description
- Pricing added to total from base_price column
- Format: "Part Number; S=Supply; P=Pressure" specifications

### 11. AG Product Selection (Agriculture Only)

**Field Name:** AGProduct  
**Form Label:** "Select DewEze Product"  
**Data Type:** Dropdown  
**Required:** Yes (when Application = Agriculture)  
**Source Data Sheet:** `ag_products.csv`  
**Dependencies:** Only visible when Application = "Agriculture"  
**Business Logic:**

- Determines if valve is required (valve_required column)
- Affects valve selection options
- Influences control system requirements
- Links to product series and type

### 12. Valve Type (Agriculture Only)

**Field Name:** ValveType  
**Form Label:** "What Type of valve do you want"  
**Data Type:** Dropdown  
**Required:** Yes (when AG Product requires valve)  
**Source Data Sheet:** `valves.csv`  
**Dependencies:** AGProduct selection and valve_required flag  
**Business Logic:**

- Filtered by product_compatibility with selected AG product
- Special handling for 660 series (for_660_series flag)
- Pricing from base_price column
- Affects control system compatibility

### 13. Cab Type (Agriculture Only)

**Field Name:** CabType  
**Form Label:** "Select Cab Type"  
**Data Type:** Dropdown  
**Required:** Yes (when Application = Agriculture)  
**Source Data Sheet:** `harnesses.csv` (chassis_compatibility column)  
**Options:** Crew Cab, Regular Cab, Extended Cab  
**Dependencies:** Make and Year selection  
**Business Logic:**

- Determines harness compatibility
- Affects harness pricing and part number selection
- Cross-references with make and year for exact harness match

### 14. Chassis Type (Agriculture Only)

**Field Name:** ChassisType  
**Form Label:** "Select Chassis Type"  
**Data Type:** Dropdown  
**Required:** Yes (when Application = Agriculture)  
**Source Data Sheet:** `harnesses.csv` (chassis_compatibility column)  
**Options:** Box Take-Off, Crew Cab, Regular Cab  
**Dependencies:** CabType and Make selection  
**Business Logic:**

- Further refines harness selection
- Must match chassis_compatibility in `harnesses.csv`
- Affects final harness part number and pricing

### 15. Control Option (Agriculture Only)

**Field Name:** ControlOption  
**Form Label:** "Select Control Option"  
**Data Type:** Dropdown  
**Required:** Yes (when Application = Agriculture)  
**Source Data Sheet:** `controls.csv`  
**Dependencies:** ValveType and AGProduct  
**Business Logic:**

- Filtered by product_compatibility with valve and AG product
- Wireless options have higher pricing (wireless_capable flag)
- Must be compatible with selected valve type
- Pricing from base_price column

### 16. Adapter Harness (Agriculture Only)

**Field Name:** AdapterHarness  
**Form Label:** "Select Your Adapter"  
**Data Type:** Dropdown  
**Required:** Yes (when Application = Agriculture)  
**Source Data Sheet:** `harnesses.csv`  
**Dependencies:** Make, Year, CabType, ChassisType  
**Business Logic:**

- Exact match required for make_compatibility, year_compatibility, and chassis_compatibility
- Displays harness_part_number and description
- Pricing from base_price column
- Special handling for Ford (Box Delete harnesses)

## Data Relationships and Cross-References

### Primary Lookup Chains:

1. **Vehicle Selection Chain:**

   - Make → Year → VehicleType → Engine
   - Each step filters the next based on `hydraulic_vehicles.csv`

2. **Kit Selection Chain:**

   - Engine + PumpSize + Application → Kit
   - Uses engine_compatibility, pump_compatibility, and application_type from `hydraulic_kits.csv`

3. **Adapter Selection Chain:**

   - Make + Kit + PumpSize → Adapter
   - Auto-calculated from `adapters.csv` compatibility columns

4. **Agriculture-Specific Chain:**

   - AGProduct → ValveType → ControlOption
   - Uses product_compatibility and valve_required flags

5. **Harness Selection Chain:**
   - Make + Year + CabType + ChassisType → Harness
   - Exact match from `harnesses.csv` compatibility columns

### Pricing Calculations:

- **Base Price:** Kit base_price from `hydraulic_kits.csv`
- **Pump Price:** Added from `pumps.csv` base_price
- **Clutch Price:** Added from `clutches.csv` base_price
- **Adapter Price:** Added from `adapters.csv` base_price
- **Valve Price:** Added from `valves.csv` base_price (if applicable)
- **Control Price:** Added from `controls.csv` base_price (if applicable)
- **Harness Price:** Added from `harnesses.csv` base_price (if applicable)

### Special Business Rules:

1. **Mack Vehicles:** Special handling for side port pumps (macfit flag)
2. **660 Series:** Special valve options (for_660_series flag in `valves.csv`)
3. **Ford Box Delete:** Specific harness requirements for 2023+ models
4. **Agriculture vs Commercial:** Different component availability and pricing
5. **Wireless Controls:** Premium pricing for wireless-capable options

## Form Validation Rules:

1. All required fields must be completed before proceeding
2. Each selection must be compatible with previous selections
3. Agriculture-specific fields only appear when Application = "Agriculture"
4. Adapter is auto-calculated and cannot be manually overridden
5. Pricing updates dynamically as selections are made

## Integration Points:

- **Matrix Files:** Used for complex compatibility lookups
- **Pricing Groups:** Referenced for bulk pricing calculations
- **Option Lists:** Source for dropdown population
- **ERD Relationships:** Maintain referential integrity between data sheets
