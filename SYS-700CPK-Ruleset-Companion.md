# SYS-700CPK Ruleset Companion

This document explains the business rules and logic as implemented in the SYS-700CPK Configurator, based on the rules defined in `Rulesets/Ruleset SYS.700CPK.xml` and related matrix/option files.

---

## 1. Field Definitions (ComponentAttributes)

- The XML defines all fields (ComponentAttributes) for the configurator, including:
  - Application, Year, Vehicle_Type, Make, Engine, Pump, Kit, Clutch, Adapter, Product, Cab, Chassis, Reservoir, Hoses660, Valve, ACC_Hose, Aux_Hoses, Control, Harness, AGKitLessMiniPack, ValveYN, Macfit
- Each field specifies:
  - Data type (String, Boolean, etc.)
  - Option list (e.g., OptionListID="Make")
  - Display type (e.g., TypeableDropDown, RadioButtonVertical, CheckBox)
  - Required/visibility flags
- These definitions are mapped directly to the `fields` object in `truck-hydraulic-system-fields.js`.

## 2. Field Order and Grouping

- The ruleset organizes fields into logical screens/pages (e.g., "Truck Selections", "AG Questions").
- The JS implementation uses `getFieldGroups()` to mirror this grouping for UI rendering.

## 3. Option Filtering and Matrix Logic

- **Option lists** for fields are filtered using matrix lookups and group logic:
  - Example: `Make.OptionListGroup` is set to "B" for AG, "O" for others.
  - `EngineGroup`, `PumpGroupMatrix`, `Kit_Group`, `Clutch_Group`, `MultipleAdapterGroups` are all referenced in the XML and used in JS for filtering options.
- **Matrix lookups** (e.g., `LookUpMatrix("Kit_Group", Engine, Year, Pump, Vehicle_Type, App)`) determine which options are valid for the current state.
- The JS code uses `evaluateMatrixValue` to perform these lookups.

## 4. RuleTree Logic (Screen Flow and Conditions)

- The XML RuleTree defines the flow of the form:
  - Screens for Application, Year, Make, Vehicle_Type, Engine, Pump, Kit, Clutch, Adapter, etc.
  - Conditional screens (e.g., AGKitLessMiniPack only if Application = AG)
  - Special conditions (e.g., Macfit for Mack, warning messages for certain kits)
- The JS code implements these conditions using the `condition` property and dynamic rendering.

## 5. Calculated/Display Fields and Messages

- Some fields (e.g., Adapter) are calculated using matrix lookups and/or hardcoded logic.
- The XML includes rules for displaying messages (e.g., "No Engine Kit Available", warnings for specific kits).
- The JS code logs or displays these messages as needed.

## 6. Pricing and Manufacturing Rules

- The XML includes rules for calculating prices using matrices (e.g., `Pricing_Kit`, `Pricing_Pump`, etc.) and variables (e.g., Markup, FirstPrice).
- Manufacturing rules (MFG Component, MFG Material) are defined for BOM generation.
- These are not fully implemented in the JS UI but are referenced for integration.

## 7. Resetting Dependent Fields

- When a user changes a field, all dependent fields are reset to prevent invalid combinations. This is managed by the `dependentFields` map in `sys-700cpk.js`.

## 8. Fallbacks and Demo Logic

- If XML or matrix data is missing or cannot be loaded, the code provides fallback options and values based on common scenarios (as seen in the video transcripts).

## 9. Extending the Rules

- To add or change rules, update the relevant matrix or option list XML files and adjust the field logic in `truck-hydraulic-system-fields.js`.
- For new dependencies, add or update the `condition` and `getOptions`/`getValue` logic for the affected fields.

---

**For more details, see the inline comments in the code and the main documentation file.**
