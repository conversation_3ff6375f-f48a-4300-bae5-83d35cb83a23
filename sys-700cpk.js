// sys-700cpk.js
// Main UI logic for the SYS-700CPK Configurator
//
// This script renders the form based on field definitions in truck-hydraulic-system-fields.js,
// manages form state, handles user input, and updates dependent fields dynamically.
//
// Key concepts:
// - Uses getFieldGroups() to organize fields into sections.
// - Each field is rendered according to its type and dynamic options.
// - Changing a field resets dependent fields and triggers a re-render.
// - Display/calculated fields (e.g., Adapter) are updated live.
//
// To add new fields or groups, update truck-hydraulic-system-fields.js.
//
// See SYS-700CPK-Configurator-Documentation.md for a high-level overview and usage examples.

// Modular JS for SYS.700CPK Configurator
import { fields, getFieldGroups, isFieldVisible } from './truck-hydraulic-system-fields.js';
import { evaluateMatrixValue } from './matrix-loader.js';

// Form state management
var formState = {};
var pendingUpdates = new Map();
var updateTimeoutId = null;
var lastValues = new Map();

// Queue a form update with debouncing
function queueFormUpdate(fieldName, value) {
  pendingUpdates.set(fieldName, value);
  clearTimeout(updateTimeoutId);
  updateTimeoutId = setTimeout(processPendingUpdates, 50);
}

// Process all pending form updates
async function processPendingUpdates() {
  var updates = Array.from(pendingUpdates.entries());
  pendingUpdates.clear();

  for (var i = 0; i < updates.length; i++) {
    var [fieldName, value] = updates[i];
    var lastValue = lastValues.get(fieldName);

    // Only update if the value has actually changed
    if (JSON.stringify(lastValue) !== JSON.stringify(value)) {
      await updateFormState(fieldName, value);
      lastValues.set(fieldName, value);
    } else {
      console.debug('Skipping update for ' + fieldName + ' - value unchanged');
    }
  }
}

// Helper function to track form state changes
function logStateChange(fieldName, oldValue, newValue) {
  if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
    console.log('Form field updated - ' + fieldName + ':', {
      oldValue: oldValue,
      newValue: newValue,
      timestamp: new Date().toISOString()
    });
  }
}

// Helper function for field validation
function validateField(fieldName, value, fieldDef) {
  if (!value && fieldDef.required) {
    return { isValid: false, message: `${fieldDef.label} is required` };
  }
  return { isValid: true };
}

// Update debug panel
function updateDebugPanel() {
  const debugContent = document.getElementById('debug-content');
  if (debugContent) {
    const adapterField = fields.Adapter;
    const adapterVisible = adapterField ? isFieldVisible('Adapter', formState) : false;
    const adapterCondition = adapterField && adapterField.condition ? adapterField.condition(formState) : 'No condition';

    debugContent.innerHTML = `
      Form State: ${JSON.stringify(formState)}<br>
      Adapter Field Exists: ${!!adapterField}<br>
      Adapter Visible: ${adapterVisible}<br>
      Adapter Condition: ${adapterCondition}<br>
      Kit: ${formState.Kit || 'Not set'}<br>
      Pump: ${formState.Pump || 'Not set'}
    `;
  }
}

// Update display fields with calculated values
async function updateDisplayFields() {
  const displayFields = document.querySelectorAll('[data-display-field="true"]');

  console.log(`Found ${displayFields.length} display fields to update`);
  updateDebugPanel();

  for (const displayElement of displayFields) {
    const fieldName = displayElement.dataset.fieldName;
    const fieldDef = fields[fieldName];

    console.log(`Updating display field: ${fieldName}`);

    if (fieldDef && fieldDef.getValue && typeof fieldDef.getValue === 'function') {
      try {
        // Show loading state
        displayElement.textContent = 'Calculating...';
        displayElement.classList.add('loading');

        // Get the calculated value
        const value = await fieldDef.getValue(formState);

        // Update the display
        displayElement.textContent = value || 'Not available';
        displayElement.classList.remove('loading');

        console.log(`Updated display field ${fieldName}:`, value);
      } catch (error) {
        console.error(`Error updating display field ${fieldName}:`, error);
        displayElement.textContent = 'Error calculating value';
        displayElement.classList.remove('loading');
        displayElement.classList.add('error');
      }
    } else {
      console.warn(`Display field ${fieldName} has no getValue function`);
    }
  }
}

// Display error message for a field
function showFieldError(fieldContainer, message) {
  let errorDiv = fieldContainer.querySelector('.field-error');
  if (!errorDiv) {
    errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    fieldContainer.appendChild(errorDiv);
  }
  errorDiv.textContent = message;
  fieldContainer.classList.add('has-error');
}

// Clear error message for a field
function clearFieldError(fieldContainer) {
  if (!fieldContainer) {
    console.log('No field container to clear errors from');
    return;
  }
  const errorDiv = fieldContainer.querySelector('.field-error');
  if (errorDiv) {
    errorDiv.remove();
  }
  fieldContainer.classList.remove('has-error');
}

// Create input element based on field definition
async function createInput(fieldName, fieldDef, options = []) {
  const container = document.createElement('div');
  container.className = 'input-container';

  // Add label
  const label = document.createElement('label');
  label.htmlFor = fieldName;
  label.textContent = fieldDef.label;
  if (fieldDef.required) {
    const requiredSpan = document.createElement('span');
    requiredSpan.className = 'required-indicator';
    requiredSpan.textContent = '*';
    label.appendChild(requiredSpan);
  }
  container.appendChild(label);

  // Add help text if available
  if (fieldDef.helpText) {
    const helpText = document.createElement('div');
    helpText.className = 'field-help-text';
    helpText.textContent = fieldDef.helpText;
    container.appendChild(helpText);
  }

  // Create input element
  let input;
  if (fieldDef.type === 'select') {
    input = document.createElement('select');
    input.name = fieldName;
    input.id = fieldName;
    input.required = fieldDef.required;

    // Add loading state
    input.disabled = true;
    const loadingOption = document.createElement('option');
    loadingOption.value = '';
    loadingOption.textContent = 'Loading options...';
    input.appendChild(loadingOption);

    // Use either provided dynamic options or static options
    const optionsToUse = options.length > 0 ? options : (fieldDef.options || []);

    // Clear loading state and add options
    input.innerHTML = '';
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = `Select ${fieldDef.label}`;
    input.appendChild(defaultOption);

    optionsToUse.forEach(opt => {
      const option = document.createElement('option');
      option.value = opt.value;
      option.textContent = opt.label || opt.value;
      input.appendChild(option);
    });
    input.disabled = false;

  } else if (fieldDef.type === 'boolean') {
    const wrapper = document.createElement('div');
    wrapper.className = 'checkbox-wrapper';

    input = document.createElement('input');
    input.type = 'checkbox';
    input.name = fieldName;
    input.id = fieldName;

    const checkboxLabel = document.createElement('span');
    checkboxLabel.className = 'checkbox-label';
    checkboxLabel.textContent = fieldDef.checkboxLabel || 'Yes';

    wrapper.appendChild(input);
    wrapper.appendChild(checkboxLabel);
    container.appendChild(wrapper);
    return container;

  } else if (fieldDef.type === 'display') {
    // Handle display fields that show calculated values
    input = document.createElement('div');
    input.className = 'display-field';
    input.id = fieldName;
    input.textContent = 'Calculating...';

    // Mark as display field for special handling
    input.dataset.displayField = 'true';
    input.dataset.fieldName = fieldName;

  } else {
    input = document.createElement('input');
    input.type = fieldDef.type || 'text';
    input.name = fieldName;
    input.id = fieldName;
    input.required = fieldDef.required;

    if (fieldDef.pattern) {
      input.pattern = fieldDef.pattern;
    }
    if (fieldDef.minLength) {
      input.minLength = fieldDef.minLength;
    }
    if (fieldDef.maxLength) {
      input.maxLength = fieldDef.maxLength;
    }
  }

  // Add aria labels and validation attributes
  input.setAttribute('aria-label', fieldDef.label);
  if (fieldDef.required) {
    input.setAttribute('aria-required', 'true');
  }

  container.appendChild(input);
  return container;
}

// Render form fields based on current state
async function renderForm() {
  const formElement = document.getElementById('sys-700cpk-form');
  if (!formElement) {
    console.error('Form element not found');
    return;
  }

  console.log('Rendering form with fields:', Object.keys(fields));
  console.log('Current form state:', formState);

  // Clear the form
  formElement.innerHTML = '';
  formElement.className = 'configurator-form';

  // Add form description if present
  if (fields.description) {
    const description = document.createElement('div');
    description.className = 'form-description';
    description.textContent = fields.description;
    formElement.appendChild(description);
  }

  // Get field groups for organized display
  const fieldGroups = getFieldGroups();

  // Track visible groups for animation
  let visibleGroupCount = 0;

  // Render each group
  for (const [groupKey, groupDef] of Object.entries(fieldGroups)) {
    // Check if group should be visible
    const shouldShowGroup = !groupDef.condition || groupDef.condition(formState);

    if (!shouldShowGroup) {
      continue;
    }

    visibleGroupCount++;

    // Create group container with animation delay
    const groupContainer = document.createElement('div');
    groupContainer.className = 'field-group';
    groupContainer.id = `${groupKey}-group`;
    groupContainer.style.animationDelay = `${visibleGroupCount * 0.1}s`;

    // Add group title
    const groupTitle = document.createElement('h2');
    groupTitle.textContent = groupDef.title;
    groupTitle.className = 'group-title';
    groupContainer.appendChild(groupTitle);

    // Add group description if present
    if (groupDef.description) {
      const groupDesc = document.createElement('div');
      groupDesc.className = 'group-description';
      groupDesc.textContent = groupDef.description;
      groupContainer.appendChild(groupDesc);
    }

    let visibleFieldCount = 0;

    // Render fields in this group
    for (const fieldName of groupDef.fields) {
      const fieldDef = fields[fieldName];
      if (!fieldDef) continue;

      // Check if field should be visible
      const shouldShow = isFieldVisible(fieldName, formState);

      if (shouldShow) {
        visibleFieldCount++;

        // Create field container
        const fieldContainer = document.createElement('div');
        fieldContainer.id = `${fieldName}-container`;
        fieldContainer.className = 'field-container';
        fieldContainer.style.animationDelay = `${visibleFieldCount * 0.05}s`;

        console.log('🔧 Creating field container:', {
          fieldName,
          id: fieldContainer.id,
          visibility: 'visible',
          currentValue: formState[fieldName]
        });

        try {
          // Get dynamic options if available
          let options = [];
          if (fieldDef.getOptions) {
            options = await fieldDef.getOptions(formState);
            console.log(`Dynamic options for ${fieldName}:`, options);
          }

          // Create the field input
          const input = await createInput(fieldName, fieldDef, options);

          // Add event listeners to the actual input element
          const inputElement = input.querySelector('select, input');
          if (inputElement) {
            // Mark this input as a form field for the form-level handler to identify
            inputElement.dataset.formField = 'true';
            console.log('🔧 Marked input as form field:', fieldName);
          }

          fieldContainer.appendChild(input);

          // Restore previous value if it exists
          if (formState[fieldName] !== undefined) {
            const inputElement = input.querySelector('select, input');
            if (inputElement) {
              if (inputElement.type === 'checkbox') {
                inputElement.checked = formState[fieldName];
              } else {
                inputElement.value = formState[fieldName];
              }

              // Validate restored value
              const validation = validateField(fieldName, formState[fieldName], fieldDef);
              if (!validation.isValid) {
                showFieldError(fieldContainer, validation.message);
              }
            }
          }

        } catch (error) {
          console.error(`Error rendering field ${fieldName}:`, error);
          // Show error state in the UI
          fieldContainer.classList.add('has-error');
          const errorDiv = document.createElement('div');
          errorDiv.className = 'field-error';
          errorDiv.textContent = 'Error loading field options';
          fieldContainer.appendChild(errorDiv);
        }

        groupContainer.appendChild(fieldContainer);
      }
    }

    // Only add group if it has visible fields
    if (visibleFieldCount > 0) {
      formElement.appendChild(groupContainer);
    }
  }

  // Add navigation/submission buttons if needed
  const buttonContainer = document.createElement('div');
  buttonContainer.className = 'form-buttons';

  if (Object.keys(formState).length > 0) {
    const backButton = document.createElement('button');
    backButton.type = 'button';
    backButton.className = 'button button-secondary';
    backButton.textContent = 'Back';
    backButton.onclick = () => history.back();
    buttonContainer.appendChild(backButton);
  }

  const submitButton = document.createElement('button');
  submitButton.type = 'submit';
  submitButton.className = 'button button-primary';
  submitButton.textContent = 'Continue';
  buttonContainer.appendChild(submitButton);

  formElement.appendChild(buttonContainer);
}

// Handler for field value changes
async function handleFieldChange(event) {
  const fieldElement = event.target;
  const fieldName = fieldElement.getAttribute('name');
  const fieldType = fieldElement.getAttribute('type');
  const value = fieldType === 'checkbox' ? fieldElement.checked : fieldElement.value;

  console.log('🔧 Field change event:', {
    fieldName,
    oldValue: formState[fieldName],
    newValue: value,
    fieldType
  });

  await updateFormState(fieldName, value);
}  // Update form state and handle dependent fields
async function updateFormState(fieldName, value) {
  const oldValue = formState[fieldName];
  console.log('🔧 Updating form state:', {
    fieldName,
    oldValue,
    newValue: value,
    hasField: !!fields[fieldName],
    currentState: { ...formState }
  });

  // Validate the field
  const fieldDef = fields[fieldName];
  const validation = validateField(fieldName, value, fieldDef);
  const fieldContainer = document.getElementById(`${fieldName}-container`);

  console.log('🔧 Field update:', {
    fieldName,
    oldValue,
    newValue: value,
    hasContainer: !!fieldContainer,
    validation
  });

  if (!validation.isValid && fieldContainer) {
    showFieldError(fieldContainer, validation.message);
    return false;
  } else if (fieldContainer) {
    clearFieldError(fieldContainer);
  }

  // Update the state for the changed field
  formState[fieldName] = value;

  // Update debug panel
  updateDebugPanel();

  // Field dependencies mapping
  const dependencyMap = {
    'Application': {
      fields: ['Make', 'Year', 'Vehicle_Type', 'Engine', 'Product', 'Valve', 'Control', 'Harness'],
      condition: () => true // Always clear on Application change
    },
    'Make': {
      fields: ['Year', 'Vehicle_Type', 'Engine', 'Kit', 'Clutch', 'Adapter'],
      condition: () => formState.Make !== oldValue
    },
    'Year': {
      fields: ['Vehicle_Type', 'Engine', 'Kit'],
      condition: () => formState.Year !== oldValue
    },
    'Vehicle_Type': {
      fields: ['Engine', 'Product', 'Kit'],
      condition: () => formState.Vehicle_Type !== oldValue
    },
    'Engine': {
      fields: ['Kit', 'Pump', 'Product'],
      condition: () => {
        console.log('🔧 Engine field change detected:', {
          oldEngine: oldValue,
          newEngine: formState.Engine,
          dependentFields: ['Kit', 'Pump', 'Product'],
          currentPumpValue: formState.Pump
        });
        return formState.Engine !== oldValue;
      }
    },
    'Pump': {
      fields: ['Kit'],
      condition: () => formState.Pump !== oldValue
    }
  };

  // Clear dependent fields based on dependency map
  const dependency = dependencyMap[fieldName];
  if (dependency && dependency.condition()) {
    console.log('🔧 Processing field dependencies:', {
      changedField: fieldName,
      dependentFields: dependency.fields,
      currentState: { ...formState }
    });

    for (const dependentField of dependency.fields) {
      if (formState[dependentField]) {
        console.log(`🔧 Clearing dependent field:`, {
          field: dependentField,
          oldValue: formState[dependentField],
          trigger: fieldName,
          triggerOldValue: oldValue,
          triggerNewValue: value
        });
        delete formState[dependentField];
      }
    }

    // After clearing state, try to clear UI errors if containers exist
    dependency.fields.forEach(field => {
      const container = document.getElementById(`${field}-container`);
      if (container) {
        clearFieldError(container);
        container.classList.add('loading');
      }
    });
  }

  // Show loading indicator for affected fields
  const affectedFields = dependency?.fields || [];
  affectedFields.forEach(field => {
    const container = document.getElementById(`${field}-container`);
    if (container) {
      container.classList.add('loading');
    }
  });

  console.log('🔧 Re-rendering form with state:', formState);
  // Re-render form to reflect changes
  await renderForm();

  // Update display fields with calculated values
  await updateDisplayFields();

  // Log visible fields after render
  const visibleFields = Object.keys(fields).filter(fieldName => {
    const fieldDef = fields[fieldName];
    return !fieldDef.condition || fieldDef.condition(formState);
  });
  console.log('🔧 Visible fields after render:', visibleFields);

  // Restore focus to the updated field
  const updatedFieldContainer = document.getElementById(`${fieldName}-container`);
  if (updatedFieldContainer) {
    const inputElement = updatedFieldContainer.querySelector('input, select');
    if (inputElement) {
      inputElement.focus();
    }
  }

  return true;
}

// Initialize form when DOM is loaded
document.addEventListener('DOMContentLoaded', async function () {
  console.log('Initializing SYS.700CPK configurator...');

  const form = document.getElementById('sys-700cpk-form');
  if (!form) {
    console.error('Form element not found');
    return;
  }

  // Initialize form state from URL parameters if present
  const urlParams = new URLSearchParams(window.location.search);
  for (const [key, value] of urlParams.entries()) {
    if (fields[key]) {
      formState[key] = value;
    }
  }

  // Initial render
  await renderForm();

  // Update display fields after initial render
  await updateDisplayFields();

  // Handle form submission
  form.addEventListener('submit', async function (event) {
    event.preventDefault();

    // Validate all visible fields
    const visibleFields = document.querySelectorAll('.field-container:not([style*="display: none"])');
    let isValid = true;

    for (const fieldContainer of visibleFields) {
      const fieldName = fieldContainer.id.replace('-container', '');
      const fieldDef = fields[fieldName];
      const input = fieldContainer.querySelector('input, select');

      if (input) {
        const value = input.type === 'checkbox' ? input.checked : input.value;
        const validation = validateField(fieldName, value, fieldDef);

        if (!validation.isValid) {
          showFieldError(fieldContainer, validation.message);
          isValid = false;
        } else {
          clearFieldError(fieldContainer);
        }
      }
    }

    if (!isValid) {
      // Scroll to first error
      const firstError = document.querySelector('.field-container.has-error');
      if (firstError) {
        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      return;
    }

    try {
      // Show loading state
      const submitButton = form.querySelector('button[type="submit"]');
      const originalText = submitButton.textContent;
      submitButton.disabled = true;
      submitButton.textContent = 'Processing...';

      // Update URL with form state
      const params = new URLSearchParams();
      for (const [key, value] of Object.entries(formState)) {
        if (value !== undefined && value !== '') {
          params.set(key, value);
        }
      }
      const newUrl = `${window.location.pathname}?${params.toString()}`;
      history.pushState(formState, '', newUrl);

      // Trigger form submission event for external handlers
      const submitEvent = new CustomEvent('configuratorSubmit', {
        detail: { formState: { ...formState } }
      });
      document.dispatchEvent(submitEvent);

      // Reset button state
      submitButton.disabled = false;
      submitButton.textContent = originalText;

    } catch (error) {
      console.error('Error processing form submission:', error);
      // Show error message to user
      const errorContainer = document.createElement('div');
      errorContainer.className = 'form-error';
      errorContainer.textContent = 'An error occurred while processing your request. Please try again.';
      form.insertBefore(errorContainer, form.firstChild);
    }
  });

  // Handle field changes using event delegation
  form.addEventListener('change', function (event) {
    // Skip if not a form control
    if (!event.target.matches('select, input')) return;

    var fieldName = event.target.name;
    var value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    var lastValue = lastValues.get(fieldName);

    console.debug('Form field change:', {
      field: fieldName,
      oldValue: lastValue,
      newValue: value
    });

    // Check if value actually changed
    if (JSON.stringify(lastValue) === JSON.stringify(value)) {
      console.debug('🔄 Skipping update for ' + fieldName + ' - value unchanged');
      return;
    }

    // Queue update and log
    queueFormUpdate(fieldName, value);
    console.debug('🔄 Queued update for ' + fieldName, {
      oldValue: lastValue,
      newValue: value,
      pendingUpdates: pendingUpdates.size
    });

    console.log('🔄 Field change:', { field: fieldName, value: value });

    // Queue the form update
    queueFormUpdate(fieldName, value);
  });
});
