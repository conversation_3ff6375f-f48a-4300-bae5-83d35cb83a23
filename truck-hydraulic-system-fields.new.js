// truck-hydraulic-system-fields.js
// Field definitions and dynamic logic for the SYS-700CPK Configurator

import { evaluateMatrixValue } from './matrix-loader.js';

// Cache for engine options
const engineOptionsCache = (function () {
    const cache = {};
    return {
        getCacheKey: function (formState) {
            return [
                formState.Make,
                formState.Vehicle_Type,
                formState.Year,
                formState.Application
            ].join('|');
        },
        get: function (formState) {
            return cache[this.getCacheKey(formState)];
        },
        set: function (formState, options) {
            cache[this.getCacheKey(formState)] = options;
        },
        clear: function () {
            cache = {};
        }
    };
})();

// Field option caching
const optionsCache = {
    _data: {},
    _getCacheKey: function (fieldName, formState) {
        let stateStr = '';
        // Only include relevant state fields in cache key
        const relevantFields = {
            Engine: ['Make', 'Vehicle_Type', 'Year', 'Application'],
            Pump: ['Engine', 'Year', 'Vehicle_Type', 'Application']
        };
        if (relevantFields[fieldName]) {
            stateStr = relevantFields[fieldName].map(function (key) {
                return formState[key];
            }).join('|');
        }
        return fieldName + ':' + stateStr;
    },
    get: function (fieldName, formState) {
        const key = this._getCacheKey(fieldName, formState);
        return this._data[key];
    },
    set: function (fieldName, formState, options) {
        const key = this._getCacheKey(fieldName, formState);
        this._data[key] = options;
        console.debug('Cached options for ' + fieldName + ':', {
            key: key,
            optionsCount: options.length
        });
    },
    clear: function (fieldName) {
        if (fieldName) {
            // Clear only cache entries for this field
            const prefix = fieldName + ':';
            Object.keys(this._data).forEach(function (key) {
                if (key.startsWith(prefix)) {
                    delete this._data[key];
                }
            }, this);
        } else {
            this._data = {};
        }
    }
};

// Helper functions for group normalization
function cleanGroup(group) {
    if (group == null) return null;
    const str = group.toString().trim();
    // Remove any spaces and special characters but keep letters and numbers
    const cleaned = str.replace(/[^a-zA-Z0-9]/g, '');
    // Then extract just the numbers from the end of the string
    const numbers = cleaned.match(/\d+$/);
    return numbers ? numbers[0] : cleaned;
}

function padGroup(group) {
    if (group == null) return null;
    const str = group.toString().trim();
    if (str.length < 3) {
        return str.padStart(3, '0');
    }
    return str;
}

function normalizeVehicleType(type) {
    if (type === 'TRUCK') return 'OT';
    return type;
}

function normalizeApplicationType(type) {
    if (type === 'AG') return 'B';
    return 'C';
}

// Utility function for parsing pump options from XML
function parsePumpOptions(xmlText, pumpGroups) {
    if (!xmlText) return [];

    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlText, "text/xml");
    const pumpOptions = [];

    // Convert comma-separated string to array if needed
    const groupsToCheck = typeof pumpGroups === 'string' ? pumpGroups.split(',') :
        Array.isArray(pumpGroups) ? pumpGroups : [pumpGroups];

    xmlDoc.querySelectorAll('Value').forEach(value => {
        const code = value.querySelector('Property[Name="Value"]')?.textContent;
        const description = value.querySelector('Property[Name="Description"]')?.textContent;
        const groups = Array.from(value.querySelectorAll('Group')).map(g => g.getAttribute('Name'));

        if (code && description) {
            // Check if pump belongs to any of the allowed groups
            const shouldInclude = !pumpGroups || groupsToCheck.some(group => groups.includes(group));

            if (shouldInclude) {
                // Ensure consistent value format using XGL notation
                const normalizedCode = code.endsWith('GL') ? code : `${code}GL`;
                pumpOptions.push({
                    value: normalizedCode,
                    label: description.includes('Gal') ? description : `${code} Gal Pump`
                });
            }
        }
    });

    // Sort by pump size numerically
    return pumpOptions.sort((a, b) => {
        const sizeA = parseInt(a.value.replace(/[^\d]/g, ''));
        const sizeB = parseInt(b.value.replace(/[^\d]/g, ''));
        return sizeA - sizeB;
    });
}

// Define all field configurations
export const fields = {
    Application: {
        label: "Application",
        type: "select",
        required: true,
        options: [
            { value: "AG", label: "Agriculture" },
            { value: "COM", label: "Commercial Application" },
            { value: "OTHER", label: "Other Application" },
            { value: "WRK", label: "Wrecker" }
        ]
    },

    AGKitLessMiniPack: {
        label: "Less AG Mini Pack?",
        type: "boolean",
        required: false,
        condition: function (formState) { return formState.Application === 'AG'; }
    },

    Make: {
        label: "Select Make of Vehicle",
        type: "select",
        required: true,
        condition: function (formState) { return formState.Application && formState.Application.length > 0; },
        async getOptions(formState) {
            console.log('Getting Make options for Application:', formState.Application);

            try {
                const response = await fetch('./data/Option_Lists/OptionList_Make.xml');
                if (!response.ok) throw new Error('Failed to load Make options');

                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");

                const makeOptions = [];
                xmlDoc.querySelectorAll('Value').forEach(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;
                    const groups = Array.from(value.querySelectorAll('Group')).map(g => g.getAttribute('Name'));

                    if (code && description) {
                        const shouldInclude = formState.Application === 'AG' ?
                            groups.includes('B') :
                            groups.includes('O');

                        if (shouldInclude) {
                            makeOptions.push({ value: code, label: description });
                        }
                    }
                });

                makeOptions.sort((a, b) => a.label.localeCompare(b.label));
                console.log('Make options loaded:', makeOptions);
                return makeOptions;
            } catch (error) {
                console.error('Error loading Make options:', error);
                // Fallback options
                const fallbackOptions = [
                    { value: "FD", label: "Ford" },
                    { value: "DG", label: "Dodge/Ram" },
                    { value: "GM", label: "Chevrolet/GMC" },
                    { value: "FL", label: "Freightliner" }
                ];

                return formState.Application === 'AG' ?
                    fallbackOptions.filter(opt => ['FD', 'DG', 'GM'].includes(opt.value)) :
                    fallbackOptions;
            }
        }
    },

    Adapter: {
        label: "Adapter",
        type: "display",
        required: true,
        condition: (formState) => {
            console.debug('Adapter field condition check:', {
                make: formState.Make,
                pump: formState.Pump,
                kit: formState.Kit,
                result: Boolean(formState.Make && formState.Pump && formState.Kit)
            });
            return formState.Make && formState.Pump && formState.Kit;
        },
        getValue: async function (formState) {
            // Define adapter mappings
            const ADAPTER_MAPPINGS = {
                'FD': { // Ford
                    '700607': { // Specific kit mapping
                        '9GL': "03-51261; S=16HB-16MB90; P=10MJ-12MB45",
                        '12GL': "03-51261-12; S=16HB-16MB90; P=12MJ-12MB45",
                        default: "03-51261; S=16HB-16MB90; P=10MJ-12MB45"
                    },
                    default: { // Default Ford mapping
                        '9GL': "03-51261; S=16HB-16MB90; P=10MJ-12MB45",
                        '12GL': "03-51261-12; S=16HB-16MB90; P=12MJ-12MB45",
                        default: "03-51261; S=16HB-16MB90; P=10MJ-12MB45"
                    }
                },
                'DG': { // Dodge/Ram
                    default: {
                        '9GL': "03-1920; S=16HB-16MB90; P=10MJ-12MB90",
                        '12GL': "03-1920-12; S=16HB-16MB90; P=12MJ-12MB90",
                        default: "03-1920; S=16HB-16MB90; P=10MJ-12MB90"
                    }
                },
                'FL': { // Freightliner
                    default: "03-6923; S=20MB90; P=12MJ-12MB90"
                },
                'GM': { // GM/Chevy
                    default: "03-51261; S=16HB-16MB90; P=10MJ-12MB45"
                }
            };

            // Default adapter to use if nothing else matches
            const DEFAULT_ADAPTER = "03-51261; S=16HB-16MB90; P=10MJ-12MB45";

            try {
                // The Make field should already give us the correct code (FD, DG, GM, FL)
                const normalizedMake = formState.Make;
                const normalizedPump = formState.Pump?.replace(/HP$/, '');

                console.debug('Getting adapter for:', {
                    make: formState.Make,
                    kit: formState.Kit,
                    pump: normalizedPump,
                    state: {
                        haveMake: Boolean(formState.Make),
                        haveKit: Boolean(formState.Kit),
                        havePump: Boolean(formState.Pump),
                        makeType: typeof formState.Make,
                        kitType: typeof formState.Kit,
                        pumpType: typeof formState.Pump
                    }
                });

                // Get make-specific adapters or use default
                const makeAdapters = ADAPTER_MAPPINGS[normalizedMake];
                console.debug('Adapter mapping lookup:', {
                    make: normalizedMake,
                    foundAdapters: Boolean(makeAdapters),
                    hasKitMapping: makeAdapters && makeAdapters[formState.Kit],
                    availableMappings: {
                        makes: Object.keys(ADAPTER_MAPPINGS),
                        kitsForMake: makeAdapters ? Object.keys(makeAdapters) : []
                    }
                });

                if (!makeAdapters) {
                    console.debug('No adapters found for make:', formState.Make);
                    return DEFAULT_ADAPTER;
                }

                // Check for kit-specific mapping first
                if (makeAdapters[formState.Kit]) {
                    console.debug('Found kit-specific adapter mapping:', {
                        kit: formState.Kit,
                        mapping: makeAdapters[formState.Kit]
                    });

                    const kitAdapters = makeAdapters[formState.Kit];
                    // If kit adapters is a string, it's a direct adapter spec
                    if (typeof kitAdapters === 'string') {
                        return kitAdapters;
                    }
                    // Otherwise look up by pump size or use kit default
                    const adapter = kitAdapters[normalizedPump] || kitAdapters.default;
                    if (adapter) {
                        console.debug('Using kit-specific adapter:', {
                            kit: formState.Kit,
                            pump: normalizedPump,
                            adapter: adapter
                        });
                        return adapter;
                    }
                }

                // Fall back to make's default adapters
                const defaultAdapters = makeAdapters.default;
                if (typeof defaultAdapters === 'string') {
                    return defaultAdapters;
                }

                // Get pump-specific adapter or use make's default
                const adapter = defaultAdapters[normalizedPump] || defaultAdapters.default || DEFAULT_ADAPTER;

                console.debug('Adapter lookup result:', {
                    originalMake: formState.Make,
                    normalizedMake,
                    kit: formState.Kit,
                    pump: normalizedPump,
                    kitAdapters: makeAdapters[formState.Kit],
                    defaultAdapters: makeAdapters.default,
                    result: adapter
                });

                return adapter;
            } catch (error) {
                console.error('Error in adapter lookup:', error);
                return DEFAULT_ADAPTER;
            }
        }
    }
};

// Get field groups for display organization
export function getFieldGroups() {
    return {
        truckSelections: {
            title: "Truck Selections",
            fields: ['Application', 'AGKitLessMiniPack', 'Make', 'Macfit', 'Year', 'Vehicle_Type', 'Engine', 'Pump', 'Kit', 'Clutch', 'Adapter']
        },
        agQuestions: {
            title: "AG Questions",
            fields: ['Product', 'Valve', 'Cab', 'Chassis', 'ACC_Hose', 'Aux_Hoses', 'Control', 'Harness'],
            condition: function (formState) { return formState.Application === 'AG'; }
        },
        commercialQuestions: {
            title: "Commercial Questions",
            fields: ['Product', 'Cab', 'Chassis', 'Control', 'Harness'],
            condition: function (formState) { return formState.Application === 'COM'; }
        }
    };
}

// Helper function to determine if a field should be visible
export function isFieldVisible(fieldName, formState) {
    const field = fields[fieldName];
    if (!field) return false;

    if (field.condition) {
        return field.condition(formState);
    }

    return true;
}

// Export other helper functions
export { parsePumpOptions };
