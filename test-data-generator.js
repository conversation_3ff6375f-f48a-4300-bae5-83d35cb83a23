// Test Data Generator for Harper Configurators
// Supports both Truck Hydraulic System (SYS.700CPK) and Butler Manufacturing Configurators

const generateTestData = (configuratorType = 'hydraulic', simpleMode = true) => {
    // Base test data structures
    const data = {
        vehicles: [
            {
                vehicle_id_PK: "veh_001",
                make: "Ford",
                year: "2024",
                vehicle_type: "F550",
                engine: "Ford 6.7L Diesel",
                application: "Agriculture",
                macfit: false
            },
            {
                vehicle_id_PK: "veh_002",
                make: "Freightliner",
                year: "2024",
                vehicle_type: "M2",
                engine: "Freightliner M2 Cummins",
                application: "Commercial",
                macfit: false
            }
        ],

        configurations: [
            {
                config_id_PK: "conf_001",
                vehicle_id_FK: "veh_001",
                customer_code: "CUST_A",
                first_price: 5000.00,
                total_price: 7500.00,
                created_date: "2025-07-14T10:00:00Z",
                status: "draft"
            },
            {
                config_id_PK: "conf_002",
                vehicle_id_FK: "veh_001",
                customer_code: "CUST_B",
                first_price: 6000.00,
                total_price: 8500.00,
                created_date: "2025-07-14T11:00:00Z",
                status: "submitted"
            }
        ],

        config_line_items: [
            {
                line_item_id_PK: "cli_001",
                config_id_FK: "conf_001",
                component_type: "Kit",
                part_number: "700001",
                quantity: 1,
                unit_price: 2500.00,
                line_total: 2500.00
            },
            {
                line_item_id_PK: "cli_002",
                config_id_FK: "conf_001",
                component_type: "Pump",
                part_number: "02-2909",
                quantity: 1,
                unit_price: 1500.00,
                line_total: 1500.00
            }
        ],

        // Common Components
        kits: [
            {
                kit_id_PK: "kit_001",
                kit_part_number: "700001",
                description: "Standard AG Kit",
                base_price: 2500.00,
                kit_group: "AG"
            },
            {
                kit_id_PK: "kit_002",
                kit_part_number: "700336",
                description: "Commercial Kit",
                base_price: 3000.00,
                kit_group: "COM"
            }
        ],

        pumps: [
            {
                pump_id_PK: "pump_001",
                pump_part_number: "02-2909",
                pump_size: "5GL",
                pump_type: "Standard",
                base_price: 1500.00,
                pump_group: "STD"
            },
            {
                pump_id_PK: "pump_002",
                pump_part_number: "02-0500",
                pump_size: "7GL",
                pump_type: "Heavy Duty",
                base_price: 2000.00,
                pump_group: "HD"
            }
        ]
    };

    // Butler-specific data
    const butlerData = {
        beds: [
            {
                bed_id_PK: "bed_001",
                model: "Standard Flatbed",
                type: "Flatbed",
                length: "8ft"
            },
            {
                bed_id_PK: "bed_002",
                model: "Heavy Duty Platform",
                type: "Platform",
                length: "12ft"
            }
        ],

        toolboxes: [
            {
                toolbox_id_PK: "tool_001",
                position: "left",
                size: "standard",
                material: "aluminum"
            },
            {
                toolbox_id_PK: "tool_002",
                position: "across-bed",
                size: "large",
                material: "steel"
            }
        ],

        bed_mounts: [
            {
                mount_id_PK: "mount_001",
                bed_id_FK: "bed_001",
                vehicle_make: "Ford",
                vehicle_year: "2024",
                mount_kit: "MK-001"
            },
            {
                mount_id_PK: "mount_002",
                bed_id_FK: "bed_002",
                vehicle_make: "Freightliner",
                vehicle_year: "2024",
                mount_kit: "MK-002"
            }
        ]
    };

    // AG-specific data
    const hydraulicData = {
        ag_products: [
            {
                ag_product_id_PK: "ag_001",
                product_type: "Bale Bed",
                product_series: "660",
                valve_required: true,
                description: "660 Series Bale Bed"
            },
            {
                ag_product_id_PK: "ag_002",
                product_type: "Hay Processor",
                product_series: "800-900",
                valve_required: true,
                description: "800 Series Processor"
            }
        ],

        valves: [
            {
                valve_id_PK: "valve_001",
                valve_part_number: "05-7050",
                valve_type: "Standard",
                base_price: 500.00,
                for_660_series: true
            },
            {
                valve_id_PK: "valve_002",
                valve_part_number: "05-7102",
                valve_type: "Heavy Duty",
                base_price: 750.00,
                for_660_series: false
            }
        ]
    };

    // Return data based on configurator type
    const result = { ...data };

    if (configuratorType === 'butler') {
        Object.assign(result, butlerData);
    } else { // hydraulic
        Object.assign(result, hydraulicData);
    }

    // Convert to different formats if needed
    const toSQL = () => {
        // SQL generation logic here
        let sql = '';
        // ... SQL generation code ...
        return sql;
    };

    const toJSON = () => {
        return JSON.stringify(result, null, 2);
    };

    // Return data with format converters
    return {
        data: result,
        toSQL,
        toJSON
    };
};

// Example matrix data for compatibility checks
const matrixData = {
    vt_filter_matrix: [
        {
            filter_id_PK: "filter_001",
            make: "Ford",
            year: "2024",
            vehicle_type_result: "F550,F600,F650"
        },
        {
            filter_id_PK: "filter_002",
            make: "Freightliner",
            year: "2024",
            vehicle_type_result: "M2,M2-106"
        }
    ],

    engine_group_matrix: [
        {
            engine_group_id_PK: "eng_group_001",
            make: "Ford",
            vehicle_type: "F550",
            year: "2024",
            engine_groups: "6.7L_DIESEL"
        },
        {
            engine_group_id_PK: "eng_group_002",
            make: "Freightliner",
            vehicle_type: "M2",
            year: "2024",
            engine_groups: "M2_CUMMINS"
        }
    ]
};

export { generateTestData, matrixData };
