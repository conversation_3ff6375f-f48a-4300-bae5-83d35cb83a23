<IncludeRuleset Namespace="SYS" Name="Butler_Manufacturing" EntityId="fc85f3bf-e2cb-4473-99a8-a4f2b8832ef8">
  <Tags />
  <ComponentAttributes />
  <RuleTree Type="IncludeRuleset">
    <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Begin Manufacturing Rules" ConditionExpression="" Enabled="true">
      <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Variable" Caption="Set Smart Part" ConditionExpression="" Enabled="true">
        <vars>
          <var name="ConfigurationCode">
            <value>="SMART" + Bed</value>
          </var>
        </vars>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Component" Caption="Manufacturing Component" ConditionExpression="" Enabled="true">
        <Parameters>
          <Parameter Key="Component ID" Value="=sys.partnumber" Description="Unique ID for this Job" IsRequired="true" IsSystem="true" DataType="String" />
          <Parameter Key="Parent Component ID" Value="" Description="Parent BOM Component ID (Leave Blank for top level)" IsRequired="false" IsSystem="true" DataType="String" />
          <Parameter Key="Print Code" Value="I" Description="I for Internal, E for External" IsRequired="true" IsSystem="true" DataType="String" />
          <Parameter Key="Config Component Sequence" Value="=ToString(ComponentSequence)" Description="" IsRequired="false" IsSystem="true" DataType="String" />
          <Parameter Key="Part Number" Value="=sys.partnumber" Description="Required for Sub Jobs.  Part Number must exist in SyteLine." IsRequired="false" IsSystem="true" DataType="String" />
          <Parameter Key="Quantity" Value="=input.quantity" Description="Required for SubJobs" IsRequired="false" IsSystem="true" DataType="Number" />
          <Parameter Key="Operation No" Value="" Description="Valid Operation No From Parent Job/Not Required for Top Level Job" IsRequired="false" IsSystem="true" DataType="String" />
          <Parameter Key="job.contains_tax_free_matl" Value="=null" Description="Syteline Schema Mapping to job. contains_tax_free_matl field." IsRequired="false" IsSystem="false" DataType="Number" />
          <Parameter Key="job.description" Value="=Bed.Value.Description" Description="Syteline Schema Mapping to job. description field" IsRequired="false" IsSystem="false" DataType="String" />
          <Parameter Key="job.export_type" Value="" Description="Syteline Schema Mapping to job. export_type field." IsRequired="false" IsSystem="false" DataType="String" />
          <Parameter Key="job.midnight_of_job_sch_compdate" Value="" Description="Syteline Schema Mapping to job. midnight_of_job_sch_compdate field." IsRequired="false" IsSystem="false" DataType="String" />
          <Parameter Key="job.midnight_of_job_sch_end_date" Value="" Description="Syteline Schema Mapping to job. midnight_of_job_sch_end_date field." IsRequired="false" IsSystem="false" DataType="String" />
          <Parameter Key="job.rcpt_rqmt" Value="" Description="Syteline Schema Mapping to job. rcpt_rqmt field." IsRequired="false" IsSystem="false" DataType="String" />
          <Parameter Key="job.rework" Value="=null" Description="Syteline Schema Mapping to job. rework field." IsRequired="false" IsSystem="false" DataType="Number" />
          <Parameter Key="job.scheduled" Value="=null" Description="Syteline Schema Mapping to job. scheduled field." IsRequired="false" IsSystem="false" DataType="Number" />
          <Parameter Key="job.unlinked_xref" Value="=null" Description="Syteline Schema Mapping to job. unlinked_xref field." IsRequired="false" IsSystem="false" DataType="Number" />
          <Parameter Key="job.whse" Value="" Description="Syteline Schema Mapping to job. whse field." IsRequired="false" IsSystem="false" DataType="String" />
          <Parameter Key="job.text" Value="" Description="Syteline Schema Mapping to job. text field." IsRequired="false" IsSystem="false" DataType="String" />
          <Parameter Key="ConfigurationCode" Value="=ConfigurationCode" Description="Syteline Schema Mapping to IPN field." IsRequired="false" IsSystem="true" DataType="String" />
          <Parameter Key="co.pricecode" Value="=if(CustomerOrder=true,CustCode,NULL)" Description="" IsRequired="false" IsSystem="false" DataType="String" />
          <Parameter Key="co.inv_freq" Value="=&quot;D&quot;" Description="" IsRequired="false" IsSystem="false" DataType="String" />
          <Parameter Key="Plant ID" Value="=sys.namespace" Description="" IsRequired="false" IsSystem="false" DataType="String" />
        </Parameters>
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Operation" Caption="Operation 10 - Welding" ConditionExpression="" Enabled="true">
          <Comment>Welding time at top level.  Currently this time is in the deck.  It needs to be moved from to deck to this location</Comment>
          <Parameters>
            <Parameter Key="Component or Material ID" Value="=sys.partnumber" Description="Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="Operation No" Value="=&quot;10&quot;" Description="Required" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="Operation Name" Value="" Description="Operation Name or Work Center ID" IsRequired="false" IsSystem="true" DataType="String" />
            <Parameter Key="Operation Description" Value="" Description="Description of the Operation" IsRequired="false" IsSystem="true" DataType="String" />
            <Parameter Key="Work Center ID" Value="200" Description="Valid Work Center ID.  Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="Print Code" Value="I" Description="I for Internal, E for External" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="jrt_sch.run_lbr_hrs" Value="=if(BedType.Value=&quot;A&quot;,8.7,8.95)" Description="Used for &quot;H&quot; Hours/Piece --- Enter Hours" IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.pcs_per_lbr_hr" Value="=0" Description="Used for &quot;P&quot; Piece/Hour --- Enter Pieces" IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.bflush_type" Value="" Description="Syteline Schema Mapping to jobroute. bflush_type field." IsRequired="false" IsSystem="false" DataType="String" />
            <Parameter Key="jobroute.cntrl_point" Value="=null" Description="Syteline Schema Mapping to jobroute. cntrl_point field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.efficiency" Value="=null" Description="Syteline Schema Mapping to jobroute. efficiency field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.fixovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. fixovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.fovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. fovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.run_basis_lbr" Value="H" Description="P = Pieces/Hour or H = Hours/Piece" IsRequired="false" IsSystem="false" DataType="String" />
            <Parameter Key="jobroute.run_basis_mch" Value="" Description="Syteline Schema Mapping to jobroute. run_basis_mch field." IsRequired="false" IsSystem="false" DataType="String" />
            <Parameter Key="jobroute.run_rate_lbr" Value="=null" Description="Syteline Schema Mapping to jobroute. run_rate_lbr field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.setup_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. setup_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.varovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. varovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.vovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. vovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.finish_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. finish_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.move_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. move_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.offset_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. offset_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.pcs_per_mch_hr" Value="=null" Description="Syteline Schema Mapping to jrt_sch. pcs_per_mch_hr field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.queue_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. queue_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.run_mch_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. run_mch_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.sched_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. sched_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.setup_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. setup_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.whenrule" Value="=null" Description="Syteline Schema Mapping to jrt_sch. whenrule field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.text" Value="" Description="Syteline Schema Mapping to jobroute. text field." IsRequired="false" IsSystem="false" DataType="String" />
            <Parameter Key="coitem.text" Value="" Description="Add information here will create note on bed " IsRequired="false" IsSystem="false" DataType="String" />
          </Parameters>
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="BedLength&lt;=86" ConditionExpression="=BedLength&lt;=86" Enabled="true">
            <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Material Rule for Manufacturing - Dodge TB Mount" ConditionExpression="=TBDodge=True" Enabled="true">
              <RuleData>
                <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                <Column Key="Operation No" ExpressionType="Expression" />
                <Column Key="Plant ID" ExpressionType="Expression" />
                <Column Key="Part Number" ExpressionType="Expression" />
                <Column Key="Quantity" ExpressionType="Expression" />
                <Value>
                  <Column Value="50" />
                  <Column Value="=sys.partnumber" />
                  <Column Value="=&quot;10&quot;" />
                  <Column Value="" />
                  <Column Value="=TBDodge_sel" />
                  <Column Value="2" />
                </Value>
              </RuleData>
            </Rule>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Material Rule for Manufacturing - Bed Weld" ConditionExpression="" Enabled="true">
            <RuleData>
              <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
              <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
              <Column Key="Operation No" ExpressionType="Expression" />
              <Column Key="Plant ID" ExpressionType="Expression" />
              <Column Key="Part Number" ExpressionType="Expression" />
              <Column Key="Quantity" ExpressionType="Expression" />
              <Value>
                <Column Value="10" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;10&quot;" />
                <Column Value="" />
                <Column Value="=BedFeature_sel" />
                <Column Value="1" />
              </Value>
              <Value>
                <Column Value="20" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;10&quot;" />
                <Column Value="" />
                <Column Value="=Headache_sel" />
                <Column Value="1" />
              </Value>
              <Value>
                <Column Value="30" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;10&quot;" />
                <Column Value="" />
                <Column Value="=Tailboard_sel" />
                <Column Value="1" />
              </Value>
            </RuleData>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Material Rule for Arm Crosstube" ConditionExpression="=BedType=&quot;A&quot;" Enabled="true">
            <RuleData>
              <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
              <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
              <Column Key="Operation No" ExpressionType="Expression" />
              <Column Key="Plant ID" ExpressionType="Expression" />
              <Column Key="Part Number" ExpressionType="Expression" />
              <Column Key="Quantity" ExpressionType="Expression" />
              <Value>
                <Column Value="36" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;10&quot;" />
                <Column Value="" />
                <Column Value="=Crosstube_sel" />
                <Column Value="1" />
              </Value>
            </RuleData>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Material Rule for Manufacturing - 5th wheel" ConditionExpression="=BedFeature!=&quot;TT&quot; &amp; BedFeature!=&quot;TS&quot;" Enabled="false">
            <RuleData>
              <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
              <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
              <Column Key="Operation No" ExpressionType="Expression" />
              <Column Key="Plant ID" ExpressionType="Expression" />
              <Column Key="Part Number" ExpressionType="Expression" />
              <Column Key="Quantity" ExpressionType="Expression" />
              <Value>
                <Column Value="40" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;10&quot;" />
                <Column Value="" />
                <Column Value="=BallType_sel" />
                <Column Value="1" />
              </Value>
            </RuleData>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Material Rule for Manufacturing - Rub Rails" ConditionExpression="=RubRails=True" Enabled="true">
            <RuleData>
              <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
              <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
              <Column Key="Operation No" ExpressionType="Expression" />
              <Column Key="Plant ID" ExpressionType="Expression" />
              <Column Key="Part Number" ExpressionType="Expression" />
              <Column Key="Quantity" ExpressionType="Expression" />
              <Value>
                <Column Value="51" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;10&quot;" />
                <Column Value="" />
                <Column Value="=RubRails_sel" />
                <Column Value="1" />
              </Value>
            </RuleData>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Material Rule for Manufacturing - Mount Tabs" ConditionExpression="=MntTabs=True and WeldTab=True" Enabled="true">
            <RuleData>
              <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
              <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
              <Column Key="Operation No" ExpressionType="Expression" />
              <Column Key="Plant ID" ExpressionType="Expression" />
              <Column Key="Part Number" ExpressionType="Expression" />
              <Column Key="Quantity" ExpressionType="Expression" />
              <Value>
                <Column Value="60" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;10&quot;" />
                <Column Value="" />
                <Column Value="=MountTab_sel" />
                <Column Value="1" />
              </Value>
            </RuleData>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Weld on brackets for rear toolboxes Spike&amp;Regular" ConditionExpression="=RTBrackets=True" Enabled="true">
            <RuleData>
              <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
              <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
              <Column Key="Operation No" ExpressionType="Expression" />
              <Column Key="Plant ID" ExpressionType="Expression" />
              <Column Key="Part Number" ExpressionType="Expression" />
              <Column Key="Quantity" ExpressionType="Expression" />
              <Value>
                <Column Value="65" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;10&quot;" />
                <Column Value="" />
                <Column Value="=RearTBMount" />
                <Column Value="2" />
              </Value>
            </RuleData>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Operation" Caption="Operation 11-hours for skirting weld" ConditionExpression="=BedFeature=&quot;SK&quot; or BedFeature=&quot;SF&quot; or BedFeature=&quot;SW&quot; or BedFeature=&quot;US&quot;" Enabled="true">
            <Parameters>
              <Parameter Key="Component or Material ID" Value="=sys.partnumber" Description="Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
              <Parameter Key="Operation No" Value="=&quot;11&quot;" Description="Required" IsRequired="true" IsSystem="true" DataType="String" />
              <Parameter Key="Operation Name" Value="" Description="Operation Name or Work Center ID" IsRequired="false" IsSystem="false" DataType="String" />
              <Parameter Key="Operation Description" Value="" Description="Description of the Operation" IsRequired="false" IsSystem="false" DataType="String" />
              <Parameter Key="Work Center ID" Value="200" Description="Valid Work Center ID.  Parent Material or Component" IsRequired="true" IsSystem="false" DataType="String" />
              <Parameter Key="Print Code" Value="I" Description="I for Internal, E for External" IsRequired="true" IsSystem="false" DataType="String" />
              <Parameter Key="jrt_sch.run_lbr_hrs" Value="=5" Description="Used for &quot;H&quot; Hours/Piece --- Enter Hours" IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.pcs_per_lbr_hr" Value="=0" Description="Used for &quot;P&quot; Piece/Hour --- Enter Pieces" IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.bflush_type" Value="" Description="Syteline Schema Mapping to jobroute. bflush_type field." IsRequired="false" IsSystem="false" DataType="String" />
              <Parameter Key="jobroute.cntrl_point" Value="=null" Description="Syteline Schema Mapping to jobroute. cntrl_point field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.efficiency" Value="=null" Description="Syteline Schema Mapping to jobroute. efficiency field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.fixovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. fixovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.fovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. fovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.run_basis_lbr" Value="H" Description="P = Pieces/Hour or H = Hours/Piece" IsRequired="false" IsSystem="false" DataType="String" />
              <Parameter Key="jobroute.run_basis_mch" Value="" Description="Syteline Schema Mapping to jobroute. run_basis_mch field." IsRequired="false" IsSystem="false" DataType="String" />
              <Parameter Key="jobroute.run_rate_lbr" Value="=null" Description="Syteline Schema Mapping to jobroute. run_rate_lbr field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.setup_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. setup_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.varovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. varovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.vovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. vovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.finish_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. finish_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.move_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. move_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.offset_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. offset_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.pcs_per_mch_hr" Value="=null" Description="Syteline Schema Mapping to jrt_sch. pcs_per_mch_hr field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.queue_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. queue_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.run_mch_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. run_mch_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.sched_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. sched_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.setup_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. setup_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.whenrule" Value="=null" Description="Syteline Schema Mapping to jrt_sch. whenrule field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.text" Value="" Description="Syteline Schema Mapping to jobroute. text field." IsRequired="false" IsSystem="false" DataType="String" />
              <Parameter Key="coitem.text" Value="" Description="Add information here will create note on bed " IsRequired="false" IsSystem="false" DataType="String" />
            </Parameters>
            <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Material Rule for Manufacturing - Skirts" ConditionExpression="=BedFeature=&quot;SK&quot; or BedFeature=&quot;SF&quot;or BedFeature=&quot;SW&quot; or BedFeature=&quot;US&quot;" Enabled="true">
              <RuleData>
                <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                <Column Key="Operation No" ExpressionType="Expression" />
                <Column Key="Plant ID" ExpressionType="Expression" />
                <Column Key="Part Number" ExpressionType="Expression" />
                <Column Key="Quantity" ExpressionType="Expression" />
                <Value>
                  <Column Value="34" />
                  <Column Value="=sys.partnumber" />
                  <Column Value="=&quot;11&quot;" />
                  <Column Value="" />
                  <Column Value="=LHSkirt" />
                  <Column Value="1" />
                </Value>
                <Value>
                  <Column Value="35" />
                  <Column Value="=sys.partnumber" />
                  <Column Value="=&quot;11&quot;" />
                  <Column Value="" />
                  <Column Value="=RHSkirt" />
                  <Column Value="1" />
                </Value>
              </RuleData>
            </Rule>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Operation" Caption="Operation 15- Painting" ConditionExpression="" Enabled="true">
          <Parameters>
            <Parameter Key="Component or Material ID" Value="=sys.partnumber" Description="Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="Operation No" Value="=&quot;15&quot;" Description="Required" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="Operation Name" Value="" Description="Operation Name or Work Center ID" IsRequired="false" IsSystem="true" DataType="String" />
            <Parameter Key="Operation Description" Value="" Description="Description of the Operation" IsRequired="false" IsSystem="true" DataType="String" />
            <Parameter Key="Work Center ID" Value="300" Description="Valid Work Center ID.  Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="Print Code" Value="I" Description="I for Internal, E for External" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="jrt_sch.run_lbr_hrs" Value="=2" Description="Used for &quot;H&quot; Hours/Piece --- Enter Hours" IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.pcs_per_lbr_hr" Value="=0" Description="Used for &quot;P&quot; Piece/Hour --- Enter Pieces" IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.bflush_type" Value="" Description="Syteline Schema Mapping to jobroute. bflush_type field." IsRequired="false" IsSystem="false" DataType="String" />
            <Parameter Key="jobroute.cntrl_point" Value="=1" Description="Syteline Schema Mapping to jobroute. cntrl_point field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.efficiency" Value="=null" Description="Syteline Schema Mapping to jobroute. efficiency field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.fixovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. fixovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.fovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. fovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.run_basis_lbr" Value="H" Description="P = Pieces/Hour or H = Hours/Piece" IsRequired="false" IsSystem="false" DataType="String" />
            <Parameter Key="jobroute.run_basis_mch" Value="" Description="Syteline Schema Mapping to jobroute. run_basis_mch field." IsRequired="false" IsSystem="false" DataType="String" />
            <Parameter Key="jobroute.run_rate_lbr" Value="=null" Description="Syteline Schema Mapping to jobroute. run_rate_lbr field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.setup_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. setup_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.varovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. varovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.vovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. vovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.finish_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. finish_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.move_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. move_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.offset_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. offset_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.pcs_per_mch_hr" Value="=null" Description="Syteline Schema Mapping to jrt_sch. pcs_per_mch_hr field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.queue_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. queue_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.run_mch_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. run_mch_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.sched_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. sched_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.setup_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. setup_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.whenrule" Value="=null" Description="Syteline Schema Mapping to jrt_sch. whenrule field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.text" Value="" Description="Syteline Schema Mapping to jobroute. text field." IsRequired="false" IsSystem="false" DataType="String" />
          </Parameters>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Operation" Caption="Operation 20 - Assembly For Beds" ConditionExpression="" Enabled="true">
          <Comment>Assembly time at top level.  Currently this time is in the deck.  It needs to be moved from to deck to this location</Comment>
          <Parameters>
            <Parameter Key="Component or Material ID" Value="=sys.partnumber" Description="Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="Operation No" Value="=&quot;20&quot;" Description="Required" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="Operation Name" Value="" Description="Operation Name or Work Center ID" IsRequired="false" IsSystem="true" DataType="String" />
            <Parameter Key="Operation Description" Value="" Description="Description of the Operation" IsRequired="false" IsSystem="true" DataType="String" />
            <Parameter Key="Work Center ID" Value="400" Description="Valid Work Center ID.  Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="Print Code" Value="I" Description="I for Internal, E for External" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="jrt_sch.run_lbr_hrs" Value="=1+.33" Description="Used for &quot;H&quot; Hours/Piece --- Enter Hours" IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.pcs_per_lbr_hr" Value="=0" Description="Used for &quot;P&quot; Piece/Hour --- Enter Pieces" IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.bflush_type" Value="" Description="Syteline Schema Mapping to jobroute. bflush_type field." IsRequired="false" IsSystem="false" DataType="String" />
            <Parameter Key="jobroute.cntrl_point" Value="=null" Description="Syteline Schema Mapping to jobroute. cntrl_point field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.efficiency" Value="=null" Description="Syteline Schema Mapping to jobroute. efficiency field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.fixovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. fixovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.fovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. fovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.run_basis_lbr" Value="H" Description="P = Pieces/Hour or H = Hours/Piece" IsRequired="false" IsSystem="false" DataType="String" />
            <Parameter Key="jobroute.run_basis_mch" Value="" Description="Syteline Schema Mapping to jobroute. run_basis_mch field." IsRequired="false" IsSystem="false" DataType="String" />
            <Parameter Key="jobroute.run_rate_lbr" Value="=null" Description="Syteline Schema Mapping to jobroute. run_rate_lbr field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.setup_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. setup_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.varovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. varovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.vovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. vovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.finish_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. finish_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.move_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. move_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.offset_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. offset_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.pcs_per_mch_hr" Value="=null" Description="Syteline Schema Mapping to jrt_sch. pcs_per_mch_hr field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.queue_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. queue_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.run_mch_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. run_mch_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.sched_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. sched_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.setup_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. setup_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.whenrule" Value="=null" Description="Syteline Schema Mapping to jrt_sch. whenrule field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.text" Value="" Description="Syteline Schema Mapping to jobroute. text field." IsRequired="false" IsSystem="false" DataType="String" />
          </Parameters>
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Ball" ConditionExpression="=BallType=&quot;R&quot;" Enabled="true">
            <RuleData>
              <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
              <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
              <Column Key="Operation No" ExpressionType="Expression" />
              <Column Key="Plant ID" ExpressionType="Expression" />
              <Column Key="Part Number" ExpressionType="Expression" />
              <Column Key="Quantity" ExpressionType="Expression" />
              <Value>
                <Column Value="70" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;20&quot;" />
                <Column Value="" />
                <Column Value="=Ball" />
                <Column Value="1" />
              </Value>
            </RuleData>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Completeing Group Material" ConditionExpression="" Enabled="true">
            <RuleData>
              <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
              <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
              <Column Key="Operation No" ExpressionType="Expression" />
              <Column Key="Plant ID" ExpressionType="Expression" />
              <Column Key="Part Number" ExpressionType="Expression" />
              <Column Key="Quantity" ExpressionType="Expression" />
              <Value>
                <Column Value="80" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;20&quot;" />
                <Column Value="" />
                <Column Value="=CompletingGroup" />
                <Column Value="1" />
              </Value>
            </RuleData>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="STT Material" ConditionExpression="=Headache=&quot;SL&quot; or Headache=&quot;ML&quot; or Headache=&quot;TL&quot;" Enabled="true">
            <RuleData>
              <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
              <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
              <Column Key="Operation No" ExpressionType="Expression" />
              <Column Key="Plant ID" ExpressionType="Expression" />
              <Column Key="Part Number" ExpressionType="Expression" />
              <Column Key="Quantity" ExpressionType="Expression" />
              <Value>
                <Column Value="84" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;20&quot;" />
                <Column Value="" />
                <Column Value="=HeadacheLights_sel" />
                <Column Value="1" />
              </Value>
            </RuleData>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Tailboard and Clearance Light Pigtails" ConditionExpression="=Wireharness=false" Enabled="false">
            <Comment>Not an option any longer</Comment>
            <RuleData>
              <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
              <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
              <Column Key="Operation No" ExpressionType="Expression" />
              <Column Key="Plant ID" ExpressionType="Expression" />
              <Column Key="Part Number" ExpressionType="Expression" />
              <Column Key="Quantity" ExpressionType="Expression" />
              <Value>
                <Column Value="85" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;20&quot;" />
                <Column Value="" />
                <Column Value="=TC_pigtail_select" />
                <Column Value="1" />
              </Value>
            </RuleData>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Operation" Caption="Operation 24- Harness Hours" ConditionExpression="=Wireharness=true" Enabled="false">
            <Comment>Standard now moved .33 hours to operation 20</Comment>
            <Parameters>
              <Parameter Key="Component or Material ID" Value="=sys.partnumber" Description="Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
              <Parameter Key="Operation No" Value="=&quot;24&quot;" Description="Required" IsRequired="true" IsSystem="true" DataType="String" />
              <Parameter Key="Operation Name" Value="" Description="Operation Name or Work Center ID" IsRequired="false" IsSystem="true" DataType="String" />
              <Parameter Key="Operation Description" Value="" Description="Description of the Operation" IsRequired="false" IsSystem="true" DataType="String" />
              <Parameter Key="Work Center ID" Value="400" Description="Valid Work Center ID.  Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
              <Parameter Key="Print Code" Value="I" Description="I for Internal, E for External" IsRequired="true" IsSystem="true" DataType="String" />
              <Parameter Key="jrt_sch.run_lbr_hrs" Value="=.33" Description="Used for &quot;H&quot; Hours/Piece --- Enter Hours" IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.pcs_per_lbr_hr" Value="=0" Description="Used for &quot;P&quot; Piece/Hour --- Enter Pieces" IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.bflush_type" Value="" Description="Syteline Schema Mapping to jobroute. bflush_type field." IsRequired="false" IsSystem="false" DataType="String" />
              <Parameter Key="jobroute.cntrl_point" Value="=null" Description="Syteline Schema Mapping to jobroute. cntrl_point field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.efficiency" Value="=null" Description="Syteline Schema Mapping to jobroute. efficiency field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.fixovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. fixovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.fovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. fovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.run_basis_lbr" Value="H" Description="P = Pieces/Hour or H = Hours/Piece" IsRequired="false" IsSystem="false" DataType="String" />
              <Parameter Key="jobroute.run_basis_mch" Value="" Description="Syteline Schema Mapping to jobroute. run_basis_mch field." IsRequired="false" IsSystem="false" DataType="String" />
              <Parameter Key="jobroute.run_rate_lbr" Value="=null" Description="Syteline Schema Mapping to jobroute. run_rate_lbr field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.setup_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. setup_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.varovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. varovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.vovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. vovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.finish_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. finish_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.move_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. move_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.offset_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. offset_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.pcs_per_mch_hr" Value="=null" Description="Syteline Schema Mapping to jrt_sch. pcs_per_mch_hr field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.queue_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. queue_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.run_mch_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. run_mch_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.sched_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. sched_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.setup_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. setup_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.whenrule" Value="=null" Description="Syteline Schema Mapping to jrt_sch. whenrule field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.text" Value="" Description="Syteline Schema Mapping to jobroute. text field." IsRequired="false" IsSystem="false" DataType="String" />
            </Parameters>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Front and Rear Harness Material" ConditionExpression="" Enabled="true">
            <Comment>=Wireharness=true  This is standard on all beds. Changed operation number to 20 from 24</Comment>
            <RuleData>
              <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
              <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
              <Column Key="Operation No" ExpressionType="Expression" />
              <Column Key="Plant ID" ExpressionType="Expression" />
              <Column Key="Part Number" ExpressionType="Expression" />
              <Column Key="Quantity" ExpressionType="Expression" />
              <Value>
                <Column Value="86" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;20&quot;" />
                <Column Value="" />
                <Column Value="=FrontHarness_sel" />
                <Column Value="1" />
              </Value>
              <Value>
                <Column Value="87" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;20&quot;" />
                <Column Value="" />
                <Column Value="=RearHarness_sel" />
                <Column Value="1" />
              </Value>
            </RuleData>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Does it have STT Lights" ConditionExpression="=Headache=&quot;SL&quot; or Headache=&quot;ML&quot; or Headache=&quot;TL&quot; or Headache=&quot;XL&quot;" Enabled="true">
            <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="STT Harness Material" ConditionExpression="" Enabled="true">
              <Comment>=Wireharness=True Standard now</Comment>
              <RuleData>
                <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                <Column Key="Operation No" ExpressionType="Expression" />
                <Column Key="Plant ID" ExpressionType="Expression" />
                <Column Key="Part Number" ExpressionType="Expression" />
                <Column Key="Quantity" ExpressionType="Expression" />
                <Value>
                  <Column Value="88" />
                  <Column Value="=sys.partnumber" />
                  <Column Value="=&quot;20&quot;" />
                  <Column Value="" />
                  <Column Value="=STT_sel" />
                  <Column Value="1" />
                </Value>
              </RuleData>
            </Rule>
            <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="STT Pigtail" ConditionExpression="=Wireharness=False" Enabled="false">
              <Comment>Not an option</Comment>
              <RuleData>
                <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                <Column Key="Operation No" ExpressionType="Expression" />
                <Column Key="Plant ID" ExpressionType="Expression" />
                <Column Key="Part Number" ExpressionType="Expression" />
                <Column Key="Quantity" ExpressionType="Expression" />
                <Value>
                  <Column Value="89" />
                  <Column Value="=sys.partnumber" />
                  <Column Value="=&quot;20&quot;" />
                  <Column Value="" />
                  <Column Value="=pigtail_sel" />
                  <Column Value="1" />
                </Value>
              </RuleData>
            </Rule>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Complete Assembly" ConditionExpression="=Assembly_Comp=True" Enabled="false">
            <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Front Toolbox Material" ConditionExpression="=FrontToolbox != &quot;NNNN&quot;" Enabled="true">
              <RuleData>
                <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                <Column Key="Operation No" ExpressionType="Expression" />
                <Column Key="Plant ID" ExpressionType="Expression" />
                <Column Key="Part Number" ExpressionType="Expression" />
                <Column Key="Quantity" ExpressionType="Expression" />
                <Value>
                  <Column Value="9000" />
                  <Column Value="=sys.partnumber" />
                  <Column Value="=&quot;20&quot;" />
                  <Column Value="" />
                  <Column Value="=FrontToolbox_sel" />
                  <Column Value="1" />
                </Value>
              </RuleData>
            </Rule>
            <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Rear Toolbox Material" ConditionExpression="=RearToolbox= true" Enabled="true">
              <RuleData>
                <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                <Column Key="Operation No" ExpressionType="Expression" />
                <Column Key="Plant ID" ExpressionType="Expression" />
                <Column Key="Part Number" ExpressionType="Expression" />
                <Column Key="Quantity" ExpressionType="Expression" />
                <Value>
                  <Column Value="9100" />
                  <Column Value="=sys.partnumber" />
                  <Column Value="=&quot;20&quot;" />
                  <Column Value="" />
                  <Column Value="=RearToolbox_sel" />
                  <Column Value="1" />
                </Value>
              </RuleData>
            </Rule>
            <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Shipping Completeing Grouping" ConditionExpression="=Assembly_Comp=True" Enabled="true">
              <RuleData>
                <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                <Column Key="Operation No" ExpressionType="Expression" />
                <Column Key="Plant ID" ExpressionType="Expression" />
                <Column Key="Part Number" ExpressionType="Expression" />
                <Column Key="Quantity" ExpressionType="Expression" />
                <Value>
                  <Column Value="10500" />
                  <Column Value="=sys.partnumber" />
                  <Column Value="=&quot;20&quot;" />
                  <Column Value="" />
                  <Column Value="=ShipCompleteingGroup" />
                  <Column Value="1" />
                </Value>
              </RuleData>
            </Rule>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Skirt Assembly" ConditionExpression="=BedFeature=&quot;SK&quot; or BedFeature=&quot;SW&quot; or BedFeature=&quot;SF&quot; or BedFeature=&quot;US&quot;" Enabled="true">
            <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Operation" Caption="Operation 25 - Assembly For Beds" ConditionExpression="" Enabled="true">
              <Parameters>
                <Parameter Key="Component or Material ID" Value="=sys.partnumber" Description="Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
                <Parameter Key="Operation No" Value="=&quot;25&quot;" Description="Required" IsRequired="true" IsSystem="true" DataType="String" />
                <Parameter Key="Operation Name" Value="" Description="Operation Name or Work Center ID" IsRequired="false" IsSystem="true" DataType="String" />
                <Parameter Key="Operation Description" Value="" Description="Description of the Operation" IsRequired="false" IsSystem="true" DataType="String" />
                <Parameter Key="Work Center ID" Value="400" Description="Valid Work Center ID.  Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
                <Parameter Key="Print Code" Value="I" Description="I for Internal, E for External" IsRequired="true" IsSystem="true" DataType="String" />
                <Parameter Key="jrt_sch.run_lbr_hrs" Value="=1.5" Description="Used for &quot;H&quot; Hours/Piece --- Enter Hours" IsRequired="false" IsSystem="false" DataType="Number" />
                <Parameter Key="jrt_sch.pcs_per_lbr_hr" Value="=0" Description="Used for &quot;P&quot; Piece/Hour --- Enter Pieces" IsRequired="false" IsSystem="false" DataType="Number" />
                <Parameter Key="jobroute.bflush_type" Value="" Description="Syteline Schema Mapping to jobroute. bflush_type field." IsRequired="false" IsSystem="false" DataType="String" />
                <Parameter Key="jobroute.cntrl_point" Value="=null" Description="Syteline Schema Mapping to jobroute. cntrl_point field." IsRequired="false" IsSystem="false" DataType="Number" />
                <Parameter Key="jobroute.efficiency" Value="=null" Description="Syteline Schema Mapping to jobroute. efficiency field." IsRequired="false" IsSystem="false" DataType="Number" />
                <Parameter Key="jobroute.fixovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. fixovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
                <Parameter Key="jobroute.fovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. fovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
                <Parameter Key="jobroute.run_basis_lbr" Value="H" Description="P = Pieces/Hour or H = Hours/Piece" IsRequired="false" IsSystem="false" DataType="String" />
                <Parameter Key="jobroute.run_basis_mch" Value="" Description="Syteline Schema Mapping to jobroute. run_basis_mch field." IsRequired="false" IsSystem="false" DataType="String" />
                <Parameter Key="jobroute.run_rate_lbr" Value="=null" Description="Syteline Schema Mapping to jobroute. run_rate_lbr field." IsRequired="false" IsSystem="false" DataType="Number" />
                <Parameter Key="jobroute.setup_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. setup_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
                <Parameter Key="jobroute.varovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. varovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
                <Parameter Key="jobroute.vovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. vovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
                <Parameter Key="jrt_sch.finish_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. finish_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
                <Parameter Key="jrt_sch.move_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. move_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
                <Parameter Key="jrt_sch.offset_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. offset_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
                <Parameter Key="jrt_sch.pcs_per_mch_hr" Value="=null" Description="Syteline Schema Mapping to jrt_sch. pcs_per_mch_hr field." IsRequired="false" IsSystem="false" DataType="Number" />
                <Parameter Key="jrt_sch.queue_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. queue_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
                <Parameter Key="jrt_sch.run_mch_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. run_mch_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
                <Parameter Key="jrt_sch.sched_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. sched_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
                <Parameter Key="jrt_sch.setup_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. setup_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
                <Parameter Key="jrt_sch.whenrule" Value="=null" Description="Syteline Schema Mapping to jrt_sch. whenrule field." IsRequired="false" IsSystem="false" DataType="Number" />
                <Parameter Key="jobroute.text" Value="" Description="Syteline Schema Mapping to jobroute. text field." IsRequired="false" IsSystem="false" DataType="String" />
              </Parameters>
            </Rule>
            <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Front Toolbox Material" ConditionExpression="" Enabled="true">
              <RuleData>
                <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                <Column Key="Operation No" ExpressionType="Expression" />
                <Column Key="Plant ID" ExpressionType="Expression" />
                <Column Key="Part Number" ExpressionType="Expression" />
                <Column Key="Quantity" ExpressionType="Expression" />
                <Value>
                  <Column Value="90" />
                  <Column Value="=sys.partnumber" />
                  <Column Value="=&quot;25&quot;" />
                  <Column Value="" />
                  <Column Value="=FrontToolbox_sel" />
                  <Column Value="1" />
                </Value>
              </RuleData>
            </Rule>
            <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Rear Toolbox Material" ConditionExpression="" Enabled="true">
              <RuleData>
                <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                <Column Key="Operation No" ExpressionType="Expression" />
                <Column Key="Plant ID" ExpressionType="Expression" />
                <Column Key="Part Number" ExpressionType="Expression" />
                <Column Key="Quantity" ExpressionType="Expression" />
                <Value>
                  <Column Value="91" />
                  <Column Value="=sys.partnumber" />
                  <Column Value="=&quot;25&quot;" />
                  <Column Value="" />
                  <Column Value="=RearToolbox_sel" />
                  <Column Value="1" />
                </Value>
              </RuleData>
            </Rule>
            <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Complete Assembly" ConditionExpression="=Assembly_Comp=True" Enabled="false">
              <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Shipping Completeing Grouping" ConditionExpression="=Assembly_Comp=True" Enabled="true">
                <RuleData>
                  <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                  <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                  <Column Key="Operation No" ExpressionType="Expression" />
                  <Column Key="Plant ID" ExpressionType="Expression" />
                  <Column Key="Part Number" ExpressionType="Expression" />
                  <Column Key="Quantity" ExpressionType="Expression" />
                  <Value>
                    <Column Value="105" />
                    <Column Value="=sys.partnumber" />
                    <Column Value="=&quot;20&quot;" />
                    <Column Value="" />
                    <Column Value="=ShipCompleteingGroup" />
                    <Column Value="1" />
                  </Value>
                </RuleData>
              </Rule>
            </Rule>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Arm" ConditionExpression="=BedType=&quot;A&quot;" Enabled="true">
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Operation" Caption="Operation 21 - Assembly Arm Beds" ConditionExpression="" Enabled="true">
            <Parameters>
              <Parameter Key="Component or Material ID" Value="=sys.partnumber" Description="Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
              <Parameter Key="Operation No" Value="=&quot;21&quot;" Description="Required" IsRequired="true" IsSystem="true" DataType="String" />
              <Parameter Key="Operation Name" Value="" Description="Operation Name or Work Center ID" IsRequired="false" IsSystem="true" DataType="String" />
              <Parameter Key="Operation Description" Value="" Description="Description of the Operation" IsRequired="false" IsSystem="true" DataType="String" />
              <Parameter Key="Work Center ID" Value="400" Description="Valid Work Center ID.  Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
              <Parameter Key="Print Code" Value="I" Description="I for Internal, E for External" IsRequired="true" IsSystem="true" DataType="String" />
              <Parameter Key="jrt_sch.run_lbr_hrs" Value="=1.75" Description="Used for &quot;H&quot; Hours/Piece --- Enter Hours" IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.pcs_per_lbr_hr" Value="=0" Description="Used for &quot;P&quot; Piece/Hour --- Enter Pieces" IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.bflush_type" Value="" Description="Syteline Schema Mapping to jobroute. bflush_type field." IsRequired="false" IsSystem="false" DataType="String" />
              <Parameter Key="jobroute.cntrl_point" Value="=null" Description="Syteline Schema Mapping to jobroute. cntrl_point field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.efficiency" Value="=null" Description="Syteline Schema Mapping to jobroute. efficiency field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.fixovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. fixovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.fovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. fovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.run_basis_lbr" Value="H" Description="P = Pieces/Hour or H = Hours/Piece" IsRequired="false" IsSystem="false" DataType="String" />
              <Parameter Key="jobroute.run_basis_mch" Value="" Description="Syteline Schema Mapping to jobroute. run_basis_mch field." IsRequired="false" IsSystem="false" DataType="String" />
              <Parameter Key="jobroute.run_rate_lbr" Value="=null" Description="Syteline Schema Mapping to jobroute. run_rate_lbr field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.setup_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. setup_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.varovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. varovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.vovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. vovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.finish_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. finish_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.move_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. move_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.offset_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. offset_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.pcs_per_mch_hr" Value="=null" Description="Syteline Schema Mapping to jrt_sch. pcs_per_mch_hr field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.queue_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. queue_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.run_mch_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. run_mch_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.sched_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. sched_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.setup_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. setup_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.whenrule" Value="=null" Description="Syteline Schema Mapping to jrt_sch. whenrule field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.text" Value="" Description="Syteline Schema Mapping to jobroute. text field." IsRequired="false" IsSystem="false" DataType="String" />
            </Parameters>
            <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Arm Bed Material" ConditionExpression="" Enabled="true">
              <RuleData>
                <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                <Column Key="Operation No" ExpressionType="Expression" />
                <Column Key="Plant ID" ExpressionType="Expression" />
                <Column Key="Part Number" ExpressionType="Expression" />
                <Column Key="Quantity" ExpressionType="Expression" />
                <Value>
                  <Column Value="180" />
                  <Column Value="=sys.partnumber" />
                  <Column Value="=&quot;21&quot;" />
                  <Column Value="" />
                  <Column Value="=Arms_sel" />
                  <Column Value="1" />
                </Value>
                <Value>
                  <Column Value="190" />
                  <Column Value="=sys.partnumber" />
                  <Column Value="=&quot;21&quot;" />
                  <Column Value="" />
                  <Column Value="=SpinLength_sel" />
                  <Column Value="1" />
                </Value>
                <Value>
                  <Column Value="200" />
                  <Column Value="=sys.partnumber" />
                  <Column Value="=&quot;21&quot;" />
                  <Column Value="" />
                  <Column Value="=Outlets_sel" />
                  <Column Value="1" />
                </Value>
              </RuleData>
            </Rule>
            <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Complete Assembly" ConditionExpression="=Assembly_Comp=True" Enabled="false">
              <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Hydraulic Power and Wired Control Material" ConditionExpression="" Enabled="false">
                <RuleData>
                  <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                  <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                  <Column Key="Operation No" ExpressionType="Expression" />
                  <Column Key="Plant ID" ExpressionType="Expression" />
                  <Column Key="Part Number" ExpressionType="Expression" />
                  <Column Key="Quantity" ExpressionType="Expression" />
                  <Value>
                    <Column Value="210" />
                    <Column Value="=sys.partnumber" />
                    <Column Value="=&quot;21&quot;" />
                    <Column Value="" />
                    <Column Value="=HydPwrUnit_sel" />
                    <Column Value="1" />
                  </Value>
                  <Value>
                    <Column Value="220" />
                    <Column Value="=sys.partnumber" />
                    <Column Value="=&quot;21&quot;" />
                    <Column Value="" />
                    <Column Value="=Wired" />
                    <Column Value="1" />
                  </Value>
                </RuleData>
              </Rule>
              <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Wireless Control" ConditionExpression="=ControlType=True" Enabled="true">
                <RuleData>
                  <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                  <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                  <Column Key="Operation No" ExpressionType="Expression" />
                  <Column Key="Plant ID" ExpressionType="Expression" />
                  <Column Key="Part Number" ExpressionType="Expression" />
                  <Column Key="Quantity" ExpressionType="Expression" />
                  <Value>
                    <Column Value="230" />
                    <Column Value="=sys.partnumber" />
                    <Column Value="=&quot;21&quot;" />
                    <Column Value="" />
                    <Column Value="=ControlType_sel" />
                    <Column Value="1" />
                  </Value>
                </RuleData>
              </Rule>
            </Rule>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Spike" ConditionExpression="=BedType=&quot;S&quot;" Enabled="true">
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Operation" Caption="Operation 22 - Assembly Spike Beds" ConditionExpression="" Enabled="true">
            <Parameters>
              <Parameter Key="Component or Material ID" Value="=sys.partnumber" Description="Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
              <Parameter Key="Operation No" Value="=&quot;22&quot;" Description="Required" IsRequired="true" IsSystem="true" DataType="String" />
              <Parameter Key="Operation Name" Value="" Description="Operation Name or Work Center ID" IsRequired="false" IsSystem="true" DataType="String" />
              <Parameter Key="Operation Description" Value="" Description="Description of the Operation" IsRequired="false" IsSystem="true" DataType="String" />
              <Parameter Key="Work Center ID" Value="400" Description="Valid Work Center ID.  Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
              <Parameter Key="Print Code" Value="I" Description="I for Internal, E for External" IsRequired="true" IsSystem="true" DataType="String" />
              <Parameter Key="jrt_sch.run_lbr_hrs" Value="=.75" Description="Used for &quot;H&quot; Hours/Piece --- Enter Hours" IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.pcs_per_lbr_hr" Value="=0" Description="Used for &quot;P&quot; Piece/Hour --- Enter Pieces" IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.bflush_type" Value="" Description="Syteline Schema Mapping to jobroute. bflush_type field." IsRequired="false" IsSystem="false" DataType="String" />
              <Parameter Key="jobroute.cntrl_point" Value="=null" Description="Syteline Schema Mapping to jobroute. cntrl_point field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.efficiency" Value="=null" Description="Syteline Schema Mapping to jobroute. efficiency field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.fixovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. fixovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.fovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. fovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.run_basis_lbr" Value="H" Description="P = Pieces/Hour or H = Hours/Piece" IsRequired="false" IsSystem="false" DataType="String" />
              <Parameter Key="jobroute.run_basis_mch" Value="" Description="Syteline Schema Mapping to jobroute. run_basis_mch field." IsRequired="false" IsSystem="false" DataType="String" />
              <Parameter Key="jobroute.run_rate_lbr" Value="=null" Description="Syteline Schema Mapping to jobroute. run_rate_lbr field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.setup_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. setup_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.varovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. varovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.vovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. vovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.finish_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. finish_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.move_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. move_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.offset_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. offset_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.pcs_per_mch_hr" Value="=null" Description="Syteline Schema Mapping to jrt_sch. pcs_per_mch_hr field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.queue_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. queue_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.run_mch_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. run_mch_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.sched_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. sched_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.setup_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. setup_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.whenrule" Value="=null" Description="Syteline Schema Mapping to jrt_sch. whenrule field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.text" Value="" Description="Syteline Schema Mapping to jobroute. text field." IsRequired="false" IsSystem="false" DataType="String" />
            </Parameters>
            <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Complete Assembly" ConditionExpression="=Assembly_Comp=True" Enabled="false">
              <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Hydraulic Power and Wired Control Material" ConditionExpression="" Enabled="false">
                <RuleData>
                  <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                  <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                  <Column Key="Operation No" ExpressionType="Expression" />
                  <Column Key="Plant ID" ExpressionType="Expression" />
                  <Column Key="Part Number" ExpressionType="Expression" />
                  <Column Key="Quantity" ExpressionType="Expression" />
                  <Value>
                    <Column Value="270" />
                    <Column Value="=sys.partnumber" />
                    <Column Value="=&quot;22&quot;" />
                    <Column Value="" />
                    <Column Value="=HydPwrUnit_sel" />
                    <Column Value="1" />
                  </Value>
                  <Value>
                    <Column Value="280" />
                    <Column Value="=sys.partnumber" />
                    <Column Value="=&quot;22&quot;" />
                    <Column Value="" />
                    <Column Value="=Wired" />
                    <Column Value="1" />
                  </Value>
                </RuleData>
              </Rule>
              <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Wireless Control" ConditionExpression="=ControlType=True" Enabled="true">
                <RuleData>
                  <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                  <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                  <Column Key="Operation No" ExpressionType="Expression" />
                  <Column Key="Plant ID" ExpressionType="Expression" />
                  <Column Key="Part Number" ExpressionType="Expression" />
                  <Column Key="Quantity" ExpressionType="Expression" />
                  <Value>
                    <Column Value="290" />
                    <Column Value="=sys.partnumber" />
                    <Column Value="=&quot;22&quot;" />
                    <Column Value="" />
                    <Column Value="=ControlType_sel" />
                    <Column Value="1" />
                  </Value>
                </RuleData>
              </Rule>
            </Rule>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Spike Bed material" ConditionExpression="" Enabled="true">
            <RuleData>
              <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
              <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
              <Column Key="Operation No" ExpressionType="Expression" />
              <Column Key="Plant ID" ExpressionType="Expression" />
              <Column Key="Part Number" ExpressionType="Expression" />
              <Column Key="Quantity" ExpressionType="Expression" />
              <Value>
                <Column Value="240" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;22&quot;" />
                <Column Value="" />
                <Column Value="=Outlets_sel" />
                <Column Value="1" />
              </Value>
              <Value>
                <Column Value="250" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;22&quot;" />
                <Column Value="" />
                <Column Value="=Spikes_sel" />
                <Column Value="1" />
              </Value>
              <Value>
                <Column Value="260" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;22&quot;" />
                <Column Value="" />
                <Column Value="=SideRails_sel" />
                <Column Value="1" />
              </Value>
            </RuleData>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Flat" ConditionExpression="=BedType=&quot;R&quot;" Enabled="true">
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Operation" Caption="Operation 23 - Assembly Flat Beds" ConditionExpression="" Enabled="true">
            <Parameters>
              <Parameter Key="Component or Material ID" Value="=sys.partnumber" Description="Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
              <Parameter Key="Operation No" Value="=&quot;23&quot;" Description="Required" IsRequired="true" IsSystem="true" DataType="String" />
              <Parameter Key="Operation Name" Value="" Description="Operation Name or Work Center ID" IsRequired="false" IsSystem="true" DataType="String" />
              <Parameter Key="Operation Description" Value="" Description="Description of the Operation" IsRequired="false" IsSystem="true" DataType="String" />
              <Parameter Key="Work Center ID" Value="400" Description="Valid Work Center ID.  Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
              <Parameter Key="Print Code" Value="I" Description="I for Internal, E for External" IsRequired="true" IsSystem="true" DataType="String" />
              <Parameter Key="jrt_sch.run_lbr_hrs" Value="=0" Description="Used for &quot;H&quot; Hours/Piece --- Enter Hours" IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.pcs_per_lbr_hr" Value="=0" Description="Used for &quot;P&quot; Piece/Hour --- Enter Pieces" IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.bflush_type" Value="" Description="Syteline Schema Mapping to jobroute. bflush_type field." IsRequired="false" IsSystem="false" DataType="String" />
              <Parameter Key="jobroute.cntrl_point" Value="=null" Description="Syteline Schema Mapping to jobroute. cntrl_point field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.efficiency" Value="=null" Description="Syteline Schema Mapping to jobroute. efficiency field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.fixovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. fixovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.fovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. fovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.run_basis_lbr" Value="H" Description="P = Pieces/Hour or H = Hours/Piece" IsRequired="false" IsSystem="false" DataType="String" />
              <Parameter Key="jobroute.run_basis_mch" Value="" Description="Syteline Schema Mapping to jobroute. run_basis_mch field." IsRequired="false" IsSystem="false" DataType="String" />
              <Parameter Key="jobroute.run_rate_lbr" Value="=null" Description="Syteline Schema Mapping to jobroute. run_rate_lbr field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.setup_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. setup_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.varovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. varovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.vovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. vovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.finish_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. finish_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.move_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. move_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.offset_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. offset_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.pcs_per_mch_hr" Value="=null" Description="Syteline Schema Mapping to jrt_sch. pcs_per_mch_hr field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.queue_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. queue_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.run_mch_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. run_mch_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.sched_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. sched_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.setup_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. setup_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jrt_sch.whenrule" Value="=null" Description="Syteline Schema Mapping to jrt_sch. whenrule field." IsRequired="false" IsSystem="false" DataType="Number" />
              <Parameter Key="jobroute.text" Value="" Description="Syteline Schema Mapping to jobroute. text field." IsRequired="false" IsSystem="false" DataType="String" />
            </Parameters>
            <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Hydraulic Plumbing (Outlets or Not)" ConditionExpression="=HydPowerFlat=True" Enabled="true">
              <RuleData>
                <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                <Column Key="Operation No" ExpressionType="Expression" />
                <Column Key="Plant ID" ExpressionType="Expression" />
                <Column Key="Part Number" ExpressionType="Expression" />
                <Column Key="Quantity" ExpressionType="Expression" />
                <Value>
                  <Column Value="300" />
                  <Column Value="=sys.partnumber" />
                  <Column Value="=&quot;23&quot;" />
                  <Column Value="" />
                  <Column Value="=Outlets_sel" />
                  <Column Value="1" />
                </Value>
              </RuleData>
            </Rule>
            <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Complete Assembly" ConditionExpression="=Assembly_Comp=True" Enabled="false">
              <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Hydraulic Power and Wired Control Material" ConditionExpression="=HydPowerFlat=True" Enabled="false">
                <RuleData>
                  <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                  <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                  <Column Key="Operation No" ExpressionType="Expression" />
                  <Column Key="Plant ID" ExpressionType="Expression" />
                  <Column Key="Part Number" ExpressionType="Expression" />
                  <Column Key="Quantity" ExpressionType="Expression" />
                  <Value>
                    <Column Value="310" />
                    <Column Value="=sys.partnumber" />
                    <Column Value="=&quot;23&quot;" />
                    <Column Value="" />
                    <Column Value="=HydPwrUnit_sel" />
                    <Column Value="1" />
                  </Value>
                  <Value>
                    <Column Value="320" />
                    <Column Value="=sys.partnumber" />
                    <Column Value="=&quot;23&quot;" />
                    <Column Value="" />
                    <Column Value="=Wired" />
                    <Column Value="1" />
                  </Value>
                </RuleData>
              </Rule>
              <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Wireless Control" ConditionExpression="=ControlType=True" Enabled="true">
                <RuleData>
                  <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                  <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                  <Column Key="Operation No" ExpressionType="Expression" />
                  <Column Key="Plant ID" ExpressionType="Expression" />
                  <Column Key="Part Number" ExpressionType="Expression" />
                  <Column Key="Quantity" ExpressionType="Expression" />
                  <Value>
                    <Column Value="330" />
                    <Column Value="=sys.partnumber" />
                    <Column Value="=&quot;23&quot;" />
                    <Column Value="" />
                    <Column Value="=ControlType_sel" />
                    <Column Value="1" />
                  </Value>
                </RuleData>
              </Rule>
            </Rule>
            <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Side Rails" ConditionExpression="=SideRails=True" Enabled="true">
              <RuleData>
                <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                <Column Key="Operation No" ExpressionType="Expression" />
                <Column Key="Plant ID" ExpressionType="Expression" />
                <Column Key="Part Number" ExpressionType="Expression" />
                <Column Key="Quantity" ExpressionType="Expression" />
                <Value>
                  <Column Value="340" />
                  <Column Value="=sys.partnumber" />
                  <Column Value="=&quot;23&quot;" />
                  <Column Value="" />
                  <Column Value="=SideRails_sel" />
                  <Column Value="1" />
                </Value>
              </RuleData>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Operation" Caption="Operation 30 - Shipping" ConditionExpression="" Enabled="false">
      <Comment>Welding time at top level.  Currently this time is in the deck.  It needs to be moved from to deck to this location</Comment>
      <Parameters>
        <Parameter Key="Component or Material ID" Value="=sys.partnumber" Description="Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
        <Parameter Key="Operation No" Value="=&quot;30&quot;" Description="Required" IsRequired="true" IsSystem="true" DataType="String" />
        <Parameter Key="Operation Name" Value="" Description="Operation Name or Work Center ID" IsRequired="false" IsSystem="true" DataType="String" />
        <Parameter Key="Operation Description" Value="" Description="Description of the Operation" IsRequired="false" IsSystem="true" DataType="String" />
        <Parameter Key="Work Center ID" Value="600" Description="Valid Work Center ID.  Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
        <Parameter Key="Print Code" Value="I" Description="I for Internal, E for External" IsRequired="true" IsSystem="true" DataType="String" />
        <Parameter Key="jrt_sch.run_lbr_hrs" Value="=0" Description="Used for &quot;H&quot; Hours/Piece --- Enter Hours" IsRequired="false" IsSystem="false" DataType="Number" />
        <Parameter Key="jrt_sch.pcs_per_lbr_hr" Value="=0" Description="Used for &quot;P&quot; Piece/Hour --- Enter Pieces" IsRequired="false" IsSystem="false" DataType="Number" />
        <Parameter Key="jobroute.bflush_type" Value="" Description="Syteline Schema Mapping to jobroute. bflush_type field." IsRequired="false" IsSystem="false" DataType="String" />
        <Parameter Key="jobroute.cntrl_point" Value="=1" Description="Syteline Schema Mapping to jobroute. cntrl_point field." IsRequired="false" IsSystem="false" DataType="Number" />
        <Parameter Key="jobroute.efficiency" Value="=null" Description="Syteline Schema Mapping to jobroute. efficiency field." IsRequired="false" IsSystem="false" DataType="Number" />
        <Parameter Key="jobroute.fixovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. fixovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
        <Parameter Key="jobroute.fovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. fovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
        <Parameter Key="jobroute.run_basis_lbr" Value="H" Description="P = Pieces/Hour or H = Hours/Piece" IsRequired="false" IsSystem="false" DataType="String" />
        <Parameter Key="jobroute.run_basis_mch" Value="" Description="Syteline Schema Mapping to jobroute. run_basis_mch field." IsRequired="false" IsSystem="false" DataType="String" />
        <Parameter Key="jobroute.run_rate_lbr" Value="=null" Description="Syteline Schema Mapping to jobroute. run_rate_lbr field." IsRequired="false" IsSystem="false" DataType="Number" />
        <Parameter Key="jobroute.setup_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. setup_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
        <Parameter Key="jobroute.varovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. varovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
        <Parameter Key="jobroute.vovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. vovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
        <Parameter Key="jrt_sch.finish_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. finish_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
        <Parameter Key="jrt_sch.move_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. move_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
        <Parameter Key="jrt_sch.offset_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. offset_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
        <Parameter Key="jrt_sch.pcs_per_mch_hr" Value="=null" Description="Syteline Schema Mapping to jrt_sch. pcs_per_mch_hr field." IsRequired="false" IsSystem="false" DataType="Number" />
        <Parameter Key="jrt_sch.queue_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. queue_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
        <Parameter Key="jrt_sch.run_mch_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. run_mch_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
        <Parameter Key="jrt_sch.sched_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. sched_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
        <Parameter Key="jrt_sch.setup_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. setup_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
        <Parameter Key="jrt_sch.whenrule" Value="=null" Description="Syteline Schema Mapping to jrt_sch. whenrule field." IsRequired="false" IsSystem="false" DataType="Number" />
        <Parameter Key="jobroute.text" Value="" Description="Syteline Schema Mapping to jobroute. text field." IsRequired="false" IsSystem="false" DataType="String" />
      </Parameters>
    </Rule>
    <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Customer order line adds" ConditionExpression="=CustomerOrder=True" Enabled="true">
      <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Shipped Toolbox" ConditionExpression="=Assembly_Comp=False" Enabled="true">
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Bed is not skirted" ConditionExpression="=BedFeature!=&quot;SK&quot; &amp; BedFeature!=&quot;SW&quot; &amp; BedFeature!=&quot;SF&quot; &amp; BedFeature!=&quot;US&quot;" Enabled="true">
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Front Toolbox Material" ConditionExpression="=FrontToolbox != &quot;NNNN&quot;" Enabled="true">
            <RuleData>
              <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
              <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
              <Column Key="Operation No" ExpressionType="Expression" />
              <Column Key="Plant ID" ExpressionType="Expression" />
              <Column Key="Part Number" ExpressionType="Expression" />
              <Column Key="Quantity" ExpressionType="Expression" />
              <Column Key="Create New Line" DataType="Number" ExpressionType="Expression" />
              <Column Key="Price" DataType="Number" ExpressionType="Expression" />
              <Value>
                <Column Value="358" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;30&quot;" />
                <Column Value="" />
                <Column Value="=FrontLHToolbox_sel" />
                <Column Value="1" />
                <Column Value="=1" />
                <Column Value="=FrontToolbox.Value.PriceFlatBed*.5" />
              </Value>
              <Value>
                <Column Value="359" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;30&quot;" />
                <Column Value="" />
                <Column Value="=FrontRHToolbox_sel" />
                <Column Value="1" />
                <Column Value="=1" />
                <Column Value="=FrontToolbox.Value.PriceFlatBed*.5" />
              </Value>
            </RuleData>
          </Rule>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Rear Toolbox Material &amp; Mudflap Mounts Non Skirted Bed" ConditionExpression="=RearToolbox=true" Enabled="true">
          <Comment>Note this does not apply to skirts as RearToolbox=true is not answered for skirted it is automaticaly assigned toolboxes by selection of a skirted deck.</Comment>
          <RuleData>
            <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
            <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
            <Column Key="Operation No" ExpressionType="Expression" />
            <Column Key="Plant ID" ExpressionType="Expression" />
            <Column Key="Part Number" ExpressionType="Expression" />
            <Column Key="Quantity" ExpressionType="Expression" />
            <Column Key="Create New Line" DataType="Number" ExpressionType="Expression" />
            <Column Key="Price" DataType="Number" ExpressionType="Expression" />
            <Value>
              <Column Value="360" />
              <Column Value="=sys.partnumber" />
              <Column Value="=&quot;30&quot;" />
              <Column Value="" />
              <Column Value="=RearLHToolbox_sel" />
              <Column Value="1" />
              <Column Value="=1" />
              <Column Value="=RearTB_Price*.5" />
            </Value>
            <Value>
              <Column Value="361" />
              <Column Value="=sys.partnumber" />
              <Column Value="=&quot;30&quot;" />
              <Column Value="" />
              <Column Value="=RearRHToolbox_sel" />
              <Column Value="1" />
              <Column Value="=1" />
              <Column Value="=RearTB_Price*.5" />
            </Value>
            <Value>
              <Column Value="362" />
              <Column Value="=sys.partnumber" />
              <Column Value="=&quot;30&quot;" />
              <Column Value="" />
              <Column Value="=MudflapMount_RT" />
              <Column Value="1" />
              <Column Value="=1" />
              <Column Value="=MudflapMountRT_Price" />
            </Value>
            <Value>
              <Column Value="363" />
              <Column Value="=sys.partnumber" />
              <Column Value="=&quot;30&quot;" />
              <Column Value="" />
              <Column Value="=RearToolboxHardware" />
              <Column Value="1" />
              <Column Value="=1" />
              <Column Value="=0" />
            </Value>
          </RuleData>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Skirted Mudflap Mounts" ConditionExpression="=BedFeature=&quot;SK&quot; or BedFeature=&quot;SW&quot; or BedFeature=&quot;SF&quot; or BedFeature=&quot;US&quot;" Enabled="true">
          <RuleData>
            <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
            <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
            <Column Key="Operation No" ExpressionType="Expression" />
            <Column Key="Plant ID" ExpressionType="Expression" />
            <Column Key="Part Number" ExpressionType="Expression" />
            <Column Key="Quantity" ExpressionType="Expression" />
            <Column Key="Create New Line" DataType="Number" ExpressionType="Expression" />
            <Column Key="Price" DataType="Number" ExpressionType="Expression" />
            <Value>
              <Column Value="364" />
              <Column Value="=sys.partnumber" />
              <Column Value="=&quot;30&quot;" />
              <Column Value="" />
              <Column Value="=MudflapMount_RT" />
              <Column Value="1" />
              <Column Value="=1" />
              <Column Value="=MudflapMountRT_Price" />
            </Value>
          </RuleData>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Shipping Completeing Grouping" ConditionExpression="=Assembly_Comp=False" Enabled="true">
        <RuleData>
          <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
          <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
          <Column Key="Operation No" ExpressionType="Expression" />
          <Column Key="Plant ID" ExpressionType="Expression" />
          <Column Key="Part Number" ExpressionType="Expression" />
          <Column Key="Quantity" ExpressionType="Expression" />
          <Column Key="Create New Line" DataType="Number" ExpressionType="Expression" />
          <Column Key="Price" DataType="Number" ExpressionType="Expression" />
          <Value>
            <Column Value="445" />
            <Column Value="=sys.partnumber" />
            <Column Value="=&quot;30&quot;" />
            <Column Value="" />
            <Column Value="=ShipCompleteingGroup" />
            <Column Value="1" />
            <Column Value="=1" />
            <Column Value="=if(CompleteGroupType=&quot;HFG&quot;,Arm_Finish_Group_Disc,if(CompleteGroupType=&quot;SFG&quot;,Spike_Finish_Group_Disc,Reg_Finish_Group_Disc))" />
          </Value>
        </RuleData>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Mudflaps" ConditionExpression="=Mudflaps=True" Enabled="true">
        <Comment>Divide by 2 as the part will be qty 2 on the invoice and  pick list</Comment>
        <RuleData>
          <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
          <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
          <Column Key="Operation No" ExpressionType="Expression" />
          <Column Key="Plant ID" ExpressionType="Expression" />
          <Column Key="Part Number" ExpressionType="Expression" />
          <Column Key="Quantity" ExpressionType="Expression" />
          <Column Key="Create New Line" DataType="Number" ExpressionType="Expression" />
          <Column Key="Price" DataType="Number" ExpressionType="Expression" />
          <Value>
            <Column Value="446" />
            <Column Value="=sys.partnumber" />
            <Column Value="=&quot;30&quot;" />
            <Column Value="" />
            <Column Value="=Mudflaps_sel" />
            <Column Value="2" />
            <Column Value="=1" />
            <Column Value="=Mudflaps_Price/2" />
          </Value>
        </RuleData>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Wireharness yes" ConditionExpression="=Wireharness=true" Enabled="false" />
      <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Adapter Harness Material" ConditionExpression="=AdapeterHarness=True" Enabled="true">
        <RuleData>
          <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
          <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
          <Column Key="Operation No" ExpressionType="Expression" />
          <Column Key="Plant ID" ExpressionType="Expression" />
          <Column Key="Part Number" ExpressionType="Expression" />
          <Column Key="Quantity" ExpressionType="Expression" />
          <Column Key="Create New Line" DataType="Number" ExpressionType="Expression" />
          <Column Key="Price" DataType="Number" ExpressionType="Expression" />
          <Value>
            <Column Value="430" />
            <Column Value="=sys.partnumber" />
            <Column Value="=&quot;30&quot;" />
            <Column Value="" />
            <Column Value="=ButlerAdapter" />
            <Column Value="1" />
            <Column Value="=1" />
            <Column Value="=AdapterHarness_Price" />
          </Value>
        </RuleData>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Mount Kit Material" ConditionExpression="=MountKit=true" Enabled="true">
        <RuleData>
          <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
          <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
          <Column Key="Operation No" ExpressionType="Expression" />
          <Column Key="Plant ID" ExpressionType="Expression" />
          <Column Key="Part Number" ExpressionType="Expression" />
          <Column Key="Quantity" ExpressionType="Expression" />
          <Column Key="Create New Line" DataType="Number" ExpressionType="Expression" />
          <Column Key="Price" DataType="Number" ExpressionType="Expression" />
          <Column Key="Message" ExpressionType="Expression" />
          <Column Key="Config String" ExpressionType="Expression" />
          <Value>
            <Column Value="400" />
            <Column Value="=sys.partnumber" />
            <Column Value="=&quot;30&quot;" />
            <Column Value="" />
            <Column Value="=MountKit_sel" />
            <Column Value="1" />
            <Column Value="=1" />
            <Column Value="=MountKit_Price" />
            <Column Value="" />
            <Column Value="" />
          </Value>
        </RuleData>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Work Lights Material" ConditionExpression="=WorkLights=True" Enabled="true">
        <RuleData>
          <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
          <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
          <Column Key="Operation No" ExpressionType="Expression" />
          <Column Key="Plant ID" ExpressionType="Expression" />
          <Column Key="Part Number" ExpressionType="Expression" />
          <Column Key="Quantity" ExpressionType="Expression" />
          <Column Key="Create New Line" DataType="Number" ExpressionType="Expression" />
          <Column Key="Price" DataType="Number" ExpressionType="Expression" />
          <Value>
            <Column Value="390" />
            <Column Value="=sys.partnumber" />
            <Column Value="=&quot;30&quot;" />
            <Column Value="" />
            <Column Value="=WorkLights_sel" />
            <Column Value="1" />
            <Column Value="=1" />
            <Column Value="=WorkLights_Price" />
          </Value>
        </RuleData>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Material Rule for Manufacturing - Mount Tabs" ConditionExpression="=MntTabs=True" Enabled="true">
        <RuleData>
          <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
          <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
          <Column Key="Operation No" ExpressionType="Expression" />
          <Column Key="Plant ID" ExpressionType="Expression" />
          <Column Key="Part Number" ExpressionType="Expression" />
          <Column Key="Quantity" ExpressionType="Expression" />
          <Column Key="Create New Line" DataType="Number" ExpressionType="Expression" />
          <Column Key="Price" DataType="Number" ExpressionType="Expression" />
          <Value>
            <Column Value="350" />
            <Column Value="=sys.partnumber" />
            <Column Value="=&quot;30&quot;" />
            <Column Value="" />
            <Column Value="=MountTab_sel" />
            <Column Value="1" />
            <Column Value="=1" />
            <Column Value="=MountTab_Price" />
          </Value>
        </RuleData>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="XBed Material" ConditionExpression="=XBed!=&quot;NN&quot;" Enabled="true">
        <RuleData>
          <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
          <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
          <Column Key="Operation No" ExpressionType="Expression" />
          <Column Key="Plant ID" ExpressionType="Expression" />
          <Column Key="Part Number" ExpressionType="Expression" />
          <Column Key="Quantity" ExpressionType="Expression" />
          <Column Key="Create New Line" DataType="Number" ExpressionType="Expression" />
          <Column Key="Price" DataType="Number" ExpressionType="Expression" />
          <Value>
            <Column Value="380" />
            <Column Value="=sys.partnumber" />
            <Column Value="=&quot;30&quot;" />
            <Column Value="" />
            <Column Value="=XBed_sel" />
            <Column Value="1" />
            <Column Value="=1" />
            <Column Value="=XBed_Price" />
          </Value>
        </RuleData>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Customer Order Only" ConditionExpression="=CustomerOrder=True" Enabled="false" />
      <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Arm" ConditionExpression="=BedType=&quot;A&quot;" Enabled="true">
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Operation" Caption="Operation 31 - Shipping Arm Beds" ConditionExpression="" Enabled="false">
          <Parameters>
            <Parameter Key="Component or Material ID" Value="=sys.partnumber" Description="Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="Operation No" Value="=&quot;31&quot;" Description="Required" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="Operation Name" Value="" Description="Operation Name or Work Center ID" IsRequired="false" IsSystem="true" DataType="String" />
            <Parameter Key="Operation Description" Value="" Description="Description of the Operation" IsRequired="false" IsSystem="true" DataType="String" />
            <Parameter Key="Work Center ID" Value="600" Description="Valid Work Center ID.  Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="Print Code" Value="I" Description="I for Internal, E for External" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="jrt_sch.run_lbr_hrs" Value="=0" Description="Used for &quot;H&quot; Hours/Piece --- Enter Hours" IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.pcs_per_lbr_hr" Value="=0" Description="Used for &quot;P&quot; Piece/Hour --- Enter Pieces" IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.bflush_type" Value="" Description="Syteline Schema Mapping to jobroute. bflush_type field." IsRequired="false" IsSystem="false" DataType="String" />
            <Parameter Key="jobroute.cntrl_point" Value="=1" Description="Syteline Schema Mapping to jobroute. cntrl_point field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.efficiency" Value="=null" Description="Syteline Schema Mapping to jobroute. efficiency field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.fixovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. fixovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.fovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. fovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.run_basis_lbr" Value="H" Description="P = Pieces/Hour or H = Hours/Piece" IsRequired="false" IsSystem="false" DataType="String" />
            <Parameter Key="jobroute.run_basis_mch" Value="" Description="Syteline Schema Mapping to jobroute. run_basis_mch field." IsRequired="false" IsSystem="false" DataType="String" />
            <Parameter Key="jobroute.run_rate_lbr" Value="=null" Description="Syteline Schema Mapping to jobroute. run_rate_lbr field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.setup_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. setup_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.varovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. varovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.vovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. vovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.finish_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. finish_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.move_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. move_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.offset_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. offset_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.pcs_per_mch_hr" Value="=null" Description="Syteline Schema Mapping to jrt_sch. pcs_per_mch_hr field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.queue_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. queue_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.run_mch_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. run_mch_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.sched_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. sched_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.setup_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. setup_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.whenrule" Value="=null" Description="Syteline Schema Mapping to jrt_sch. whenrule field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.text" Value="" Description="Syteline Schema Mapping to jobroute. text field." IsRequired="false" IsSystem="false" DataType="String" />
          </Parameters>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Arm Spikes" ConditionExpression="=Arm_Spikes=true" Enabled="true">
          <RuleData>
            <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
            <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
            <Column Key="Operation No" ExpressionType="Expression" />
            <Column Key="Plant ID" ExpressionType="Expression" />
            <Column Key="Part Number" ExpressionType="Expression" />
            <Column Key="Quantity" ExpressionType="Expression" />
            <Column Key="Create New Line" DataType="Number" ExpressionType="Expression" />
            <Column Key="Price" DataType="Number" ExpressionType="Expression" />
            <Value>
              <Column Value="447" />
              <Column Value="=sys.partnumber" />
              <Column Value="=&quot;31&quot;" />
              <Column Value="" />
              <Column Value="=Arm_Spikes_sel" />
              <Column Value="1" />
              <Column Value="=1" />
              <Column Value="=ABSpike_Price" />
            </Value>
          </RuleData>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Arm Spike Bracket" ConditionExpression="=Arm_Spikes_Bracket=true" Enabled="true">
          <RuleData>
            <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
            <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
            <Column Key="Operation No" ExpressionType="Expression" />
            <Column Key="Plant ID" ExpressionType="Expression" />
            <Column Key="Part Number" ExpressionType="Expression" />
            <Column Key="Quantity" ExpressionType="Expression" />
            <Column Key="Create New Line" DataType="Number" ExpressionType="Expression" />
            <Column Key="Price" DataType="Number" ExpressionType="Expression" />
            <Value>
              <Column Value="448" />
              <Column Value="=sys.partnumber" />
              <Column Value="=&quot;31&quot;" />
              <Column Value="" />
              <Column Value="=Arm_Spikes_Brackets_sel" />
              <Column Value="1" />
              <Column Value="=1" />
              <Column Value="=AB_Brackets_Price" />
            </Value>
          </RuleData>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Wireless Control Material" ConditionExpression="=ControlType=True" Enabled="true">
          <RuleData>
            <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
            <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
            <Column Key="Operation No" ExpressionType="Expression" />
            <Column Key="Plant ID" ExpressionType="Expression" />
            <Column Key="Part Number" ExpressionType="Expression" />
            <Column Key="Quantity" ExpressionType="Expression" />
            <Column Key="Create New Line" DataType="Number" ExpressionType="Expression" />
            <Column Key="Price" DataType="Number" ExpressionType="Expression" />
            <Value>
              <Column Value="470" />
              <Column Value="=sys.partnumber" />
              <Column Value="=&quot;31&quot;" />
              <Column Value="" />
              <Column Value="=ControlType_sel" />
              <Column Value="1" />
              <Column Value="=1" />
              <Column Value="=Wireless_Price" />
            </Value>
          </RuleData>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Variable Surcharge ARM" ConditionExpression="" Enabled="true">
          <RuleData>
            <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
            <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
            <Column Key="Operation No" ExpressionType="Expression" />
            <Column Key="Plant ID" ExpressionType="Expression" />
            <Column Key="Part Number" ExpressionType="Expression" />
            <Column Key="Quantity" ExpressionType="Expression" />
            <Column Key="Create New Line" DataType="Number" ExpressionType="Expression" />
            <Column Key="Price" DataType="Number" ExpressionType="Expression" />
            <Column Key="Message" ExpressionType="Expression" />
            <Column Key="Config String" ExpressionType="Expression" />
            <Value>
              <Column Value="400" />
              <Column Value="=sys.partnumber" />
              <Column Value="=&quot;30&quot;" />
              <Column Value="" />
              <Column Value="=&quot;vsbba22&quot;" />
              <Column Value="1" />
              <Column Value="=1" />
              <Column Value="=100.00" />
              <Column Value="" />
              <Column Value="" />
            </Value>
          </RuleData>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Shipping parts" ConditionExpression="=Assembly_Comp=False" Enabled="false">
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Hydraulic Power and Wired Control Material" ConditionExpression="" Enabled="false">
            <RuleData>
              <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
              <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
              <Column Key="Operation No" ExpressionType="Expression" />
              <Column Key="Plant ID" ExpressionType="Expression" />
              <Column Key="Part Number" ExpressionType="Expression" />
              <Column Key="Quantity" ExpressionType="Expression" />
              <Value>
                <Column Value="450" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;31&quot;" />
                <Column Value="" />
                <Column Value="=HydPwrUnit_sel" />
                <Column Value="1" />
              </Value>
              <Value>
                <Column Value="460" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;31&quot;" />
                <Column Value="" />
                <Column Value="=Wired" />
                <Column Value="1" />
              </Value>
            </RuleData>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Customer Order Only" ConditionExpression="=CustomerOrder=True" Enabled="true" />
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Job Order Only" ConditionExpression="=CustomerOrder=False" Enabled="false">
            <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Wireless Control Material" ConditionExpression="=ControlType=True" Enabled="true">
              <RuleData>
                <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
                <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
                <Column Key="Operation No" ExpressionType="Expression" />
                <Column Key="Plant ID" ExpressionType="Expression" />
                <Column Key="Part Number" ExpressionType="Expression" />
                <Column Key="Quantity" ExpressionType="Expression" />
                <Column Key="Create New Line" DataType="Number" ExpressionType="Expression" />
                <Column Key="Price" DataType="Number" ExpressionType="Expression" />
                <Value>
                  <Column Value="470" />
                  <Column Value="=sys.partnumber" />
                  <Column Value="=&quot;31&quot;" />
                  <Column Value="" />
                  <Column Value="=ControlType_sel" />
                  <Column Value="" />
                  <Column Value="" />
                  <Column Value="=Wireless_Price" />
                </Value>
              </RuleData>
            </Rule>
          </Rule>
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Spike" ConditionExpression="=BedType=&quot;S&quot;" Enabled="true">
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Operation" Caption="Operation 32 - Assembly Spike Beds" ConditionExpression="" Enabled="false">
          <Parameters>
            <Parameter Key="Component or Material ID" Value="=sys.partnumber" Description="Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="Operation No" Value="=&quot;32&quot;" Description="Required" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="Operation Name" Value="" Description="Operation Name or Work Center ID" IsRequired="false" IsSystem="true" DataType="String" />
            <Parameter Key="Operation Description" Value="" Description="Description of the Operation" IsRequired="false" IsSystem="true" DataType="String" />
            <Parameter Key="Work Center ID" Value="600" Description="Valid Work Center ID.  Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="Print Code" Value="I" Description="I for Internal, E for External" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="jrt_sch.run_lbr_hrs" Value="=0" Description="Used for &quot;H&quot; Hours/Piece --- Enter Hours" IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.pcs_per_lbr_hr" Value="=0" Description="Used for &quot;P&quot; Piece/Hour --- Enter Pieces" IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.bflush_type" Value="" Description="Syteline Schema Mapping to jobroute. bflush_type field." IsRequired="false" IsSystem="false" DataType="String" />
            <Parameter Key="jobroute.cntrl_point" Value="=null" Description="Syteline Schema Mapping to jobroute. cntrl_point field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.efficiency" Value="=null" Description="Syteline Schema Mapping to jobroute. efficiency field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.fixovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. fixovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.fovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. fovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.run_basis_lbr" Value="H" Description="P = Pieces/Hour or H = Hours/Piece" IsRequired="false" IsSystem="false" DataType="String" />
            <Parameter Key="jobroute.run_basis_mch" Value="" Description="Syteline Schema Mapping to jobroute. run_basis_mch field." IsRequired="false" IsSystem="false" DataType="String" />
            <Parameter Key="jobroute.run_rate_lbr" Value="=null" Description="Syteline Schema Mapping to jobroute. run_rate_lbr field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.setup_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. setup_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.varovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. varovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.vovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. vovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.finish_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. finish_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.move_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. move_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.offset_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. offset_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.pcs_per_mch_hr" Value="=null" Description="Syteline Schema Mapping to jrt_sch. pcs_per_mch_hr field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.queue_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. queue_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.run_mch_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. run_mch_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.sched_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. sched_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.setup_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. setup_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.whenrule" Value="=null" Description="Syteline Schema Mapping to jrt_sch. whenrule field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.text" Value="" Description="Syteline Schema Mapping to jobroute. text field." IsRequired="false" IsSystem="false" DataType="String" />
          </Parameters>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Drop Forks Material" ConditionExpression="=DropForks=True" Enabled="true">
          <RuleData>
            <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
            <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
            <Column Key="Operation No" ExpressionType="Expression" />
            <Column Key="Plant ID" ExpressionType="Expression" />
            <Column Key="Part Number" ExpressionType="Expression" />
            <Column Key="Quantity" ExpressionType="Expression" />
            <Column Key="Create New Line" DataType="Number" ExpressionType="Expression" />
            <Column Key="Price" DataType="Number" ExpressionType="Expression" />
            <Value>
              <Column Value="505" />
              <Column Value="=sys.partnumber" />
              <Column Value="=&quot;32&quot;" />
              <Column Value="" />
              <Column Value="=DropForks_sel" />
              <Column Value="1" />
              <Column Value="=1" />
              <Column Value="=DropForks_Price" />
            </Value>
          </RuleData>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Wireless Control Material" ConditionExpression="=ControlType=True" Enabled="true">
          <RuleData>
            <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
            <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
            <Column Key="Operation No" ExpressionType="Expression" />
            <Column Key="Plant ID" ExpressionType="Expression" />
            <Column Key="Part Number" ExpressionType="Expression" />
            <Column Key="Quantity" ExpressionType="Expression" />
            <Column Key="Create New Line" DataType="Number" ExpressionType="Expression" />
            <Column Key="Price" DataType="Number" ExpressionType="Expression" />
            <Value>
              <Column Value="500" />
              <Column Value="=sys.partnumber" />
              <Column Value="=&quot;32&quot;" />
              <Column Value="" />
              <Column Value="=ControlType_sel" />
              <Column Value="1" />
              <Column Value="=1" />
              <Column Value="=Wireless_Price" />
            </Value>
          </RuleData>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Variable Surcharge Spike" ConditionExpression="" Enabled="true">
          <RuleData>
            <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
            <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
            <Column Key="Operation No" ExpressionType="Expression" />
            <Column Key="Plant ID" ExpressionType="Expression" />
            <Column Key="Part Number" ExpressionType="Expression" />
            <Column Key="Quantity" ExpressionType="Expression" />
            <Column Key="Create New Line" DataType="Number" ExpressionType="Expression" />
            <Column Key="Price" DataType="Number" ExpressionType="Expression" />
            <Column Key="Message" ExpressionType="Expression" />
            <Column Key="Config String" ExpressionType="Expression" />
            <Value>
              <Column Value="400" />
              <Column Value="=sys.partnumber" />
              <Column Value="=&quot;30&quot;" />
              <Column Value="" />
              <Column Value="=&quot;vsbbs22&quot;" />
              <Column Value="1" />
              <Column Value="=1" />
              <Column Value="=100.00" />
              <Column Value="" />
              <Column Value="" />
            </Value>
          </RuleData>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Shipping parts" ConditionExpression="=Assembly_Comp=False" Enabled="false">
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Hydraulic Power and Wired Control Material" ConditionExpression="" Enabled="false">
            <RuleData>
              <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
              <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
              <Column Key="Operation No" ExpressionType="Expression" />
              <Column Key="Plant ID" ExpressionType="Expression" />
              <Column Key="Part Number" ExpressionType="Expression" />
              <Column Key="Quantity" ExpressionType="Expression" />
              <Value>
                <Column Value="480" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;32&quot;" />
                <Column Value="" />
                <Column Value="=HydPwrUnit_sel" />
                <Column Value="1" />
              </Value>
              <Value>
                <Column Value="490" />
                <Column Value="=sys.partnumber" />
                <Column Value="=&quot;32&quot;" />
                <Column Value="" />
                <Column Value="=Wired" />
                <Column Value="1" />
              </Value>
            </RuleData>
          </Rule>
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Customer Order Only" ConditionExpression="=CustomerOrder=True" Enabled="false" />
        </Rule>
      </Rule>
      <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Flat" ConditionExpression="=BedType=&quot;R&quot;" Enabled="true">
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Variable Surcharge Regular" ConditionExpression="" Enabled="true">
          <RuleData>
            <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
            <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
            <Column Key="Operation No" ExpressionType="Expression" />
            <Column Key="Plant ID" ExpressionType="Expression" />
            <Column Key="Part Number" ExpressionType="Expression" />
            <Column Key="Quantity" ExpressionType="Expression" />
            <Column Key="Create New Line" DataType="Number" ExpressionType="Expression" />
            <Column Key="Price" DataType="Number" ExpressionType="Expression" />
            <Column Key="Message" ExpressionType="Expression" />
            <Column Key="Config String" ExpressionType="Expression" />
            <Value>
              <Column Value="400" />
              <Column Value="=sys.partnumber" />
              <Column Value="=&quot;30&quot;" />
              <Column Value="" />
              <Column Value="=&quot;vsbbr22&quot;" />
              <Column Value="1" />
              <Column Value="=1" />
              <Column Value="=0.00" />
              <Column Value="" />
              <Column Value="" />
            </Value>
          </RuleData>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Material" Caption="Wireless Control Material" ConditionExpression="=ControlType=True" Enabled="false">
          <RuleData>
            <Column Key="Material ID" IsRequired="true" ExpressionType="Expression" />
            <Column Key="Parent Component or Material ID" ExpressionType="Expression" />
            <Column Key="Operation No" ExpressionType="Expression" />
            <Column Key="Plant ID" ExpressionType="Expression" />
            <Column Key="Part Number" ExpressionType="Expression" />
            <Column Key="Quantity" ExpressionType="Expression" />
            <Column Key="Create New Line" DataType="Number" ExpressionType="Expression" />
            <Column Key="Price" DataType="Number" ExpressionType="Expression" />
            <Value>
              <Column Value="530" />
              <Column Value="=sys.partnumber" />
              <Column Value="=&quot;33&quot;" />
              <Column Value="" />
              <Column Value="=ControlType_sel" />
              <Column Value="1" />
              <Column Value="=1" />
              <Column Value="=Wireless_Price" />
            </Value>
          </RuleData>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="MFG Operation" Caption="Operation 33 - Assembly Flat Beds" ConditionExpression="" Enabled="false">
          <Parameters>
            <Parameter Key="Component or Material ID" Value="=sys.partnumber" Description="Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="Operation No" Value="=&quot;33&quot;" Description="Required" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="Operation Name" Value="" Description="Operation Name or Work Center ID" IsRequired="false" IsSystem="true" DataType="String" />
            <Parameter Key="Operation Description" Value="" Description="Description of the Operation" IsRequired="false" IsSystem="true" DataType="String" />
            <Parameter Key="Work Center ID" Value="600" Description="Valid Work Center ID.  Parent Material or Component" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="Print Code" Value="I" Description="I for Internal, E for External" IsRequired="true" IsSystem="true" DataType="String" />
            <Parameter Key="jrt_sch.run_lbr_hrs" Value="=0" Description="Used for &quot;H&quot; Hours/Piece --- Enter Hours" IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.pcs_per_lbr_hr" Value="=0" Description="Used for &quot;P&quot; Piece/Hour --- Enter Pieces" IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.bflush_type" Value="" Description="Syteline Schema Mapping to jobroute. bflush_type field." IsRequired="false" IsSystem="false" DataType="String" />
            <Parameter Key="jobroute.cntrl_point" Value="=null" Description="Syteline Schema Mapping to jobroute. cntrl_point field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.efficiency" Value="=null" Description="Syteline Schema Mapping to jobroute. efficiency field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.fixovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. fixovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.fovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. fovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.run_basis_lbr" Value="H" Description="P = Pieces/Hour or H = Hours/Piece" IsRequired="false" IsSystem="false" DataType="String" />
            <Parameter Key="jobroute.run_basis_mch" Value="" Description="Syteline Schema Mapping to jobroute. run_basis_mch field." IsRequired="false" IsSystem="false" DataType="String" />
            <Parameter Key="jobroute.run_rate_lbr" Value="=null" Description="Syteline Schema Mapping to jobroute. run_rate_lbr field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.setup_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. setup_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.varovhd_rate" Value="=null" Description="Syteline Schema Mapping to jobroute. varovhd_rate field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.vovhd_rate_mch" Value="=null" Description="Syteline Schema Mapping to jobroute. vovhd_rate_mch field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.finish_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. finish_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.move_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. move_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.offset_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. offset_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.pcs_per_mch_hr" Value="=null" Description="Syteline Schema Mapping to jrt_sch. pcs_per_mch_hr field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.queue_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. queue_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.run_mch_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. run_mch_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.sched_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. sched_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.setup_hrs" Value="=null" Description="Syteline Schema Mapping to jrt_sch. setup_hrs field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jrt_sch.whenrule" Value="=null" Description="Syteline Schema Mapping to jrt_sch. whenrule field." IsRequired="false" IsSystem="false" DataType="Number" />
            <Parameter Key="jobroute.text" Value="" Description="Syteline Schema Mapping to jobroute. text field." IsRequired="false" IsSystem="false" DataType="String" />
          </Parameters>
        </Rule>
        <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Shipping parts" ConditionExpression="=Assembly_Comp=False" Enabled="false">
          <Rule Namespace="SYS" Ruleset="Butler_Manufacturing" ChildType="true" RuleTypeName="Condition" Caption="Customer Order Only" ConditionExpression="=CustomerOrder=True" Enabled="false" />
        </Rule>
      </Rule>
    </Rule>
  </RuleTree>
</IncludeRuleset>