# Truck Hydraulic System Configurator (SYS.700CPK) - Entity Relationship Diagram

## Overview

This document describes the data relationships and entity structure for the Truck Hydraulic System Configurator (SYS.700CPK), a general-purpose truck hydraulic system configuration system for PTO/pump kits with identifier `*700CPK`.

## ERD Core relationships

```mermaid
erDiagram
    VEHICLE ||--o{ CONFIGURATION : "has"
    CONFIGURATION ||--o{ CONFIG_LINE_ITEM : "contains"

    %% Components
    KIT ||--o{ CONFIG_LINE_ITEM : "included_in"
    PUMP ||--o{ CONFIG_LINE_ITEM : "included_in"
    AG_PRODUCT }o--o{ VALVE : "may_require"
    VALVE ||--o{ CONFIG_LINE_ITEM : "included_in"
```

## ERD Summary

Data Relationship Summary
Core Entities
TRUCK
Stores the vehicle's core attributes like make, year, axle type, and cab/axle configuration.

BUTLER_CONFIGURATION
Represents a truck-specific configuration.

One TRUCK → many BUTLER_CONFIGURATIONs

Includes flags like wireless_control, power_unit_required, and pricing/status fields.

Configuration Structure
BUTLER_CONFIG_LINE_ITEM
Represents individual parts/components added to a configuration.

One BUTLER_CONFIGURATION → many LINE_ITEMs

Stores component type, part number, quantity, and pricing.

Bed and Compatibility
Entity

Purpose

Key Relationships

BED

Defines available bed models and types.

Linked to CONFIG_LINE_ITEM

BED_MOUNT

Matches beds to truck specs (make/year/axle).

BED → BED_MOUNT

BUTLER_SKIRTED_MATRIX

Lists available skirt options for specific beds.

BED → SKIRTED_MATRIX

TOOLBOX_COMPATIBILITY

Maps which toolboxes fit specific beds/positions.

BED ↔ TOOLBOX

Components & Accessories
The following components are available to be added to a configuration via BUTLER_CONFIG_LINE_ITEM:

TOOLBOX (left/right/across-bed)

HYDRAULIC_POWER_UNIT

HEADACHE_RACK

WORK_LIGHTS

RECEIVER_HITCH

FIFTH_WHEEL_BALL

WIRE_HARNESS

BUTLER_ADAPTER

MOUNT_KIT

Toolboxes also have a many-to-many relationship with configurations to represent position placement.

Compatibility Matrices
Matrix Entity

Purpose

TRUCK_OPTIONS_MATRIX

Defines options allowed for truck specs.

BUTLER_MOUNT_KIT_MATRIX

Matches trucks to compatible mount kits.

BUTLER_ADAPTER_HARNESS_MATRIX

Determines suitable harnesses/adapters for trucks.

DEWEZE_BED_MOUNT_MATRIX

External matrix to find proper bed mounts for truck models.

Summary of Key Relationships
TRUCK → BUTLER_CONFIGURATION → CONFIG_LINE_ITEM

BED → BED_MOUNT, BED → TOOLBOX_COMPATIBILITY, BED → SKIRTED_MATRIX

BUTLER_CONFIGURATION ↔ TOOLBOX (many-to-many via position)

Compatibility matrices reference TRUCK to determine valid parts

## ERD Diagram

```mermaid
%% Truck Hydraulic System Configurator (SYS.700CPK) - Data Relationships
erDiagram
    %% Core Vehicle Information
    VEHICLE {
        string vehicle_id_PK
        string make
        string year
        string vehicle_type
        string engine
        string application
        boolean macfit
    }

    %% Configuration Entity
    CONFIGURATION {
        string config_id_PK
        string vehicle_id_FK
        string customer_code
        decimal first_price
        decimal total_price
        datetime created_date
        string status
    }

    %% Component Entities
    KIT {
        string kit_id_PK
        string kit_part_number
        string description
        decimal base_price
        string kit_group
    }

    PUMP {
        string pump_id_PK
        string pump_part_number
        string pump_size
        string pump_type
        decimal base_price
        string pump_group
    }

    CLUTCH {
        string clutch_id_PK
        string clutch_part_number
        string clutch_type
        decimal base_price
        string clutch_group
    }

    ADAPTER {
        string adapter_id_PK
        string adapter_part_number
        string adapter_type
        decimal base_price
        string adapter_group
    }

    %% AG-Specific Components (Agricultural Application)
    AG_PRODUCT {
        string ag_product_id_PK
        string product_type
        string product_series
        boolean valve_required
        string description
    }

    VALVE {
        string valve_id_PK
        string valve_part_number
        string valve_type
        decimal base_price
        boolean for_660_series
    }

    CONTROL {
        string control_id_PK
        string control_part_number
        string control_type
        decimal base_price
    }

    HOSE {
        string hose_id_PK
        string hose_part_number
        string hose_type
        decimal base_price
        string application_type
    }

    HARNESS {
        string harness_id_PK
        string harness_part_number
        string make_compatibility
        string year_compatibility
        string chassis_compatibility
        decimal base_price
    }

    RESERVOIR {
        string reservoir_id_PK
        string reservoir_part_number
        string capacity
        decimal base_price
        boolean for_660_series
    }

    %% Configuration Line Items (BOM)
    CONFIG_LINE_ITEM {
        string line_item_id_PK
        string config_id_FK
        string component_type
        string part_number
        integer quantity
        decimal unit_price
        decimal line_total
    }

    %% Matrix/Filter Tables
    VT_FILTER_MATRIX {
        string filter_id_PK
        string make
        string year
        string vehicle_type_result
    }

    ENGINE_GROUP_MATRIX {
        string engine_group_id_PK
        string make
        string vehicle_type
        string year
        string engine_groups
    }

    PUMP_GROUP_MATRIX {
        string pump_group_id_PK
        string engine
        string application_type
        string vehicle_type
        string year
        string pump_groups
    }

    KIT_GROUP_MATRIX {
        string kit_group_id_PK
        string engine
        string year
        string pump
        string vehicle_type
        string application
        string kit_groups
    }

    ADAPTER_GROUP_MATRIX {
        string adapter_group_id_PK
        string kit_substring
        string pump
        string ag_flag
        string adapter_groups
    }

    %% Relationships
    VEHICLE ||--o{ CONFIGURATION : "has"
    CONFIGURATION ||--o{ CONFIG_LINE_ITEM : "contains"

    %% Component relationships to configuration
    KIT ||--o{ CONFIG_LINE_ITEM : "included_in"
    PUMP ||--o{ CONFIG_LINE_ITEM : "included_in"
    CLUTCH ||--o{ CONFIG_LINE_ITEM : "included_in"
    ADAPTER ||--o{ CONFIG_LINE_ITEM : "included_in"
    VALVE ||--o{ CONFIG_LINE_ITEM : "included_in"
    CONTROL ||--o{ CONFIG_LINE_ITEM : "included_in"
    HOSE ||--o{ CONFIG_LINE_ITEM : "included_in"
    HARNESS ||--o{ CONFIG_LINE_ITEM : "included_in"
    RESERVOIR ||--o{ CONFIG_LINE_ITEM : "included_in"

    %% AG Product relationships
    AG_PRODUCT ||--o{ VALVE : "may_require"
    AG_PRODUCT ||--o{ CONTROL : "may_include"
    AG_PRODUCT ||--o{ HOSE : "may_include"
    AG_PRODUCT ||--o{ RESERVOIR : "may_include"

    %% Matrix relationships for filtering
    VEHICLE ||--o{ VT_FILTER_MATRIX : "filters_by"
    VEHICLE ||--o{ ENGINE_GROUP_MATRIX : "determines"
    VEHICLE ||--o{ PUMP_GROUP_MATRIX : "determines"
    VEHICLE ||--o{ KIT_GROUP_MATRIX : "determines"
    VEHICLE ||--o{ ADAPTER_GROUP_MATRIX : "determines"
```

## Entity Descriptions

### Core Entities

#### VEHICLE

Stores the fundamental truck specifications that drive the entire configuration process.

- **vehicle_id_PK**: Unique identifier for each vehicle configuration
- **make**: Vehicle manufacturer (Ford, GM, Dodge, Mack, etc.)
- **year**: 4-digit vehicle year
- **vehicle_type**: Type of vehicle (filtered by VT_Filter matrix)
- **engine**: Engine specification
- **application**: Application type (AG=Agriculture, COM=Commercial, WRK=Work, etc.)
- **macfit**: Special Mack compatibility flag for side port pump selection

#### CONFIGURATION

Main configuration record that ties together all selected components and pricing.

- **config_id_PK**: Unique configuration identifier
- **vehicle_id_FK**: Reference to the vehicle being configured
- **customer_code**: Customer-specific pricing code
- **first_price**: Base pricing before discounts/markups
- **total_price**: Final calculated price including all components
- **created_date**: Configuration creation timestamp
- **status**: Configuration status (draft, submitted, approved, etc.)

### Component Entities

#### KIT

Primary hydraulic kit components that form the base of each configuration.

- **kit_id_PK**: Unique kit identifier
- **kit_part_number**: Manufacturing part number (e.g., 700001, 700336)
- **description**: Human-readable kit description
- **base_price**: Base price from Pricing_Kit matrix
- **kit_group**: Group classification for filtering compatibility

#### PUMP

Hydraulic pump components with various sizes and types.

- **pump_id_PK**: Unique pump identifier
- **pump_part_number**: Manufacturing part number (e.g., 02-2909, 02-0500)
- **pump_size**: Pump capacity (5GL, 7GL, 9GLHP, etc.)
- **pump_type**: Pump type classification
- **base_price**: Base price from Pricing_Pump matrix
- **pump_group**: Group classification for filtering

#### CLUTCH

Clutch components for power transmission.

- **clutch_id_PK**: Unique clutch identifier
- **clutch_part_number**: Manufacturing part number
- **clutch_type**: Type of clutch (manual, automatic, etc.)
- **base_price**: Base price from Pricing_Clutch matrix
- **clutch_group**: Group classification with HD_Clutch exceptions

#### ADAPTER

Adapter components for connecting pumps to vehicle systems.

- **adapter_id_PK**: Unique adapter identifier
- **adapter_part_number**: Manufacturing part number (e.g., 03-1010, 03-1020)
- **adapter_type**: Adapter type classification
- **base_price**: Base price from Pricing_Adapter matrix
- **adapter_group**: Group classification from MultipleAdapterGroups matrix

### Agricultural (AG) Application Components

#### AG_PRODUCT

Specialized products for agricultural applications.

- **ag_product_id_PK**: Unique AG product identifier
- **product_type**: Type of AG product
- **product_series**: Product series (660, 800-900, etc.)
- **valve_required**: Whether valve is required for this product
- **description**: Product description

#### VALVE

Hydraulic valve components for AG applications.

- **valve_id_PK**: Unique valve identifier
- **valve_part_number**: Manufacturing part number (e.g., 05-7050, 05-7102)
- **valve_type**: Type of valve
- **base_price**: Base price from Pricing_Valve matrix
- **for_660_series**: Special flag for 660 series compatibility

#### CONTROL

Control system components for AG applications.

- **control_id_PK**: Unique control identifier
- **control_part_number**: Manufacturing part number (e.g., 07-7015, 37-7184)
- **control_type**: Type of control system
- **base_price**: Base price from Pricing_Control matrix

#### HOSE

Hydraulic hose components with various applications.

- **hose_id_PK**: Unique hose identifier
- **hose_part_number**: Manufacturing part number (e.g., 04-7050, 34-7075)
- **hose_type**: Type of hose (suction, pressure, auxiliary)
- **base_price**: Base price from Pricing_Hoses or Pricing_Hoses660 matrix
- **application_type**: Application-specific hose type

#### HARNESS

Wire harness components for electrical connections.

- **harness_id_PK**: Unique harness identifier
- **harness_part_number**: Manufacturing part number (e.g., 09-1020, 09-1016)
- **make_compatibility**: Compatible vehicle makes
- **year_compatibility**: Compatible vehicle years
- **chassis_compatibility**: Compatible chassis types
- **base_price**: Base price from Pricing_Harness matrix

#### RESERVOIR

Hydraulic reservoir components for 660 series products.

- **reservoir_id_PK**: Unique reservoir identifier
- **reservoir_part_number**: Manufacturing part number
- **capacity**: Reservoir capacity
- **base_price**: Base price from Pricing_Reservoir matrix
- **for_660_series**: Flag indicating 660 series compatibility

### Configuration Management

#### CONFIG_LINE_ITEM

Bill of Materials (BOM) line items that make up the final configuration.

- **line_item_id_PK**: Unique line item identifier
- **config_id_FK**: Reference to parent configuration
- **component_type**: Type of component (Kit, Pump, Clutch, etc.)
- **part_number**: Manufacturing part number
- **quantity**: Quantity of this component
- **unit_price**: Price per unit
- **line_total**: Extended line total (quantity × unit_price)

### Compatibility Matrix Tables

#### VT_FILTER_MATRIX

Determines available vehicle types based on make and year.

- **filter_id_PK**: Unique filter identifier
- **make**: Vehicle make
- **year**: Vehicle year
- **vehicle_type_result**: Resulting available vehicle types

#### ENGINE_GROUP_MATRIX

Determines available engine groups based on vehicle specifications.

- **engine_group_id_PK**: Unique engine group identifier
- **make**: Vehicle make
- **vehicle_type**: Vehicle type
- **year**: Vehicle year
- **engine_groups**: Comma-separated engine group codes

#### PUMP_GROUP_MATRIX

Determines available pump groups based on engine and application.

- **pump_group_id_PK**: Unique pump group identifier
- **engine**: Engine specification
- **application_type**: Application type
- **vehicle_type**: Vehicle type
- **year**: Vehicle year
- **pump_groups**: Comma-separated pump group codes

#### KIT_GROUP_MATRIX

Determines available kit groups based on multiple vehicle and component factors.

- **kit_group_id_PK**: Unique kit group identifier
- **engine**: Engine specification
- **year**: Vehicle year
- **pump**: Pump specification
- **vehicle_type**: Vehicle type
- **application**: Application type
- **kit_groups**: Dash-separated kit group codes

#### ADAPTER_GROUP_MATRIX

Determines available adapter groups based on kit and pump selections.

- **adapter_group_id_PK**: Unique adapter group identifier
- **kit_substring**: Substring of kit part number (skip first 4 characters)
- **pump**: Pump specification
- **ag_flag**: Agricultural application flag (B/O)
- **adapter_groups**: Comma-separated adapter group codes

## Key Relationships

### Primary Relationships

1. **VEHICLE → CONFIGURATION** (1:Many): Each vehicle can have multiple configurations
2. **CONFIGURATION → CONFIG_LINE_ITEM** (1:Many): Each configuration contains multiple line items
3. **Components → CONFIG_LINE_ITEM** (1:Many): Each component can appear in multiple configurations

### Agricultural Application Relationships

- **AG_PRODUCT → VALVE** (1:Many): AG products may require specific valves
- **AG_PRODUCT → CONTROL** (1:Many): AG products may include control systems
- **AG_PRODUCT → HOSE** (1:Many): AG products may include specialized hoses
- **AG_PRODUCT → RESERVOIR** (1:Many): AG products may include reservoirs

### Matrix Filtering Relationships

- **VEHICLE → Matrix Tables** (1:Many): Vehicle specifications drive compatibility filtering
- Matrix tables determine which components are compatible with specific vehicle configurations

## Business Rules

### Configuration Flow

1. User selects vehicle specifications (make, year, vehicle type, engine, application)
2. System uses matrix tables to filter available components
3. User selects compatible components (kit, pump, clutch, adapter)
4. For AG applications, additional components are selected (valve, control, hoses, etc.)
5. System generates BOM with pricing and creates configuration record

### Special Handling

- **Mack Vehicles**: Require special compatibility check (Macfit field)
- **AG Applications**: Include additional component selection workflow
- **660 Series Products**: Have special valve and reservoir handling
- **Ford/GM/Sprinter**: Default to vehicle type "3" for non-AG applications

### Pricing Integration

- Base prices come from component-specific pricing matrices
- Customer-specific pricing applied via customer codes
- Final pricing includes markup/discount calculations
- Integration with SyteLine ERP for manufacturing

## Database Integration

### Production Environment

- **Database**: HARPER_APP (SalesPortal, SYTELINE_PROD)
- **User Management**: `usr.GetCustCode`, `usr.GetFirstPrice`

### Development Environment

- **Database**: PILOTDEV_APP (PilotDev)
- **User Management**: `usr.GetCustCodeDev`

### Default Environment

- **Database**: PILOT_APP (other applications)

## Manufacturing Integration

The configurator integrates with SyteLine ERP for manufacturing by:

1. Generating BOM with all selected components
2. Creating operation setup records
3. Providing part numbers for manufacturing
4. Supporting customer-specific pricing and discounting

This ERD provides the foundation for a robust, scalable truck hydraulic system configuration platform that supports complex compatibility rules, pricing matrices, and manufacturing integration.
